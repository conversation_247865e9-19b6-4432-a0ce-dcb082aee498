name: Documentation Build and Deploy

on:
  push:
    branches: [ master, main ]
    paths:
      - 'docs/**'
      - '.github/workflows/docs.yml'
      - 'src/**'  # Rebuild docs when source code changes
  pull_request:
    branches: [ master, main ]
    paths:
      - 'docs/**'
      - '.github/workflows/docs.yml'

jobs:
  build-docs:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Full history for proper versioning
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          graphviz \
          plantuml \
          pandoc \
          texlive-latex-recommended \
          texlive-latex-extra \
          texlive-fonts-recommended
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r docs/requirements.txt
        pip install -e .  # Install the package itself for autodoc
    
    - name: Check documentation syntax
      run: |
        cd docs
        make check
    
    - name: Build HTML documentation
      run: |
        cd docs
        make html
    
    - name: Check external links
      run: |
        cd docs
        make linkcheck
      continue-on-error: true  # Don't fail on broken external links
    
    - name: Build PDF documentation
      run: |
        cd docs
        make pdf
      continue-on-error: true  # PDF build might fail on some systems
    
    - name: Upload documentation artifacts
      uses: actions/upload-artifact@v3
      with:
        name: documentation
        path: |
          docs/_build/html/
          docs/_build/latex/*.pdf
        retention-days: 30
    
    - name: Deploy to GitHub Pages
      if: github.ref == 'refs/heads/master' && github.event_name == 'push'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./docs/_build/html
        force_orphan: true
        user_name: 'github-actions[bot]'
        user_email: 'github-actions[bot]@users.noreply.github.com'
        commit_message: 'Deploy documentation: ${{ github.sha }}'

  test-docs:
    runs-on: ubuntu-latest
    needs: build-docs
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r docs/requirements.txt
        pip install -e .
    
    - name: Download documentation artifacts
      uses: actions/download-artifact@v3
      with:
        name: documentation
        path: docs/_build/
    
    - name: Test documentation accessibility
      run: |
        # Install accessibility testing tools
        npm install -g @axe-core/cli
        
        # Start a simple HTTP server
        cd docs/_build/html
        python -m http.server 8080 &
        SERVER_PID=$!
        
        # Wait for server to start
        sleep 5
        
        # Run accessibility tests
        axe http://localhost:8080 --exit
        
        # Clean up
        kill $SERVER_PID
      continue-on-error: true
    
    - name: Test documentation performance
      run: |
        # Install Lighthouse CLI
        npm install -g lighthouse
        
        # Start HTTP server
        cd docs/_build/html
        python -m http.server 8080 &
        SERVER_PID=$!
        
        # Wait for server to start
        sleep 5
        
        # Run Lighthouse audit
        lighthouse http://localhost:8080 \
          --output=json \
          --output-path=lighthouse-report.json \
          --chrome-flags="--headless --no-sandbox"
        
        # Clean up
        kill $SERVER_PID
        
        # Upload Lighthouse report
        echo "Lighthouse report generated"
      continue-on-error: true

  validate-links:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r docs/requirements.txt
        pip install -e .
    
    - name: Validate internal links
      run: |
        cd docs
        make linkcheck
    
    - name: Check for broken references
      run: |
        cd docs
        # Build with warnings as errors to catch broken references
        sphinx-build -W -b html . _build/html

  spell-check:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r docs/requirements.txt
        pip install sphinxcontrib-spelling pyenchant
        sudo apt-get install -y enchant-2
    
    - name: Run spell check
      run: |
        cd docs
        make spelling
      continue-on-error: true

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: 'docs/'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  notify-status:
    runs-on: ubuntu-latest
    needs: [build-docs, test-docs, validate-links]
    if: always()
    
    steps:
    - name: Notify documentation status
      uses: 8398a7/action-slack@v3
      if: env.SLACK_WEBHOOK_URL != ''
      with:
        status: ${{ job.status }}
        text: |
          Documentation build status: ${{ job.status }}
          Repository: ${{ github.repository }}
          Branch: ${{ github.ref }}
          Commit: ${{ github.sha }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
