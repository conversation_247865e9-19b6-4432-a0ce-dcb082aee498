#!/usr/bin/env python3
"""
CVE Feed Service Infrastructure Testing
Runs comprehensive tests against actual Docker infrastructure using Traefik endpoints
"""

import asyncio
import aiohttp
import time
import json
import subprocess
import sys
from typing import Dict, List, Optional, Tuple
from test_config import (
    TestConfig, 
    SERVICE_DOMAINS, 
    HEALTH_CHECK_ENDPOINTS,
    get_service_url,
    get_api_endpoint,
    get_health_check_url,
    TEST_USERS
)

class InfrastructureTestRunner:
    """Test runner for infrastructure testing against Traefik endpoints"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.auth_token: Optional[str] = None
        self.test_results: Dict[str, List[Dict]] = {
            "health_checks": [],
            "api_tests": [],
            "frontend_tests": [],
            "integration_tests": [],
            "performance_tests": []
        }
        
    async def __aenter__(self):
        """Async context manager entry"""
        connector = aiohttp.TCPConnector(
            ssl=False,  # For local development with self-signed certs
            limit=100,
            limit_per_host=30
        )
        timeout = aiohttp.ClientTimeout(total=TestConfig.REQUEST_TIMEOUT)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={"User-Agent": "CVE-Feed-Test-Runner/1.0"}
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()

    def print_header(self, title: str, color: str = "36"):
        """Print colored header"""
        print(f"\n\033[{color}m{'='*60}\033[0m")
        print(f"\033[{color}m{title.center(60)}\033[0m")
        print(f"\033[{color}m{'='*60}\033[0m")

    def print_status(self, message: str, status: str = "info"):
        """Print colored status message"""
        colors = {
            "success": "32",  # Green
            "error": "31",    # Red
            "warning": "33",  # Yellow
            "info": "34",     # Blue
            "highlight": "35" # Purple
        }
        color = colors.get(status, "0")
        icons = {
            "success": "✅",
            "error": "❌", 
            "warning": "⚠️",
            "info": "ℹ️",
            "highlight": "🔍"
        }
        icon = icons.get(status, "•")
        print(f"\033[{color}m{icon} {message}\033[0m")

    async def wait_for_services(self, max_wait: int = 120) -> bool:
        """Wait for all services to be healthy"""
        self.print_header("🏥 HEALTH CHECK - Waiting for Services", "33")
        
        start_time = time.time()
        services_ready = set()
        
        while time.time() - start_time < max_wait:
            for service_name, health_url in HEALTH_CHECK_ENDPOINTS.items():
                if service_name in services_ready:
                    continue
                    
                try:
                    async with self.session.get(health_url) as response:
                        if response.status == 200:
                            services_ready.add(service_name)
                            self.print_status(f"{service_name.upper()} service is healthy", "success")
                            self.test_results["health_checks"].append({
                                "service": service_name,
                                "status": "healthy",
                                "url": health_url,
                                "response_time": time.time() - start_time
                            })
                        else:
                            self.print_status(f"{service_name.upper()} service not ready (HTTP {response.status})", "warning")
                except Exception as e:
                    self.print_status(f"{service_name.upper()} service not ready: {str(e)[:50]}...", "warning")
            
            if len(services_ready) == len(HEALTH_CHECK_ENDPOINTS):
                self.print_status("All services are healthy!", "success")
                return True
                
            await asyncio.sleep(5)
        
        missing_services = set(HEALTH_CHECK_ENDPOINTS.keys()) - services_ready
        self.print_status(f"Timeout waiting for services: {', '.join(missing_services)}", "error")
        return False

    async def authenticate(self) -> bool:
        """Authenticate with the API to get access token"""
        self.print_header("🔐 AUTHENTICATION", "35")
        
        login_url = get_api_endpoint("auth/login")
        login_data = {
            "username": TEST_USERS["analyst"]["username"],
            "password": TEST_USERS["analyst"]["password"]
        }
        
        try:
            async with self.session.post(login_url, json=login_data) as response:
                if response.status == 200:
                    data = await response.json()
                    self.auth_token = data.get("access_token")
                    if self.auth_token:
                        self.print_status("Authentication successful", "success")
                        return True
                    else:
                        self.print_status("No access token in response", "error")
                        return False
                else:
                    error_text = await response.text()
                    self.print_status(f"Authentication failed: HTTP {response.status} - {error_text[:100]}", "error")
                    return False
        except Exception as e:
            self.print_status(f"Authentication error: {str(e)}", "error")
            return False

    async def test_api_endpoints(self) -> Dict[str, int]:
        """Test core API endpoints"""
        self.print_header("🔌 API ENDPOINT TESTING", "34")
        
        if not self.auth_token:
            self.print_status("No auth token available, skipping API tests", "error")
            return {"passed": 0, "failed": 1}
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        endpoints_to_test = [
            ("GET", "cves", "List CVEs"),
            ("GET", "applications", "List Applications"), 
            ("GET", "auth/me", "Get Current User"),
            ("GET", "dashboard/stats", "Dashboard Statistics")
        ]
        
        passed = 0
        failed = 0
        
        for method, endpoint, description in endpoints_to_test:
            url = get_api_endpoint(endpoint)
            try:
                start_time = time.time()
                async with self.session.request(method, url, headers=headers) as response:
                    response_time = time.time() - start_time
                    
                    if response.status in [200, 201]:
                        self.print_status(f"{description}: HTTP {response.status} ({response_time:.2f}s)", "success")
                        passed += 1
                        
                        # Try to parse JSON response
                        try:
                            data = await response.json()
                            self.test_results["api_tests"].append({
                                "endpoint": endpoint,
                                "method": method,
                                "status": "passed",
                                "response_time": response_time,
                                "response_size": len(str(data))
                            })
                        except:
                            pass
                    else:
                        error_text = await response.text()
                        self.print_status(f"{description}: HTTP {response.status} - {error_text[:50]}", "error")
                        failed += 1
                        
            except Exception as e:
                self.print_status(f"{description}: Error - {str(e)[:50]}", "error")
                failed += 1
        
        return {"passed": passed, "failed": failed}

    async def test_frontend_accessibility(self) -> Dict[str, int]:
        """Test frontend accessibility and basic functionality"""
        self.print_header("🌐 FRONTEND TESTING", "36")
        
        frontend_tests = [
            (SERVICE_DOMAINS["frontend"], "Main Application"),
            (SERVICE_DOMAINS["admin"], "Admin Dashboard"),
            (SERVICE_DOMAINS["docs"], "Documentation Site")
        ]
        
        passed = 0
        failed = 0
        
        for url, description in frontend_tests:
            try:
                start_time = time.time()
                async with self.session.get(url) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        content = await response.text()
                        
                        # Basic checks
                        has_title = "<title>" in content
                        has_meta_viewport = "viewport" in content
                        has_lang_attr = 'lang=' in content
                        
                        checks_passed = sum([has_title, has_meta_viewport, has_lang_attr])
                        
                        if checks_passed >= 2:
                            self.print_status(f"{description}: Accessible ({checks_passed}/3 checks, {response_time:.2f}s)", "success")
                            passed += 1
                        else:
                            self.print_status(f"{description}: Accessibility issues ({checks_passed}/3 checks)", "warning")
                            failed += 1
                            
                        self.test_results["frontend_tests"].append({
                            "url": url,
                            "description": description,
                            "status": "passed" if checks_passed >= 2 else "failed",
                            "response_time": response_time,
                            "accessibility_score": f"{checks_passed}/3"
                        })
                    else:
                        self.print_status(f"{description}: HTTP {response.status}", "error")
                        failed += 1
                        
            except Exception as e:
                self.print_status(f"{description}: Error - {str(e)[:50]}", "error")
                failed += 1
        
        return {"passed": passed, "failed": failed}

    async def test_integration_workflows(self) -> Dict[str, int]:
        """Test integration workflows"""
        self.print_header("🔄 INTEGRATION TESTING", "35")
        
        if not self.auth_token:
            self.print_status("No auth token available, skipping integration tests", "error")
            return {"passed": 0, "failed": 1}
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        passed = 0
        failed = 0
        
        # Test 1: Create and retrieve application
        try:
            app_data = {
                "name": f"Test App {int(time.time())}",
                "description": "Integration test application",
                "version": "1.0.0",
                "environment": "development"
            }
            
            # Create application
            create_url = get_api_endpoint("applications")
            async with self.session.post(create_url, json=app_data, headers=headers) as response:
                if response.status == 201:
                    created_app = await response.json()
                    app_id = created_app.get("id")
                    
                    # Retrieve application
                    get_url = get_api_endpoint(f"applications/{app_id}")
                    async with self.session.get(get_url, headers=headers) as get_response:
                        if get_response.status == 200:
                            retrieved_app = await get_response.json()
                            if retrieved_app.get("name") == app_data["name"]:
                                self.print_status("Application CRUD workflow: Success", "success")
                                passed += 1
                            else:
                                self.print_status("Application CRUD workflow: Data mismatch", "error")
                                failed += 1
                        else:
                            self.print_status("Application CRUD workflow: Retrieval failed", "error")
                            failed += 1
                else:
                    self.print_status("Application CRUD workflow: Creation failed", "error")
                    failed += 1
        except Exception as e:
            self.print_status(f"Application CRUD workflow: Error - {str(e)[:50]}", "error")
            failed += 1
        
        # Test 2: CVE filtering and search
        try:
            search_url = get_api_endpoint("cves?severity=HIGH&limit=5")
            async with self.session.get(search_url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    if "cves" in data:
                        self.print_status("CVE filtering workflow: Success", "success")
                        passed += 1
                    else:
                        self.print_status("CVE filtering workflow: Invalid response format", "error")
                        failed += 1
                else:
                    self.print_status("CVE filtering workflow: Request failed", "error")
                    failed += 1
        except Exception as e:
            self.print_status(f"CVE filtering workflow: Error - {str(e)[:50]}", "error")
            failed += 1
        
        return {"passed": passed, "failed": failed}

    async def test_performance_metrics(self) -> Dict[str, int]:
        """Test performance metrics"""
        self.print_header("⚡ PERFORMANCE TESTING", "33")
        
        if not self.auth_token:
            self.print_status("No auth token available, skipping performance tests", "error")
            return {"passed": 0, "failed": 1}
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        passed = 0
        failed = 0
        
        # Test API response times
        endpoints = [
            ("cves", "CVE List"),
            ("applications", "Application List"),
            ("dashboard/stats", "Dashboard Stats")
        ]
        
        for endpoint, description in endpoints:
            url = get_api_endpoint(endpoint)
            response_times = []
            
            # Make 5 requests to get average response time
            for i in range(5):
                try:
                    start_time = time.time()
                    async with self.session.get(url, headers=headers) as response:
                        response_time = time.time() - start_time
                        response_times.append(response_time)
                        
                        if response.status != 200:
                            break
                except:
                    break
            
            if response_times:
                avg_time = sum(response_times) / len(response_times)
                max_time = max(response_times)
                
                if avg_time < 2.0:  # Less than 2 seconds average
                    self.print_status(f"{description}: Avg {avg_time:.2f}s, Max {max_time:.2f}s", "success")
                    passed += 1
                else:
                    self.print_status(f"{description}: Slow response - Avg {avg_time:.2f}s", "warning")
                    failed += 1
                    
                self.test_results["performance_tests"].append({
                    "endpoint": endpoint,
                    "avg_response_time": avg_time,
                    "max_response_time": max_time,
                    "status": "passed" if avg_time < 2.0 else "failed"
                })
            else:
                self.print_status(f"{description}: No successful requests", "error")
                failed += 1
        
        return {"passed": passed, "failed": failed}

    def generate_test_report(self, results: Dict[str, Dict[str, int]]):
        """Generate comprehensive test report"""
        self.print_header("📊 TEST RESULTS SUMMARY", "32")
        
        total_passed = sum(r["passed"] for r in results.values())
        total_failed = sum(r["failed"] for r in results.values())
        total_tests = total_passed + total_failed
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n📈 Overall Results:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {total_passed}")
        print(f"   Failed: {total_failed}")
        print(f"   Success Rate: {success_rate:.1f}%")
        
        print(f"\n📋 Category Breakdown:")
        for category, result in results.items():
            category_total = result["passed"] + result["failed"]
            category_rate = (result["passed"] / category_total * 100) if category_total > 0 else 0
            status_icon = "✅" if result["failed"] == 0 else "⚠️" if result["passed"] > result["failed"] else "❌"
            print(f"   {status_icon} {category.replace('_', ' ').title()}: {result['passed']}/{category_total} ({category_rate:.1f}%)")
        
        # Save detailed results to file
        with open("test-results.json", "w") as f:
            json.dump({
                "summary": {
                    "total_tests": total_tests,
                    "passed": total_passed,
                    "failed": total_failed,
                    "success_rate": success_rate
                },
                "categories": results,
                "detailed_results": self.test_results,
                "timestamp": time.time()
            }, f, indent=2)
        
        self.print_status("Detailed results saved to test-results.json", "info")
        
        return success_rate >= 80  # Consider 80%+ success rate as passing

async def main():
    """Main test execution"""
    print("\033[36m" + "="*80 + "\033[0m")
    print("\033[36m" + "CVE FEED SERVICE INFRASTRUCTURE TESTING".center(80) + "\033[0m")
    print("\033[36m" + "Testing against Traefik serviceURLmanager endpoints".center(80) + "\033[0m")
    print("\033[36m" + "="*80 + "\033[0m")
    
    async with InfrastructureTestRunner() as runner:
        # Wait for services to be ready
        if not await runner.wait_for_services():
            print("\n❌ Services not ready, aborting tests")
            return False
        
        # Authenticate
        if not await runner.authenticate():
            print("\n❌ Authentication failed, aborting tests")
            return False
        
        # Run test suites
        results = {}
        
        results["health_checks"] = {"passed": len(runner.test_results["health_checks"]), "failed": 0}
        results["api_tests"] = await runner.test_api_endpoints()
        results["frontend_tests"] = await runner.test_frontend_accessibility()
        results["integration_tests"] = await runner.test_integration_workflows()
        results["performance_tests"] = await runner.test_performance_metrics()
        
        # Generate report
        success = runner.generate_test_report(results)
        
        if success:
            print("\n🎉 Infrastructure tests completed successfully!")
            return True
        else:
            print("\n⚠️ Some infrastructure tests failed. Check the results above.")
            return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
