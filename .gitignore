# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
#pdm.lock
#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it
#   in version control.
#   https://pdm.fming.dev/#use-with-ide
.pdm.toml

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be added to the global gitignore or merged into this project gitignore.  For a PyCharm
#  project, it is recommended to include the following files in version control:
#  *.iml
#  modules.xml
#  .idea/misc.xml
#  .idea/modules.xml
#  .idea/runConfigurations.xml
#  .idea/shelf
#  .idea/workspace.xml
#  .idea/tasks.xml
#  .idea/gradle.xml
#  .idea/assetWizardSettings.xml
#  .idea/dictionaries
#  .idea/libraries
# Android studio 3.1+ serialized cache file
.idea/caches/build_file_checksums.ser

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Local development
.env.local
.env.development
.env.test
.env.production

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Node.js / Frontend
frontend/node_modules/
frontend/.next/
frontend/.nuxt/
frontend/dist/
frontend/build/
frontend/.cache/
frontend/.parcel-cache/
frontend/.vite/
frontend/coverage/
frontend/.nyc_output/
frontend/.eslintcache
frontend/.stylelintcache
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*
frontend/lerna-debug.log*
frontend/.pnpm-debug.log*

# Runtime data
frontend/pids
frontend/*.pid
frontend/*.seed
frontend/*.pid.lock

# Coverage directory used by tools like istanbul
frontend/coverage/
frontend/*.lcov

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
frontend/*.tsbuildinfo

# Optional npm cache directory
frontend/.npm

# Optional eslint cache
frontend/.eslintcache

# Optional stylelint cache
frontend/.stylelintcache

# Microbundle cache
frontend/.rpt2_cache/
frontend/.rts2_cache_cjs/
frontend/.rts2_cache_es/
frontend/.rts2_cache_umd/

# Optional REPL history
frontend/.node_repl_history

# Output of 'npm pack'
frontend/*.tgz

# Yarn Integrity file
frontend/.yarn-integrity

# dotenv environment variables file
frontend/.env
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

# Stores VSCode versions used for testing VSCode extensions
frontend/.vscode-test

# yarn v2
frontend/.yarn/cache
frontend/.yarn/unplugged
frontend/.yarn/build-state.yml
frontend/.yarn/install-state.gz
frontend/.pnp.*
