# CVE Feed Service Docker Management Makefile

# Colors for output
RED=\033[0;31m
GREEN=\033[0;32m
YELLOW=\033[1;33m
BLUE=\033[0;34m
PURPLE=\033[0;35m
CYAN=\033[0;36m
NC=\033[0m # No Color

# Default environment
ENV ?= development

# Docker compose files
COMPOSE_FILE = docker-compose.yml
ifeq ($(ENV),development)
	COMPOSE_FILE += -f docker-compose.override.yml
endif

# Project name
PROJECT_NAME = cve-feed-service

.PHONY: help build up down restart logs clean test lint format setup-dev setup-prod backup restore

# Default target
.DEFAULT_GOAL := help

help: ## Show this help message
	@echo "$(CYAN)CVE Feed Service Docker Management$(NC)"
	@echo "$(YELLOW)Available commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# =============================================================================
# DEVELOPMENT COMMANDS
# =============================================================================

setup-dev: ## Setup development environment
	@echo "$(BLUE)Setting up development environment...$(NC)"
	@cp .env.example .env
	@echo "$(YELLOW)Please edit .env file with your configuration$(NC)"
	@echo "$(GREEN)Development environment setup complete!$(NC)"

build: ## Build all Docker images
	@echo "$(BLUE)Building Docker images...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) build
	@echo "$(GREEN)Build complete!$(NC)"

build-no-cache: ## Build all Docker images without cache
	@echo "$(BLUE)Building Docker images without cache...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) build --no-cache
	@echo "$(GREEN)Build complete!$(NC)"

up: ## Start all services
	@echo "$(BLUE)Starting CVE Feed Service...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) up -d
	@echo "$(GREEN)Services started!$(NC)"
	@echo "$(YELLOW)Available services:$(NC)"
	@echo "  $(CYAN)API:$(NC)         https://api.feedme.localhost"
	@echo "  $(CYAN)Frontend:$(NC)    https://app.feedme.localhost"
	@echo "  $(CYAN)Admin:$(NC)       https://admin.feedme.localhost"
	@echo "  $(CYAN)Docs:$(NC)        https://docs.feedme.localhost"
	@echo "  $(CYAN)Metrics:$(NC)     https://metrics.feedme.localhost"
	@echo "  $(CYAN)Dashboard:$(NC)   https://dashboard.feedme.localhost"
ifeq ($(ENV),development)
	@echo "  $(CYAN)PgAdmin:$(NC)     https://pgadmin.feedme.localhost"
	@echo "  $(CYAN)Redis UI:$(NC)    https://redis.feedme.localhost"
endif

up-build: ## Build and start all services
	@echo "$(BLUE)Building and starting CVE Feed Service...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) up -d --build
	@echo "$(GREEN)Services started!$(NC)"

down: ## Stop all services
	@echo "$(BLUE)Stopping CVE Feed Service...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) down
	@echo "$(GREEN)Services stopped!$(NC)"

restart: ## Restart all services
	@echo "$(BLUE)Restarting CVE Feed Service...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) restart
	@echo "$(GREEN)Services restarted!$(NC)"

# =============================================================================
# INDIVIDUAL SERVICE COMMANDS
# =============================================================================

api: ## Start only the API service
	@docker-compose -f $(COMPOSE_FILE) up -d postgres redis api

frontend: ## Start only the frontend service
	@docker-compose -f $(COMPOSE_FILE) up -d api frontend

admin: ## Start only the admin service
	@docker-compose -f $(COMPOSE_FILE) up -d api admin

docs: ## Start only the documentation service
	@docker-compose -f $(COMPOSE_FILE) up -d docs

worker: ## Start only the worker service
	@docker-compose -f $(COMPOSE_FILE) up -d postgres redis worker

monitoring: ## Start only monitoring services
	@docker-compose -f $(COMPOSE_FILE) up -d prometheus grafana

# =============================================================================
# LOGS AND DEBUGGING
# =============================================================================

logs: ## Show logs for all services
	@docker-compose -f $(COMPOSE_FILE) logs -f

logs-api: ## Show logs for API service
	@docker-compose -f $(COMPOSE_FILE) logs -f api

logs-frontend: ## Show logs for frontend service
	@docker-compose -f $(COMPOSE_FILE) logs -f frontend

logs-worker: ## Show logs for worker service
	@docker-compose -f $(COMPOSE_FILE) logs -f worker

logs-db: ## Show logs for database service
	@docker-compose -f $(COMPOSE_FILE) logs -f postgres

status: ## Show status of all services
	@docker-compose -f $(COMPOSE_FILE) ps

# =============================================================================
# DATABASE COMMANDS
# =============================================================================

db-shell: ## Connect to database shell
	@docker-compose -f $(COMPOSE_FILE) exec postgres psql -U cve_user -d cve_feed_db

db-migrate: ## Run database migrations
	@docker-compose -f $(COMPOSE_FILE) exec api python -m alembic upgrade head

db-reset: ## Reset database (WARNING: This will delete all data!)
	@echo "$(RED)WARNING: This will delete all database data!$(NC)"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ]
	@docker-compose -f $(COMPOSE_FILE) down -v
	@docker volume rm $(PROJECT_NAME)_postgres_data || true
	@docker-compose -f $(COMPOSE_FILE) up -d postgres
	@sleep 10
	@docker-compose -f $(COMPOSE_FILE) exec api python -m alembic upgrade head

# =============================================================================
# TESTING COMMANDS
# =============================================================================

test: ## Run all tests
	@echo "$(BLUE)Running tests...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) exec api python -m pytest tests/ -v
	@echo "$(GREEN)Tests complete!$(NC)"

test-api: ## Run API tests only
	@docker-compose -f $(COMPOSE_FILE) exec api python -m pytest tests/api/ -v

test-unit: ## Run unit tests only
	@docker-compose -f $(COMPOSE_FILE) exec api python -m pytest tests/unit/ -v

test-integration: ## Run integration tests only
	@docker-compose -f $(COMPOSE_FILE) exec api python -m pytest tests/integration/ -v

test-coverage: ## Run tests with coverage report
	@docker-compose -f $(COMPOSE_FILE) exec api python -m pytest tests/ --cov=src --cov-report=html --cov-report=term

# =============================================================================
# MAINTENANCE COMMANDS
# =============================================================================

clean: ## Clean up Docker resources
	@echo "$(BLUE)Cleaning up Docker resources...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) down -v --remove-orphans
	@docker system prune -f
	@docker volume prune -f
	@echo "$(GREEN)Cleanup complete!$(NC)"

clean-all: ## Clean up everything including images
	@echo "$(RED)WARNING: This will remove all Docker images and volumes!$(NC)"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ]
	@docker-compose -f $(COMPOSE_FILE) down -v --remove-orphans --rmi all
	@docker system prune -af
	@docker volume prune -f

backup: ## Backup database
	@echo "$(BLUE)Creating database backup...$(NC)"
	@mkdir -p backups
	@docker-compose -f $(COMPOSE_FILE) exec postgres pg_dump -U cve_user cve_feed_db > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)Backup created in backups/ directory$(NC)"

restore: ## Restore database from backup (specify BACKUP_FILE=filename)
	@echo "$(BLUE)Restoring database from backup...$(NC)"
	@test -n "$(BACKUP_FILE)" || (echo "$(RED)Please specify BACKUP_FILE=filename$(NC)" && exit 1)
	@docker-compose -f $(COMPOSE_FILE) exec -T postgres psql -U cve_user -d cve_feed_db < $(BACKUP_FILE)
	@echo "$(GREEN)Database restored!$(NC)"

# =============================================================================
# PRODUCTION COMMANDS
# =============================================================================

setup-prod: ## Setup production environment
	@echo "$(BLUE)Setting up production environment...$(NC)"
	@cp .env.example .env.prod
	@echo "$(YELLOW)Please edit .env.prod file with your production configuration$(NC)"
	@echo "$(RED)Make sure to change all default passwords!$(NC)"
	@echo "$(GREEN)Production environment setup complete!$(NC)"

prod-up: ## Start production services
	@echo "$(BLUE)Starting production CVE Feed Service...$(NC)"
	@ENV=production docker-compose -f docker-compose.yml up -d
	@echo "$(GREEN)Production services started!$(NC)"

prod-down: ## Stop production services
	@echo "$(BLUE)Stopping production CVE Feed Service...$(NC)"
	@ENV=production docker-compose -f docker-compose.yml down
	@echo "$(GREEN)Production services stopped!$(NC)"

# =============================================================================
# UTILITY COMMANDS
# =============================================================================

shell-api: ## Open shell in API container
	@docker-compose -f $(COMPOSE_FILE) exec api bash

shell-frontend: ## Open shell in frontend container
	@docker-compose -f $(COMPOSE_FILE) exec frontend sh

shell-worker: ## Open shell in worker container
	@docker-compose -f $(COMPOSE_FILE) exec worker bash

update: ## Update all Docker images
	@echo "$(BLUE)Updating Docker images...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) pull
	@echo "$(GREEN)Images updated!$(NC)"

health: ## Check health of all services
	@echo "$(BLUE)Checking service health...$(NC)"
	@docker-compose -f $(COMPOSE_FILE) ps
	@echo "$(YELLOW)API Health:$(NC)"
	@curl -s https://api.feedme.localhost/health || echo "$(RED)API not responding$(NC)"
	@echo "$(YELLOW)Frontend Health:$(NC)"
	@curl -s https://app.feedme.localhost/health || echo "$(RED)Frontend not responding$(NC)"
