# CVE Feed Service - Development Environment

## 🎉 **READY TO USE!**

Your CVE Feed Service development environment is **fully operational** and ready for development!

## 🚀 **Quick Start**

### Services Running
- **Backend API**: http://localhost:8001
- **Frontend UI**: http://localhost:3000  
- **API Documentation**: http://localhost:8001/api/v1/docs

### Test Credentials
- **Username**: `<EMAIL>`
- **Password**: `password123`
- **Role**: `security_analyst`

## 📊 **Current System State**

### Database (SQLite)
- **File**: `dev_database.db` (331KB)
- **CVEs**: 4 sample vulnerabilities (CRITICAL, HIGH, MEDIUM, LOW)
- **Applications**: 1 ("Web Portal" v2.1.0)
- **Components**: 3 (nginx, nodejs, express)
- **CPE Mappings**: 3 (linked to components)

### Sample Data
- **CVE-2024-0001**: Critical nginx RCE vulnerability
- **CVE-2024-0002**: High Node.js prototype pollution
- **CVE-2024-0003**: Medium Express.js XSS vulnerability  
- **CVE-2023-9999**: Low nginx information disclosure

## 🛠️ **Development Tools**

### 1. Development Dashboard
```bash
python scripts/dev_dashboard.py
```
Interactive dashboard with:
- Real-time service status
- System statistics
- Quick actions (open browser, run tests, etc.)
- Useful commands reference

### 2. System Testing
```bash
python scripts/test_full_system.py
```
Comprehensive test covering:
- Authentication & authorization
- CVE data management
- Application & component management
- CVE feed generation
- API documentation

### 3. Sample Data Management
```bash
python scripts/seed_sample_data.py
```
Seeds database with realistic CVE data and CPE mappings.

## 🔧 **Manual Commands**

### Start Services
```bash
# Backend (if not running)
nix-shell --run 'uvicorn src.cve_feed_service.main:app --reload --host 0.0.0.0 --port 8001'

# Frontend (if not running)  
cd frontend && nix-shell -p nodejs_20 --run 'npm run dev'
```

### Database Operations
```bash
# View tables
sqlite3 dev_database.db '.tables'

# Count records
sqlite3 dev_database.db 'SELECT COUNT(*) FROM cves;'
sqlite3 dev_database.db 'SELECT COUNT(*) FROM applications;'
sqlite3 dev_database.db 'SELECT COUNT(*) FROM components;'

# View sample data
sqlite3 dev_database.db 'SELECT cve_id, cvss_v3_severity FROM cves;'
```

## 📋 **API Endpoints Verified**

### Authentication
- `POST /api/v1/auth/login` - User login
- `GET /api/v1/auth/me` - User profile

### CVE Management  
- `GET /api/v1/cves/` - List all CVEs
- `GET /api/v1/cves/{cve_id}` - Get specific CVE
- `GET /api/v1/cves/feed` - CVE feed (general)
- `GET /api/v1/cves/feed?application_id={id}` - Application-specific feed

### Application Management
- `GET /api/v1/applications/` - List applications
- `POST /api/v1/applications/` - Create application
- `GET /api/v1/applications/{id}` - Get application details

### Component Management
- `POST /api/v1/components/{app_id}/components` - Create component
- `GET /api/v1/components/{app_id}/components` - List components

### System
- `GET /health` - Health check
- `GET /api/v1/docs` - API documentation

## 🎯 **Next Development Steps**

### 1. CVE-CPE Matching Logic
The infrastructure is ready, but CVE-to-CPE matching needs implementation:
- Implement version range matching
- Add vulnerability applicability logic
- Test with real CVE data

### 2. Frontend Development
- Build React components for CVE visualization
- Implement application management UI
- Add real-time updates

### 3. Data Integration
- Implement NVD API integration
- Add automated CVE data ingestion
- Implement real-time feeds

### 4. Advanced Features
- Add filtering and search
- Implement notifications
- Add export capabilities

## 🔍 **Troubleshooting**

### Services Not Responding
```bash
# Check if ports are in use
netstat -tlnp | grep :8001
netstat -tlnp | grep :3000

# Restart services
pkill -f uvicorn
pkill -f "npm run dev"
```

### Database Issues
```bash
# Check database file
ls -la dev_database.db

# Backup and recreate if needed
cp dev_database.db dev_database.db.backup
rm dev_database.db
# Restart backend to recreate tables
```

### Authentication Issues
```bash
# Test login directly
curl -X POST "http://localhost:8001/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"<EMAIL>","password":"password123"}'
```

## 📚 **Documentation**

- **API Docs**: http://localhost:8001/api/v1/docs (Swagger UI)
- **OpenAPI Spec**: http://localhost:8001/api/v1/openapi.json
- **Health Check**: http://localhost:8001/health

## 🎉 **Success!**

Your CVE Feed Service is now a fully functional development environment with:
- ✅ Working backend API with authentication
- ✅ React frontend with hot reload
- ✅ SQLite database with sample data
- ✅ Comprehensive testing tools
- ✅ Development dashboard
- ✅ Complete API documentation

**Happy coding!** 🚀
