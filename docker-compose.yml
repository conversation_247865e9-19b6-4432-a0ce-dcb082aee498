version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cve-feed-postgres
    environment:
      POSTGRES_DB: cve_feed_db
      POSTGRES_USER: cve_user
      POSTGRES_PASSWORD: cve_password_secure_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - cve-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cve_user -d cve_feed_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    labels:
      - "traefik.enable=false"

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: cve-feed-redis
    command: redis-server --appendonly yes --requirepass redis_password_secure_2024
    volumes:
      - redis_data:/data
    networks:
      - cve-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    labels:
      - "traefik.enable=false"

  # CVE Feed Service API
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: cve-feed-api
    environment:
      - DATABASE_URL=postgresql+asyncpg://cve_user:cve_password_secure_2024@postgres:5432/cve_feed_db
      - REDIS_URL=redis://:redis_password_secure_2024@redis:6379/0
      - SECRET_KEY=your-super-secret-key-change-in-production-2024
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - CORS_ORIGINS=https://app.feedme.localhost,https://admin.feedme.localhost
      - NVD_API_KEY=${NVD_API_KEY:-}
      - RATE_LIMIT_REQUESTS=100
      - RATE_LIMIT_WINDOW=60
    volumes:
      - ./logs:/app/logs
    networks:
      - cve-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cve-api.rule=Host(`api.feedme.localhost`)"
      - "traefik.http.routers.cve-api.entrypoints=web"
      - "traefik.http.services.cve-api.loadbalancer.server.port=8000"
      - "traefik.http.routers.cve-api.middlewares=api-cors"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolallowmethods=GET,OPTIONS,PUT,POST,DELETE,PATCH"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolallowheaders=*"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolalloworiginlist=https://app.feedme.localhost,https://admin.feedme.localhost"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.api-cors.headers.addvaryheader=true"

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: cve-feed-frontend
    environment:
      - NEXT_PUBLIC_API_URL=https://api.feedme.localhost
      - NEXT_PUBLIC_APP_ENV=production
      - NEXT_PUBLIC_SENTRY_DSN=${SENTRY_DSN:-}
    networks:
      - cve-network
    depends_on:
      - api
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cve-app.rule=Host(`app.feedme.localhost`)"
      - "traefik.http.routers.cve-app.entrypoints=web"
      - "traefik.http.services.cve-app.loadbalancer.server.port=3000"
      - "traefik.http.routers.cve-app.middlewares=app-headers"
      - "traefik.http.middlewares.app-headers.headers.customrequestheaders.X-Forwarded-Proto=https"

  # Admin Dashboard
  admin:
    build:
      context: ./admin
      dockerfile: Dockerfile
    container_name: cve-feed-admin
    environment:
      - REACT_APP_API_URL=https://api.feedme.localhost
      - REACT_APP_ENV=production
    networks:
      - cve-network
    depends_on:
      - api
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cve-admin.rule=Host(`admin.feedme.localhost`)"
      - "traefik.http.routers.cve-admin.entrypoints=web"
      - "traefik.http.services.cve-admin.loadbalancer.server.port=3000"
      - "traefik.http.routers.cve-admin.middlewares=admin-auth,admin-headers"
      - "traefik.http.middlewares.admin-auth.basicauth.users=admin:$$2y$$10$$K8C7VqcqKf8vKzKzKzKzKe"
      - "traefik.http.middlewares.admin-headers.headers.customrequestheaders.X-Forwarded-Proto=https"

  # Documentation Site
  docs:
    build:
      context: ./docs
      dockerfile: Dockerfile
    container_name: cve-feed-docs
    networks:
      - cve-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cve-docs.rule=Host(`docs.feedme.localhost`)"
      - "traefik.http.routers.cve-docs.entrypoints=web"
      - "traefik.http.services.cve-docs.loadbalancer.server.port=80"

  # Background Worker for CVE Processing
  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    container_name: cve-feed-worker
    environment:
      - DATABASE_URL=postgresql+asyncpg://cve_user:cve_password_secure_2024@postgres:5432/cve_feed_db
      - REDIS_URL=redis://:redis_password_secure_2024@redis:6379/0
      - NVD_API_KEY=${NVD_API_KEY:-}
      - LOG_LEVEL=INFO
      - WORKER_CONCURRENCY=4
      - CVE_SYNC_INTERVAL=3600
    volumes:
      - ./logs:/app/logs
    networks:
      - cve-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    labels:
      - "traefik.enable=false"

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: cve-feed-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - cve-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cve-prometheus.rule=Host(`metrics.feedme.localhost`)"
      - "traefik.http.routers.cve-prometheus.entrypoints=web"
      - "traefik.http.services.cve-prometheus.loadbalancer.server.port=9090"
      - "traefik.http.routers.cve-prometheus.middlewares=metrics-auth"
      - "traefik.http.middlewares.metrics-auth.basicauth.users=admin:$$2y$$10$$K8C7VqcqKf8vKzKzKzKzKe"

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: cve-feed-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin_password_secure_2024
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_DOMAIN=dashboard.feedme.localhost
      - GF_SERVER_ROOT_URL=https://dashboard.feedme.localhost
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - cve-network
    depends_on:
      - prometheus
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cve-grafana.rule=Host(`dashboard.feedme.localhost`)"
      - "traefik.http.routers.cve-grafana.entrypoints=web"
      - "traefik.http.services.cve-grafana.loadbalancer.server.port=3000"

networks:
  cve-network:
    driver: bridge
    name: cve-feed-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
