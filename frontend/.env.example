# CVE Feed Service Frontend Configuration

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_URL=http://localhost:3000

# App Configuration
NEXT_PUBLIC_APP_NAME="CVE Feed Service"
NEXT_PUBLIC_APP_DESCRIPTION="Vulnerability Management and CVE Monitoring Dashboard"

# Authentication
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=http://localhost:3000

# Analytics (Optional)
NEXT_PUBLIC_GA_MEASUREMENT_ID=
NEXT_PUBLIC_HOTJAR_ID=

# Error Monitoring (Optional)
NEXT_PUBLIC_SENTRY_DSN=

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_ERROR_MONITORING=false

# Development
NODE_ENV=development
ANALYZE=false

# SEO
GOOGLE_SITE_VERIFICATION=

# Theme
NEXT_PUBLIC_DEFAULT_THEME=dark
