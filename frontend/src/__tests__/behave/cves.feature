Feature: CVE Management System
  As a security analyst
  I want to manage and analyze CVE vulnerabilities
  So that I can assess and mitigate security risks effectively

  Background:
    Given I am logged in as a security analyst
    And I have access to the CVE management system

  Scenario: View CVE list with basic information
    Given there are CVEs in the system
    When I navigate to the CVE list page
    Then I should see a list of CVEs
    And each CVE should display its ID
    And each CVE should display its severity level
    And each CVE should display its CVSS score
    And each CVE should display its publication date
    And each CVE should display affected applications count

  Scenario: Filter CVEs by severity level
    Given there are CVEs with different severity levels
    When I filter CVEs by "Critical" severity
    Then I should only see CVEs with critical severity
    And the CVE count should reflect the filtered results
    And the filter should be visually indicated

  Scenario: Filter CVEs by multiple criteria
    Given there are CVEs with various attributes
    When I apply multiple filters:
      | Filter Type | Value |
      | Severity | High, Critical |
      | Patch Available | Yes |
      | Trending | Yes |
    Then I should see CVEs matching all criteria
    And the applied filters should be clearly displayed
    And I should be able to clear individual filters

  Scenario: Search CVEs by keyword
    Given there are CVEs in the system
    When I search for "remote code execution"
    Then I should see CVEs containing the search term
    And the search term should be highlighted in results
    And I should see the total number of search results

  Scenario: Sort CVEs by different criteria
    Given there are multiple CVEs in the system
    When I sort CVEs by CVSS score in descending order
    Then CVEs should be ordered from highest to lowest score
    And the sort indicator should show the current sort direction
    When I change sort to publication date ascending
    Then CVEs should be ordered from oldest to newest

  Scenario: View detailed CVE information
    Given there is a CVE "CVE-2024-0001" in the system
    When I click on the CVE ID
    Then I should navigate to the CVE details page
    And I should see comprehensive CVE information:
      | Field | Present |
      | Description | Yes |
      | CVSS Vector | Yes |
      | Affected Products | Yes |
      | References | Yes |
      | CWE Categories | Yes |
      | Timeline | Yes |
      | Remediation | Yes |

  Scenario: Analyze CVE technical details
    Given I am viewing a CVE details page
    When I examine the technical details section
    Then I should see CVSS v3.1 breakdown:
      | Metric | Displayed |
      | Attack Vector | Yes |
      | Attack Complexity | Yes |
      | Privileges Required | Yes |
      | User Interaction | Yes |
      | Scope | Yes |
      | Impact Ratings | Yes |

  Scenario: View affected applications for a CVE
    Given a CVE affects multiple applications
    When I view the CVE details
    Then I should see a list of affected applications
    And each application should show its name
    And each application should show its version
    And each application should show its environment
    And each application should show its risk level

  Scenario: Track CVE remediation status
    Given there are CVEs with different remediation statuses
    When I view the CVE list
    Then I should see remediation status indicators
    And I should be able to filter by remediation status
    When I view a CVE with available patches
    Then I should see patch information
    And I should see download links for patches

  Scenario: Monitor trending CVEs
    Given there are trending CVEs in the system
    When I navigate to the trending CVEs section
    Then I should see CVEs marked as trending
    And trending CVEs should be prominently displayed
    And I should see why each CVE is trending

  Scenario: Export CVE data
    Given I have filtered CVEs by specific criteria
    When I click the export button
    Then I should see export format options
    And I should be able to export as CSV
    And I should be able to export as JSON
    And I should be able to export as PDF report
    And the export should include current filters

  Scenario: Add CVE to watchlist
    Given I am viewing a CVE details page
    When I click the "Add to Watchlist" button
    Then the CVE should be added to my watchlist
    And I should see a confirmation message
    And the button should change to "Remove from Watchlist"

  Scenario: Receive CVE notifications
    Given I have CVEs in my watchlist
    When a watched CVE is updated
    Then I should receive a notification
    And the notification should indicate what changed
    And I should be able to navigate to the updated CVE

  Scenario: Assess CVE risk level
    Given I am viewing a CVE details page
    When I examine the risk assessment section
    Then I should see an overall risk level
    And I should see risk factors contributing to the assessment
    And I should see recommended actions
    And I should see priority level

  Scenario: View CVE timeline and history
    Given a CVE has a publication and modification history
    When I view the CVE timeline
    Then I should see chronological events
    And each event should have a timestamp
    And each event should have a description
    And I should see key milestones like discovery and disclosure

  Scenario: Compare CVE severity and impact
    Given there are multiple CVEs to compare
    When I select CVEs for comparison
    Then I should see a side-by-side comparison
    And I should see CVSS scores compared
    And I should see affected applications compared
    And I should see remediation status compared

  Scenario: Filter CVEs by date range
    Given there are CVEs published over different time periods
    When I set a date range filter for the last 30 days
    Then I should only see CVEs published in that period
    And the date range should be clearly displayed
    And I should be able to use preset date ranges

  Scenario: View CVE statistics and metrics
    Given there are CVEs in the system
    When I navigate to the CVE statistics page
    Then I should see total CVE count
    And I should see CVEs grouped by severity
    And I should see CVEs grouped by status
    And I should see trending statistics
    And I should see patch availability statistics

  Scenario: Handle CVE data loading and errors
    Given the CVE system is experiencing high load
    When I navigate to the CVE list
    Then I should see loading indicators
    And the interface should remain responsive
    When there is a network error
    Then I should see an appropriate error message
    And I should have options to retry

  Scenario: CVE accessibility features
    Given I am using assistive technology
    When I navigate the CVE interface
    Then all CVE information should be accessible
    And severity levels should have proper color contrast
    And I should be able to navigate using keyboard only
    And screen readers should announce CVE details properly

  Scenario: Mobile CVE management
    Given I am using a mobile device
    When I access the CVE management system
    Then the interface should be touch-friendly
    And CVE lists should be easily scrollable
    And filters should work with touch gestures
    And CVE details should be readable on small screens

  Scenario: CVE data synchronization
    Given I am viewing CVE data
    When new CVE data becomes available
    Then the interface should update automatically
    And I should see indicators for new or updated CVEs
    And my current view should be preserved during updates

  Scenario: Advanced CVE search
    Given there are CVEs with various attributes
    When I use advanced search with multiple criteria:
      | Criteria | Value |
      | CVSS Score Range | 7.0 - 10.0 |
      | CWE Category | Injection |
      | Affected Product | WebFramework |
      | Has Exploit | Yes |
    Then I should see CVEs matching all criteria
    And I should be able to save search queries
    And I should be able to set up alerts for saved searches

  Scenario: CVE impact analysis
    Given I am viewing a high-severity CVE
    When I analyze its potential impact
    Then I should see affected systems analysis
    And I should see business impact assessment
    And I should see recommended mitigation timeline
    And I should see cost-benefit analysis for remediation
