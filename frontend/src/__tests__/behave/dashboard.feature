Feature: Dashboard Security Analytics
  As a security analyst
  I want to view comprehensive security metrics on the dashboard
  So that I can quickly assess the organization's security posture

  Background:
    Given I am logged in as a security analyst
    And I have access to the dashboard

  Scenario: View overall security metrics
    Given the system has vulnerability data
    When I navigate to the dashboard
    Then I should see the total number of CVEs
    And I should see the count of critical vulnerabilities
    And I should see the count of high severity vulnerabilities
    And I should see the count of medium severity vulnerabilities
    And I should see the count of low severity vulnerabilities
    And I should see the overall compliance score

  Scenario: View vulnerability trends
    Given the system has historical vulnerability data
    When I view the dashboard trends section
    Then I should see a trend chart showing vulnerability changes over time
    And I should see the percentage change from the previous period
    And I should see trend indicators (up/down arrows)
    And the trend data should be accurate and up-to-date

  Scenario: View top critical vulnerabilities
    Given there are critical vulnerabilities in the system
    When I view the dashboard
    Then I should see a list of top critical vulnerabilities
    And each vulnerability should show its CVE ID
    And each vulnerability should show its CVSS score
    And each vulnerability should show the number of affected applications
    And each vulnerability should show a brief description

  Scenario: View recent security activities
    Given there have been recent security activities
    When I view the dashboard
    Then I should see a list of recent activities
    And each activity should show the activity type
    And each activity should show a description
    And each activity should show the timestamp
    And each activity should show the user who performed it
    And activities should be ordered by most recent first

  Scenario: View application security metrics
    Given there are applications in the portfolio
    When I view the application metrics section
    Then I should see the total number of applications
    And I should see applications grouped by environment
    And I should see applications grouped by criticality level
    And I should see the number of vulnerable applications
    And I should see compliance status breakdown

  Scenario: Real-time data updates
    Given I am viewing the dashboard
    When new vulnerability data becomes available
    Then the dashboard metrics should update automatically
    And I should see a notification about the update
    And the last updated timestamp should reflect the current time

  Scenario: Dashboard performance with large datasets
    Given the system contains over 1000 CVEs
    And there are more than 50 applications
    When I load the dashboard
    Then the page should load within 3 seconds
    And all metrics should be displayed correctly
    And the interface should remain responsive

  Scenario: Dashboard accessibility
    Given I am using a screen reader
    When I navigate the dashboard
    Then all metrics should be announced properly
    And all charts should have text alternatives
    And I should be able to navigate using only the keyboard
    And all interactive elements should be focusable

  Scenario: Mobile dashboard view
    Given I am using a mobile device
    When I access the dashboard
    Then the layout should be responsive
    And all metrics should be visible
    And charts should be touch-friendly
    And navigation should work with touch gestures

  Scenario: Dashboard error handling
    Given the dashboard API is temporarily unavailable
    When I try to load the dashboard
    Then I should see an appropriate error message
    And I should see options to retry loading
    And cached data should be displayed if available
    And the error should not break the entire interface

  Scenario: Filter dashboard by time period
    Given I am viewing the dashboard
    When I select a different time period filter
    Then the vulnerability trends should update accordingly
    And the metrics should reflect the selected time period
    And the recent activities should be filtered by the time period
    And the URL should update to reflect the filter

  Scenario: Export dashboard data
    Given I am viewing the dashboard with current metrics
    When I click the export button
    Then I should see export format options
    And I should be able to export as PDF
    And I should be able to export as Excel
    And the exported data should include all visible metrics
    And the export should complete successfully

  Scenario: Dashboard customization
    Given I am viewing the dashboard
    When I access dashboard settings
    Then I should be able to customize which metrics are displayed
    And I should be able to rearrange metric cards
    And I should be able to set refresh intervals
    And my preferences should be saved for future visits

  Scenario: Drill-down from dashboard metrics
    Given I am viewing the dashboard
    When I click on the critical vulnerabilities count
    Then I should navigate to the CVE list filtered by critical severity
    And the filter should be pre-applied
    And I should be able to return to the dashboard easily

  Scenario: Dashboard alerts and notifications
    Given there are new critical vulnerabilities
    When I view the dashboard
    Then I should see alert indicators for critical items
    And I should receive notifications for urgent security events
    And alerts should be dismissible
    And I should be able to configure alert preferences

  Scenario: Dashboard data refresh
    Given I am viewing the dashboard
    When I click the refresh button
    Then all metrics should be updated with latest data
    And I should see a loading indicator during refresh
    And the last updated timestamp should change
    And any errors during refresh should be handled gracefully

  Scenario: Dashboard comparison view
    Given I have historical dashboard data
    When I enable comparison mode
    Then I should see current metrics alongside previous period metrics
    And I should see percentage changes highlighted
    And I should see trend indicators for each metric
    And I should be able to select different comparison periods

  Scenario: Dashboard search functionality
    Given I am on the dashboard
    When I use the global search feature
    Then I should be able to search for specific CVEs
    And I should be able to search for applications
    And search results should be relevant and accurate
    And I should be able to navigate to detailed views from search results

  Scenario: Dashboard offline functionality
    Given I have previously loaded the dashboard
    When I lose internet connectivity
    Then I should still be able to view cached dashboard data
    And I should see an indicator that I am offline
    And I should see when the data was last updated
    And the interface should remain functional for basic operations
