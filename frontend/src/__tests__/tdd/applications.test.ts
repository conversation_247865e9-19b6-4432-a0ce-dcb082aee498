/**
 * Application Management TDD Tests
 * Test-Driven Development tests for application management functionality
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Application management utility functions and classes
interface Application {
  id: string;
  name: string;
  description: string;
  environment: 'development' | 'staging' | 'production';
  criticality: 'low' | 'medium' | 'high' | 'critical';
  owner: string;
  version?: string;
  vulnerability_count: number;
  risk_score: number;
  compliance_status: 'compliant' | 'non_compliant' | 'unknown';
  technologies: string[];
  last_scan?: string;
  created_at: string;
  updated_at: string;
}

interface Vulnerability {
  id: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  score: number;
  status: 'open' | 'in_progress' | 'resolved' | 'false_positive';
  discovered_at: string;
}

interface ApplicationFilter {
  environment?: string;
  criticality?: string;
  search?: string;
  owner?: string;
  compliance_status?: string;
}

interface ApplicationMetrics {
  total_applications: number;
  by_environment: Record<string, number>;
  by_criticality: Record<string, number>;
  vulnerable_applications: number;
  average_risk_score: number;
  compliance_rate: number;
}

// Application management utility class
class ApplicationManager {
  static calculateRiskScore(vulnerabilities: Vulnerability[]): number {
    if (vulnerabilities.length === 0) return 0;

    const severityWeights = {
      'CRITICAL': 10,
      'HIGH': 7,
      'MEDIUM': 4,
      'LOW': 1
    };

    const totalWeight = vulnerabilities.reduce((sum, vuln) => {
      return sum + (severityWeights[vuln.severity] * (vuln.score / 10));
    }, 0);

    return Math.min(10, Math.round((totalWeight / vulnerabilities.length) * 10) / 10);
  }

  static determineComplianceStatus(
    vulnerabilities: Vulnerability[],
    criticalThreshold: number = 0,
    highThreshold: number = 5
  ): 'compliant' | 'non_compliant' | 'unknown' {
    const criticalCount = vulnerabilities.filter(v => v.severity === 'CRITICAL').length;
    const highCount = vulnerabilities.filter(v => v.severity === 'HIGH').length;

    if (criticalCount > criticalThreshold) return 'non_compliant';
    if (highCount > highThreshold) return 'non_compliant';
    
    return 'compliant';
  }

  static filterApplications(applications: Application[], filter: ApplicationFilter): Application[] {
    return applications.filter(app => {
      if (filter.environment && app.environment !== filter.environment) return false;
      if (filter.criticality && app.criticality !== filter.criticality) return false;
      if (filter.compliance_status && app.compliance_status !== filter.compliance_status) return false;
      if (filter.owner && !app.owner.toLowerCase().includes(filter.owner.toLowerCase())) return false;
      
      if (filter.search) {
        const searchLower = filter.search.toLowerCase();
        const matchesName = app.name.toLowerCase().includes(searchLower);
        const matchesDescription = app.description.toLowerCase().includes(searchLower);
        const matchesTechnology = app.technologies.some(tech => 
          tech.toLowerCase().includes(searchLower)
        );
        
        if (!matchesName && !matchesDescription && !matchesTechnology) return false;
      }

      return true;
    });
  }

  static sortApplications(
    applications: Application[], 
    sortBy: 'name' | 'risk_score' | 'vulnerability_count' | 'last_scan' = 'name',
    order: 'asc' | 'desc' = 'asc'
  ): Application[] {
    return [...applications].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'risk_score':
          comparison = a.risk_score - b.risk_score;
          break;
        case 'vulnerability_count':
          comparison = a.vulnerability_count - b.vulnerability_count;
          break;
        case 'last_scan':
          const aDate = new Date(a.last_scan || 0);
          const bDate = new Date(b.last_scan || 0);
          comparison = aDate.getTime() - bDate.getTime();
          break;
      }

      return order === 'desc' ? -comparison : comparison;
    });
  }

  static calculateApplicationMetrics(applications: Application[]): ApplicationMetrics {
    const total = applications.length;
    
    const byEnvironment = applications.reduce((acc, app) => {
      acc[app.environment] = (acc[app.environment] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byCriticality = applications.reduce((acc, app) => {
      acc[app.criticality] = (acc[app.criticality] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const vulnerableApps = applications.filter(app => app.vulnerability_count > 0).length;
    
    const totalRiskScore = applications.reduce((sum, app) => sum + app.risk_score, 0);
    const averageRiskScore = total > 0 ? totalRiskScore / total : 0;

    const compliantApps = applications.filter(app => app.compliance_status === 'compliant').length;
    const complianceRate = total > 0 ? (compliantApps / total) * 100 : 0;

    return {
      total_applications: total,
      by_environment: byEnvironment,
      by_criticality: byCriticality,
      vulnerable_applications: vulnerableApps,
      average_risk_score: Math.round(averageRiskScore * 10) / 10,
      compliance_rate: Math.round(complianceRate * 10) / 10
    };
  }

  static validateApplicationData(data: Partial<Application>): string[] {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length === 0) {
      errors.push('Application name is required');
    }

    if (data.name && data.name.length > 100) {
      errors.push('Application name must be less than 100 characters');
    }

    if (!data.environment) {
      errors.push('Environment is required');
    }

    if (data.environment && !['development', 'staging', 'production'].includes(data.environment)) {
      errors.push('Environment must be development, staging, or production');
    }

    if (!data.criticality) {
      errors.push('Criticality level is required');
    }

    if (data.criticality && !['low', 'medium', 'high', 'critical'].includes(data.criticality)) {
      errors.push('Criticality must be low, medium, high, or critical');
    }

    if (!data.owner || data.owner.trim().length === 0) {
      errors.push('Owner is required');
    }

    if (data.owner && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.owner)) {
      errors.push('Owner must be a valid email address');
    }

    if (data.technologies && !Array.isArray(data.technologies)) {
      errors.push('Technologies must be an array');
    }

    return errors;
  }

  static generateApplicationId(): string {
    return `app-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  static estimateScanDuration(technologies: string[], applicationSize: 'small' | 'medium' | 'large'): number {
    const baseDuration = {
      'small': 5,
      'medium': 15,
      'large': 30
    };

    const technologyMultipliers: Record<string, number> = {
      'JavaScript': 1.0,
      'TypeScript': 1.1,
      'Python': 1.2,
      'Java': 1.5,
      'C#': 1.3,
      'PHP': 1.1,
      'Ruby': 1.0,
      'Go': 0.8,
      'Rust': 0.9
    };

    let multiplier = 1.0;
    technologies.forEach(tech => {
      if (technologyMultipliers[tech]) {
        multiplier *= technologyMultipliers[tech];
      }
    });

    return Math.round(baseDuration[applicationSize] * multiplier);
  }
}

describe('Application Management TDD Tests', () => {
  describe('Risk Score Calculation', () => {
    it('should return 0 for applications with no vulnerabilities', () => {
      const score = ApplicationManager.calculateRiskScore([]);
      expect(score).toBe(0);
    });

    it('should calculate risk score based on vulnerability severity and CVSS scores', () => {
      const vulnerabilities: Vulnerability[] = [
        { id: 'CVE-1', severity: 'CRITICAL', score: 9.8, status: 'open', discovered_at: '2024-01-01' },
        { id: 'CVE-2', severity: 'HIGH', score: 8.5, status: 'open', discovered_at: '2024-01-02' },
        { id: 'CVE-3', severity: 'MEDIUM', score: 6.2, status: 'open', discovered_at: '2024-01-03' }
      ];

      const score = ApplicationManager.calculateRiskScore(vulnerabilities);
      expect(score).toBeGreaterThan(7);
      expect(score).toBeLessThanOrEqual(10);
    });

    it('should cap risk score at 10', () => {
      const vulnerabilities: Vulnerability[] = Array(20).fill(null).map((_, i) => ({
        id: `CVE-${i}`,
        severity: 'CRITICAL' as const,
        score: 10,
        status: 'open' as const,
        discovered_at: '2024-01-01'
      }));

      const score = ApplicationManager.calculateRiskScore(vulnerabilities);
      expect(score).toBe(10);
    });

    it('should weight critical vulnerabilities higher than others', () => {
      const criticalVulns: Vulnerability[] = [
        { id: 'CVE-1', severity: 'CRITICAL', score: 9.0, status: 'open', discovered_at: '2024-01-01' }
      ];

      const mediumVulns: Vulnerability[] = [
        { id: 'CVE-2', severity: 'MEDIUM', score: 6.0, status: 'open', discovered_at: '2024-01-01' }
      ];

      const criticalScore = ApplicationManager.calculateRiskScore(criticalVulns);
      const mediumScore = ApplicationManager.calculateRiskScore(mediumVulns);

      expect(criticalScore).toBeGreaterThan(mediumScore);
    });
  });

  describe('Compliance Status Determination', () => {
    it('should mark as non-compliant when critical vulnerabilities exceed threshold', () => {
      const vulnerabilities: Vulnerability[] = [
        { id: 'CVE-1', severity: 'CRITICAL', score: 9.8, status: 'open', discovered_at: '2024-01-01' },
        { id: 'CVE-2', severity: 'CRITICAL', score: 9.5, status: 'open', discovered_at: '2024-01-02' }
      ];

      const status = ApplicationManager.determineComplianceStatus(vulnerabilities, 1, 5);
      expect(status).toBe('non_compliant');
    });

    it('should mark as non-compliant when high vulnerabilities exceed threshold', () => {
      const vulnerabilities: Vulnerability[] = Array(7).fill(null).map((_, i) => ({
        id: `CVE-${i}`,
        severity: 'HIGH' as const,
        score: 8.0,
        status: 'open' as const,
        discovered_at: '2024-01-01'
      }));

      const status = ApplicationManager.determineComplianceStatus(vulnerabilities, 0, 5);
      expect(status).toBe('non_compliant');
    });

    it('should mark as compliant when within thresholds', () => {
      const vulnerabilities: Vulnerability[] = [
        { id: 'CVE-1', severity: 'HIGH', score: 8.0, status: 'open', discovered_at: '2024-01-01' },
        { id: 'CVE-2', severity: 'MEDIUM', score: 6.0, status: 'open', discovered_at: '2024-01-02' },
        { id: 'CVE-3', severity: 'LOW', score: 3.0, status: 'open', discovered_at: '2024-01-03' }
      ];

      const status = ApplicationManager.determineComplianceStatus(vulnerabilities, 0, 5);
      expect(status).toBe('compliant');
    });
  });

  describe('Application Filtering', () => {
    const mockApplications: Application[] = [
      {
        id: '1',
        name: 'Customer Portal',
        description: 'Main customer interface',
        environment: 'production',
        criticality: 'high',
        owner: '<EMAIL>',
        vulnerability_count: 5,
        risk_score: 7.2,
        compliance_status: 'compliant',
        technologies: ['React', 'Node.js'],
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      },
      {
        id: '2',
        name: 'Admin Dashboard',
        description: 'Internal admin tools',
        environment: 'production',
        criticality: 'critical',
        owner: '<EMAIL>',
        vulnerability_count: 12,
        risk_score: 8.5,
        compliance_status: 'non_compliant',
        technologies: ['Vue.js', 'Python'],
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      },
      {
        id: '3',
        name: 'Test Application',
        description: 'Development testing app',
        environment: 'development',
        criticality: 'low',
        owner: '<EMAIL>',
        vulnerability_count: 2,
        risk_score: 3.1,
        compliance_status: 'compliant',
        technologies: ['JavaScript'],
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      }
    ];

    it('should filter by environment', () => {
      const filtered = ApplicationManager.filterApplications(mockApplications, {
        environment: 'production'
      });

      expect(filtered).toHaveLength(2);
      expect(filtered.every(app => app.environment === 'production')).toBe(true);
    });

    it('should filter by criticality', () => {
      const filtered = ApplicationManager.filterApplications(mockApplications, {
        criticality: 'high'
      });

      expect(filtered).toHaveLength(1);
      expect(filtered[0].criticality).toBe('high');
    });

    it('should filter by compliance status', () => {
      const filtered = ApplicationManager.filterApplications(mockApplications, {
        compliance_status: 'non_compliant'
      });

      expect(filtered).toHaveLength(1);
      expect(filtered[0].compliance_status).toBe('non_compliant');
    });

    it('should filter by owner', () => {
      const filtered = ApplicationManager.filterApplications(mockApplications, {
        owner: 'frontend'
      });

      expect(filtered).toHaveLength(1);
      expect(filtered[0].owner).toContain('frontend');
    });

    it('should search across name, description, and technologies', () => {
      const filtered = ApplicationManager.filterApplications(mockApplications, {
        search: 'React'
      });

      expect(filtered).toHaveLength(1);
      expect(filtered[0].technologies).toContain('React');
    });

    it('should combine multiple filters', () => {
      const filtered = ApplicationManager.filterApplications(mockApplications, {
        environment: 'production',
        criticality: 'high'
      });

      expect(filtered).toHaveLength(1);
      expect(filtered[0].environment).toBe('production');
      expect(filtered[0].criticality).toBe('high');
    });

    it('should return empty array when no matches found', () => {
      const filtered = ApplicationManager.filterApplications(mockApplications, {
        search: 'nonexistent'
      });

      expect(filtered).toHaveLength(0);
    });
  });

  describe('Application Sorting', () => {
    const mockApplications: Application[] = [
      {
        id: '1', name: 'Zebra App', risk_score: 8.5, vulnerability_count: 10,
        last_scan: '2024-01-20T10:00:00Z', environment: 'production', criticality: 'high',
        owner: '<EMAIL>', description: 'Test', compliance_status: 'compliant',
        technologies: [], created_at: '2024-01-01', updated_at: '2024-01-01'
      },
      {
        id: '2', name: 'Alpha App', risk_score: 6.2, vulnerability_count: 5,
        last_scan: '2024-01-22T10:00:00Z', environment: 'production', criticality: 'medium',
        owner: '<EMAIL>', description: 'Test', compliance_status: 'compliant',
        technologies: [], created_at: '2024-01-01', updated_at: '2024-01-01'
      },
      {
        id: '3', name: 'Beta App', risk_score: 9.1, vulnerability_count: 15,
        last_scan: '2024-01-18T10:00:00Z', environment: 'production', criticality: 'critical',
        owner: '<EMAIL>', description: 'Test', compliance_status: 'non_compliant',
        technologies: [], created_at: '2024-01-01', updated_at: '2024-01-01'
      }
    ];

    it('should sort by name alphabetically', () => {
      const sorted = ApplicationManager.sortApplications(mockApplications, 'name', 'asc');
      
      expect(sorted[0].name).toBe('Alpha App');
      expect(sorted[1].name).toBe('Beta App');
      expect(sorted[2].name).toBe('Zebra App');
    });

    it('should sort by risk score descending', () => {
      const sorted = ApplicationManager.sortApplications(mockApplications, 'risk_score', 'desc');
      
      expect(sorted[0].risk_score).toBe(9.1);
      expect(sorted[1].risk_score).toBe(8.5);
      expect(sorted[2].risk_score).toBe(6.2);
    });

    it('should sort by vulnerability count ascending', () => {
      const sorted = ApplicationManager.sortApplications(mockApplications, 'vulnerability_count', 'asc');
      
      expect(sorted[0].vulnerability_count).toBe(5);
      expect(sorted[1].vulnerability_count).toBe(10);
      expect(sorted[2].vulnerability_count).toBe(15);
    });

    it('should sort by last scan date', () => {
      const sorted = ApplicationManager.sortApplications(mockApplications, 'last_scan', 'desc');
      
      expect(sorted[0].last_scan).toBe('2024-01-22T10:00:00Z');
      expect(sorted[1].last_scan).toBe('2024-01-20T10:00:00Z');
      expect(sorted[2].last_scan).toBe('2024-01-18T10:00:00Z');
    });
  });

  describe('Application Metrics Calculation', () => {
    const mockApplications: Application[] = [
      {
        id: '1', name: 'App 1', environment: 'production', criticality: 'high',
        vulnerability_count: 5, risk_score: 7.5, compliance_status: 'compliant',
        owner: '<EMAIL>', description: 'Test', technologies: [],
        created_at: '2024-01-01', updated_at: '2024-01-01'
      },
      {
        id: '2', name: 'App 2', environment: 'production', criticality: 'medium',
        vulnerability_count: 0, risk_score: 2.1, compliance_status: 'compliant',
        owner: '<EMAIL>', description: 'Test', technologies: [],
        created_at: '2024-01-01', updated_at: '2024-01-01'
      },
      {
        id: '3', name: 'App 3', environment: 'staging', criticality: 'critical',
        vulnerability_count: 12, risk_score: 9.2, compliance_status: 'non_compliant',
        owner: '<EMAIL>', description: 'Test', technologies: [],
        created_at: '2024-01-01', updated_at: '2024-01-01'
      }
    ];

    it('should calculate total applications', () => {
      const metrics = ApplicationManager.calculateApplicationMetrics(mockApplications);
      expect(metrics.total_applications).toBe(3);
    });

    it('should group applications by environment', () => {
      const metrics = ApplicationManager.calculateApplicationMetrics(mockApplications);
      
      expect(metrics.by_environment.production).toBe(2);
      expect(metrics.by_environment.staging).toBe(1);
    });

    it('should group applications by criticality', () => {
      const metrics = ApplicationManager.calculateApplicationMetrics(mockApplications);
      
      expect(metrics.by_criticality.high).toBe(1);
      expect(metrics.by_criticality.medium).toBe(1);
      expect(metrics.by_criticality.critical).toBe(1);
    });

    it('should count vulnerable applications', () => {
      const metrics = ApplicationManager.calculateApplicationMetrics(mockApplications);
      expect(metrics.vulnerable_applications).toBe(2); // Apps with vulnerability_count > 0
    });

    it('should calculate average risk score', () => {
      const metrics = ApplicationManager.calculateApplicationMetrics(mockApplications);
      expect(metrics.average_risk_score).toBe(6.3); // (7.5 + 2.1 + 9.2) / 3 = 6.27 rounded to 6.3
    });

    it('should calculate compliance rate', () => {
      const metrics = ApplicationManager.calculateApplicationMetrics(mockApplications);
      expect(metrics.compliance_rate).toBe(66.7); // 2/3 * 100 = 66.67 rounded to 66.7
    });

    it('should handle empty applications array', () => {
      const metrics = ApplicationManager.calculateApplicationMetrics([]);
      
      expect(metrics.total_applications).toBe(0);
      expect(metrics.vulnerable_applications).toBe(0);
      expect(metrics.average_risk_score).toBe(0);
      expect(metrics.compliance_rate).toBe(0);
    });
  });

  describe('Application Data Validation', () => {
    it('should validate required fields', () => {
      const errors = ApplicationManager.validateApplicationData({});
      
      expect(errors).toContain('Application name is required');
      expect(errors).toContain('Environment is required');
      expect(errors).toContain('Criticality level is required');
      expect(errors).toContain('Owner is required');
    });

    it('should validate name length', () => {
      const longName = 'a'.repeat(101);
      const errors = ApplicationManager.validateApplicationData({ name: longName });
      
      expect(errors).toContain('Application name must be less than 100 characters');
    });

    it('should validate environment values', () => {
      const errors = ApplicationManager.validateApplicationData({
        name: 'Test App',
        environment: 'invalid' as any,
        criticality: 'high',
        owner: '<EMAIL>'
      });
      
      expect(errors).toContain('Environment must be development, staging, or production');
    });

    it('should validate criticality values', () => {
      const errors = ApplicationManager.validateApplicationData({
        name: 'Test App',
        environment: 'production',
        criticality: 'invalid' as any,
        owner: '<EMAIL>'
      });
      
      expect(errors).toContain('Criticality must be low, medium, high, or critical');
    });

    it('should validate email format for owner', () => {
      const errors = ApplicationManager.validateApplicationData({
        name: 'Test App',
        environment: 'production',
        criticality: 'high',
        owner: 'invalid-email'
      });
      
      expect(errors).toContain('Owner must be a valid email address');
    });

    it('should validate technologies array', () => {
      const errors = ApplicationManager.validateApplicationData({
        name: 'Test App',
        environment: 'production',
        criticality: 'high',
        owner: '<EMAIL>',
        technologies: 'not-an-array' as any
      });
      
      expect(errors).toContain('Technologies must be an array');
    });

    it('should return empty array for valid data', () => {
      const errors = ApplicationManager.validateApplicationData({
        name: 'Test App',
        environment: 'production',
        criticality: 'high',
        owner: '<EMAIL>',
        technologies: ['React', 'Node.js']
      });
      
      expect(errors).toHaveLength(0);
    });
  });

  describe('Utility Functions', () => {
    it('should generate unique application IDs', () => {
      const id1 = ApplicationManager.generateApplicationId();
      const id2 = ApplicationManager.generateApplicationId();
      
      expect(id1).toMatch(/^app-\d+-[a-z0-9]+$/);
      expect(id2).toMatch(/^app-\d+-[a-z0-9]+$/);
      expect(id1).not.toBe(id2);
    });

    it('should estimate scan duration based on technologies and size', () => {
      const duration1 = ApplicationManager.estimateScanDuration(['JavaScript'], 'small');
      const duration2 = ApplicationManager.estimateScanDuration(['Java', 'Python'], 'large');
      
      expect(duration1).toBeGreaterThan(0);
      expect(duration2).toBeGreaterThan(duration1);
      expect(typeof duration1).toBe('number');
      expect(typeof duration2).toBe('number');
    });

    it('should apply technology multipliers correctly', () => {
      const goDuration = ApplicationManager.estimateScanDuration(['Go'], 'medium');
      const javaDuration = ApplicationManager.estimateScanDuration(['Java'], 'medium');
      
      expect(goDuration).toBeLessThan(javaDuration); // Go should be faster
    });
  });
});
