/**
 * CVE Management TDD Tests
 * Test-Driven Development tests for CVE management functionality
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// CVE management interfaces and types
interface CVE {
  id: string;
  description: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  cvss_score: number;
  cvss_vector?: string;
  published_date: string;
  modified_date?: string;
  status: 'published' | 'modified' | 'rejected' | 'disputed';
  affected_products: string[];
  references: string[];
  cwe_ids: string[];
  affected_applications: number;
  remediation_status: 'none' | 'available' | 'in_progress' | 'completed';
  patch_available: boolean;
  exploit_available: boolean;
  trending: boolean;
  tags: string[];
}

interface CVEFilter {
  severity?: string[];
  status?: string;
  trending?: boolean;
  patch_available?: boolean;
  exploit_available?: boolean;
  search?: string;
  date_range?: {
    start: string;
    end: string;
  };
  cvss_range?: {
    min: number;
    max: number;
  };
}

interface CVEMetrics {
  total_cves: number;
  by_severity: Record<string, number>;
  by_status: Record<string, number>;
  trending_count: number;
  patched_count: number;
  exploited_count: number;
  average_cvss: number;
  high_risk_count: number;
}

interface RiskAssessment {
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  risk_score: number;
  factors: string[];
  recommendations: string[];
  priority: number;
}

// CVE management utility class
class CVEManager {
  static calculateRiskScore(cve: CVE): number {
    let baseScore = cve.cvss_score;

    // Adjust for exploit availability
    if (cve.exploit_available) {
      baseScore += 1.5;
    }

    // Adjust for patch availability (inverse relationship)
    if (!cve.patch_available) {
      baseScore += 1.0;
    }

    // Adjust for trending status
    if (cve.trending) {
      baseScore += 0.5;
    }

    // Adjust for affected applications
    const appMultiplier = Math.min(cve.affected_applications * 0.1, 1.0);
    baseScore += appMultiplier;

    return Math.min(10, Math.round(baseScore * 10) / 10);
  }

  static assessRisk(cve: CVE): RiskAssessment {
    const riskScore = this.calculateRiskScore(cve);
    const factors: string[] = [];
    const recommendations: string[] = [];

    // Determine risk level
    let riskLevel: 'low' | 'medium' | 'high' | 'critical';
    if (riskScore >= 9.0) {
      riskLevel = 'critical';
    } else if (riskScore >= 7.0) {
      riskLevel = 'high';
    } else if (riskScore >= 4.0) {
      riskLevel = 'medium';
    } else {
      riskLevel = 'low';
    }

    // Identify risk factors
    if (cve.exploit_available) {
      factors.push('Public exploit available');
      recommendations.push('Prioritize patching due to active exploitation risk');
    }

    if (!cve.patch_available) {
      factors.push('No patch available');
      recommendations.push('Implement workarounds and monitoring');
    }

    if (cve.trending) {
      factors.push('Currently trending');
      recommendations.push('Monitor for increased attack activity');
    }

    if (cve.affected_applications > 5) {
      factors.push('Multiple applications affected');
      recommendations.push('Coordinate remediation across all affected systems');
    }

    if (cve.severity === 'CRITICAL' && cve.cvss_score >= 9.0) {
      factors.push('Critical severity with high CVSS score');
      recommendations.push('Immediate action required');
    }

    // Calculate priority (1-10, 10 being highest)
    const priority = Math.min(10, Math.ceil(riskScore));

    return {
      risk_level: riskLevel,
      risk_score: riskScore,
      factors,
      recommendations,
      priority
    };
  }

  static filterCVEs(cves: CVE[], filter: CVEFilter): CVE[] {
    return cves.filter(cve => {
      // Severity filter
      if (filter.severity && filter.severity.length > 0) {
        if (!filter.severity.includes(cve.severity)) return false;
      }

      // Status filter
      if (filter.status && cve.status !== filter.status) return false;

      // Trending filter
      if (filter.trending !== undefined && cve.trending !== filter.trending) return false;

      // Patch availability filter
      if (filter.patch_available !== undefined && cve.patch_available !== filter.patch_available) return false;

      // Exploit availability filter
      if (filter.exploit_available !== undefined && cve.exploit_available !== filter.exploit_available) return false;

      // Search filter
      if (filter.search) {
        const searchLower = filter.search.toLowerCase();
        const matchesId = cve.id.toLowerCase().includes(searchLower);
        const matchesDescription = cve.description.toLowerCase().includes(searchLower);
        const matchesTags = cve.tags.some(tag => tag.toLowerCase().includes(searchLower));
        const matchesProducts = cve.affected_products.some(product =>
          product.toLowerCase().includes(searchLower)
        );

        if (!matchesId && !matchesDescription && !matchesTags && !matchesProducts) return false;
      }

      // Date range filter
      if (filter.date_range) {
        const publishedDate = new Date(cve.published_date);
        const startDate = new Date(filter.date_range.start);
        const endDate = new Date(filter.date_range.end);

        if (publishedDate < startDate || publishedDate > endDate) return false;
      }

      // CVSS range filter
      if (filter.cvss_range) {
        if (cve.cvss_score < filter.cvss_range.min || cve.cvss_score > filter.cvss_range.max) return false;
      }

      return true;
    });
  }

  static sortCVEs(
    cves: CVE[],
    sortBy: 'id' | 'severity' | 'cvss_score' | 'published_date' | 'risk_score' = 'cvss_score',
    order: 'asc' | 'desc' = 'desc'
  ): CVE[] {
    return [...cves].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'id':
          comparison = a.id.localeCompare(b.id);
          break;
        case 'severity':
          const severityOrder = { 'LOW': 1, 'MEDIUM': 2, 'HIGH': 3, 'CRITICAL': 4 };
          comparison = severityOrder[a.severity] - severityOrder[b.severity];
          break;
        case 'cvss_score':
          comparison = a.cvss_score - b.cvss_score;
          break;
        case 'published_date':
          const aDate = new Date(a.published_date);
          const bDate = new Date(b.published_date);
          comparison = aDate.getTime() - bDate.getTime();
          break;
        case 'risk_score':
          const aRisk = this.calculateRiskScore(a);
          const bRisk = this.calculateRiskScore(b);
          comparison = aRisk - bRisk;
          break;
      }

      return order === 'desc' ? -comparison : comparison;
    });
  }

  static calculateMetrics(cves: CVE[]): CVEMetrics {
    const total = cves.length;

    const bySeverity = cves.reduce((acc, cve) => {
      acc[cve.severity.toLowerCase()] = (acc[cve.severity.toLowerCase()] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byStatus = cves.reduce((acc, cve) => {
      acc[cve.status] = (acc[cve.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const trendingCount = cves.filter(cve => cve.trending).length;
    const patchedCount = cves.filter(cve => cve.patch_available).length;
    const exploitedCount = cves.filter(cve => cve.exploit_available).length;

    const totalCvss = cves.reduce((sum, cve) => sum + cve.cvss_score, 0);
    const averageCvss = total > 0 ? Math.round((totalCvss / total) * 10) / 10 : 0;

    const highRiskCount = cves.filter(cve =>
      cve.severity === 'HIGH' || cve.severity === 'CRITICAL'
    ).length;

    return {
      total_cves: total,
      by_severity: bySeverity,
      by_status: byStatus,
      trending_count: trendingCount,
      patched_count: patchedCount,
      exploited_count: exploitedCount,
      average_cvss: averageCvss,
      high_risk_count: highRiskCount
    };
  }

  static parseCVSSVector(vector: string): Record<string, string> | null {
    if (!vector || !vector.startsWith('CVSS:3.')) {
      return null;
    }

    const parts = vector.split('/');
    const parsed: Record<string, string> = {};

    parts.forEach(part => {
      const [key, value] = part.split(':');
      if (key && value) {
        parsed[key] = value;
      }
    });

    return parsed;
  }

  static validateCVSSScore(score: number, vector?: string): boolean {
    // Basic range validation
    if (score < 0 || score > 10) return false;

    // If vector is provided, validate consistency
    if (vector) {
      const parsed = this.parseCVSSVector(vector);
      if (!parsed) return false;

      // Basic validation - score should match severity implications
      if (score >= 9.0 && parsed.AV !== 'N') {
        // Critical scores typically require network access
        return false;
      }
    }

    return true;
  }

  static generateCVEId(year: number, sequence: number): string {
    return `CVE-${year}-${String(sequence).padStart(4, '0')}`;
  }

  static extractCWECategories(cweIds: string[]): string[] {
    const cweCategories: Record<string, string> = {
      'CWE-78': 'Command Injection',
      'CWE-79': 'Cross-site Scripting',
      'CWE-89': 'SQL Injection',
      'CWE-94': 'Code Injection',
      'CWE-119': 'Buffer Overflow',
      'CWE-200': 'Information Exposure',
      'CWE-269': 'Privilege Escalation',
      'CWE-287': 'Authentication Bypass',
      'CWE-352': 'Cross-Site Request Forgery',
      'CWE-434': 'File Upload'
    };

    return cweIds.map(id => cweCategories[id] || 'Unknown').filter(Boolean);
  }
}

describe('CVE Management TDD Tests', () => {
  describe('Risk Score Calculation', () => {
    it('should calculate base risk score from CVSS score', () => {
      const cve: CVE = {
        id: 'CVE-2024-0001',
        description: 'Test vulnerability',
        severity: 'HIGH',
        cvss_score: 8.5,
        published_date: '2024-01-01',
        status: 'published',
        affected_products: ['Product A'],
        references: [],
        cwe_ids: [],
        affected_applications: 1,
        remediation_status: 'none',
        patch_available: false,
        exploit_available: false,
        trending: false,
        tags: []
      };

      const riskScore = CVEManager.calculateRiskScore(cve);
      expect(riskScore).toBeGreaterThan(8.5); // Should be higher due to no patch
      expect(riskScore).toBeLessThanOrEqual(10);
    });

    it('should increase risk score for exploit availability', () => {
      const baseCVE: CVE = {
        id: 'CVE-2024-0001',
        description: 'Test vulnerability',
        severity: 'HIGH',
        cvss_score: 7.0,
        published_date: '2024-01-01',
        status: 'published',
        affected_products: ['Product A'],
        references: [],
        cwe_ids: [],
        affected_applications: 1,
        remediation_status: 'none',
        patch_available: true,
        exploit_available: false,
        trending: false,
        tags: []
      };

      const exploitCVE: CVE = { ...baseCVE, exploit_available: true };

      const baseScore = CVEManager.calculateRiskScore(baseCVE);
      const exploitScore = CVEManager.calculateRiskScore(exploitCVE);

      expect(exploitScore).toBeGreaterThan(baseScore);
      expect(exploitScore - baseScore).toBe(1.5);
    });

    it('should increase risk score when no patch is available', () => {
      const patchedCVE: CVE = {
        id: 'CVE-2024-0001',
        description: 'Test vulnerability',
        severity: 'HIGH',
        cvss_score: 7.0,
        published_date: '2024-01-01',
        status: 'published',
        affected_products: ['Product A'],
        references: [],
        cwe_ids: [],
        affected_applications: 1,
        remediation_status: 'available',
        patch_available: true,
        exploit_available: false,
        trending: false,
        tags: []
      };

      const unpatchedCVE: CVE = { ...patchedCVE, patch_available: false };

      const patchedScore = CVEManager.calculateRiskScore(patchedCVE);
      const unpatchedScore = CVEManager.calculateRiskScore(unpatchedCVE);

      expect(unpatchedScore).toBeGreaterThan(patchedScore);
      expect(unpatchedScore - patchedScore).toBe(1.0);
    });

    it('should increase risk score for trending CVEs', () => {
      const normalCVE: CVE = {
        id: 'CVE-2024-0001',
        description: 'Test vulnerability',
        severity: 'HIGH',
        cvss_score: 7.0,
        published_date: '2024-01-01',
        status: 'published',
        affected_products: ['Product A'],
        references: [],
        cwe_ids: [],
        affected_applications: 1,
        remediation_status: 'available',
        patch_available: true,
        exploit_available: false,
        trending: false,
        tags: []
      };

      const trendingCVE: CVE = { ...normalCVE, trending: true };

      const normalScore = CVEManager.calculateRiskScore(normalCVE);
      const trendingScore = CVEManager.calculateRiskScore(trendingCVE);

      expect(trendingScore).toBeGreaterThan(normalScore);
      expect(trendingScore - normalScore).toBe(0.5);
    });

    it('should cap risk score at 10', () => {
      const highRiskCVE: CVE = {
        id: 'CVE-2024-0001',
        description: 'Test vulnerability',
        severity: 'CRITICAL',
        cvss_score: 9.8,
        published_date: '2024-01-01',
        status: 'published',
        affected_products: ['Product A'],
        references: [],
        cwe_ids: [],
        affected_applications: 20,
        remediation_status: 'none',
        patch_available: false,
        exploit_available: true,
        trending: true,
        tags: []
      };

      const riskScore = CVEManager.calculateRiskScore(highRiskCVE);
      expect(riskScore).toBe(10);
    });
  });

  describe('Risk Assessment', () => {
    it('should assess critical risk level correctly', () => {
      const criticalCVE: CVE = {
        id: 'CVE-2024-0001',
        description: 'Critical vulnerability',
        severity: 'CRITICAL',
        cvss_score: 9.8,
        published_date: '2024-01-01',
        status: 'published',
        affected_products: ['Product A'],
        references: [],
        cwe_ids: [],
        affected_applications: 5,
        remediation_status: 'none',
        patch_available: false,
        exploit_available: true,
        trending: true,
        tags: []
      };

      const assessment = CVEManager.assessRisk(criticalCVE);

      expect(assessment.risk_level).toBe('critical');
      expect(assessment.risk_score).toBeGreaterThanOrEqual(9.0);
      expect(assessment.priority).toBeGreaterThanOrEqual(9);
      expect(assessment.factors).toContain('Public exploit available');
      expect(assessment.factors).toContain('No patch available');
    });

    it('should provide appropriate recommendations', () => {
      const vulnerableCVE: CVE = {
        id: 'CVE-2024-0001',
        description: 'Test vulnerability',
        severity: 'HIGH',
        cvss_score: 8.0,
        published_date: '2024-01-01',
        status: 'published',
        affected_products: ['Product A'],
        references: [],
        cwe_ids: [],
        affected_applications: 10,
        remediation_status: 'none',
        patch_available: false,
        exploit_available: true,
        trending: false,
        tags: []
      };

      const assessment = CVEManager.assessRisk(vulnerableCVE);

      expect(assessment.recommendations).toContain('Prioritize patching due to active exploitation risk');
      expect(assessment.recommendations).toContain('Implement workarounds and monitoring');
      expect(assessment.recommendations).toContain('Coordinate remediation across all affected systems');
    });

    it('should calculate priority correctly', () => {
      const lowRiskCVE: CVE = {
        id: 'CVE-2024-0001',
        description: 'Low risk vulnerability',
        severity: 'LOW',
        cvss_score: 3.0,
        published_date: '2024-01-01',
        status: 'published',
        affected_products: ['Product A'],
        references: [],
        cwe_ids: [],
        affected_applications: 1,
        remediation_status: 'available',
        patch_available: true,
        exploit_available: false,
        trending: false,
        tags: []
      };

      const assessment = CVEManager.assessRisk(lowRiskCVE);

      expect(assessment.risk_level).toBe('low');
      expect(assessment.priority).toBeLessThanOrEqual(4);
    });
  });

  describe('CVE Filtering', () => {
    const mockCVEs: CVE[] = [
      {
        id: 'CVE-2024-0001',
        description: 'Critical RCE vulnerability',
        severity: 'CRITICAL',
        cvss_score: 9.8,
        published_date: '2024-01-20T10:00:00Z',
        status: 'published',
        affected_products: ['Product A'],
        references: [],
        cwe_ids: ['CWE-78'],
        affected_applications: 5,
        remediation_status: 'available',
        patch_available: true,
        exploit_available: true,
        trending: true,
        tags: ['rce', 'critical']
      },
      {
        id: 'CVE-2024-0002',
        description: 'Medium SQL injection',
        severity: 'MEDIUM',
        cvss_score: 6.5,
        published_date: '2024-01-15T14:00:00Z',
        status: 'published',
        affected_products: ['Product B'],
        references: [],
        cwe_ids: ['CWE-89'],
        affected_applications: 2,
        remediation_status: 'none',
        patch_available: false,
        exploit_available: false,
        trending: false,
        tags: ['sql', 'injection']
      }
    ];

    it('should filter by severity', () => {
      const filtered = CVEManager.filterCVEs(mockCVEs, {
        severity: ['CRITICAL']
      });

      expect(filtered).toHaveLength(1);
      expect(filtered[0].severity).toBe('CRITICAL');
    });

    it('should filter by multiple severities', () => {
      const filtered = CVEManager.filterCVEs(mockCVEs, {
        severity: ['CRITICAL', 'HIGH']
      });

      expect(filtered).toHaveLength(1); // Only critical in our mock data
      expect(['CRITICAL', 'HIGH']).toContain(filtered[0].severity);
    });

    it('should filter by trending status', () => {
      const filtered = CVEManager.filterCVEs(mockCVEs, {
        trending: true
      });

      expect(filtered).toHaveLength(1);
      expect(filtered[0].trending).toBe(true);
    });

    it('should filter by patch availability', () => {
      const filtered = CVEManager.filterCVEs(mockCVEs, {
        patch_available: false
      });

      expect(filtered).toHaveLength(1);
      expect(filtered[0].patch_available).toBe(false);
    });

    it('should filter by search term', () => {
      const filtered = CVEManager.filterCVEs(mockCVEs, {
        search: 'RCE'
      });

      expect(filtered).toHaveLength(1);
      expect(filtered[0].description).toContain('RCE');
    });

    it('should filter by date range', () => {
      const filtered = CVEManager.filterCVEs(mockCVEs, {
        date_range: {
          start: '2024-01-18T00:00:00Z',
          end: '2024-01-25T00:00:00Z'
        }
      });

      expect(filtered).toHaveLength(1);
      expect(filtered[0].id).toBe('CVE-2024-0001');
    });

    it('should filter by CVSS range', () => {
      const filtered = CVEManager.filterCVEs(mockCVEs, {
        cvss_range: {
          min: 9.0,
          max: 10.0
        }
      });

      expect(filtered).toHaveLength(1);
      expect(filtered[0].cvss_score).toBeGreaterThanOrEqual(9.0);
    });

    it('should combine multiple filters', () => {
      const filtered = CVEManager.filterCVEs(mockCVEs, {
        severity: ['CRITICAL'],
        trending: true,
        patch_available: true
      });

      expect(filtered).toHaveLength(1);
      expect(filtered[0].severity).toBe('CRITICAL');
      expect(filtered[0].trending).toBe(true);
      expect(filtered[0].patch_available).toBe(true);
    });

    it('should return empty array when no matches', () => {
      const filtered = CVEManager.filterCVEs(mockCVEs, {
        severity: ['LOW']
      });

      expect(filtered).toHaveLength(0);
    });
  });

  describe('CVE Sorting', () => {
    const mockCVEs: CVE[] = [
      {
        id: 'CVE-2024-0003',
        description: 'Test C',
        severity: 'MEDIUM',
        cvss_score: 6.0,
        published_date: '2024-01-22T10:00:00Z',
        status: 'published',
        affected_products: [],
        references: [],
        cwe_ids: [],
        affected_applications: 1,
        remediation_status: 'none',
        patch_available: false,
        exploit_available: false,
        trending: false,
        tags: []
      },
      {
        id: 'CVE-2024-0001',
        description: 'Test A',
        severity: 'CRITICAL',
        cvss_score: 9.8,
        published_date: '2024-01-20T10:00:00Z',
        status: 'published',
        affected_products: [],
        references: [],
        cwe_ids: [],
        affected_applications: 5,
        remediation_status: 'none',
        patch_available: false,
        exploit_available: true,
        trending: true,
        tags: []
      },
      {
        id: 'CVE-2024-0002',
        description: 'Test B',
        severity: 'HIGH',
        cvss_score: 8.5,
        published_date: '2024-01-21T10:00:00Z',
        status: 'published',
        affected_products: [],
        references: [],
        cwe_ids: [],
        affected_applications: 3,
        remediation_status: 'none',
        patch_available: true,
        exploit_available: false,
        trending: false,
        tags: []
      }
    ];

    it('should sort by CVE ID', () => {
      const sorted = CVEManager.sortCVEs(mockCVEs, 'id', 'asc');

      expect(sorted[0].id).toBe('CVE-2024-0001');
      expect(sorted[1].id).toBe('CVE-2024-0002');
      expect(sorted[2].id).toBe('CVE-2024-0003');
    });

    it('should sort by severity', () => {
      const sorted = CVEManager.sortCVEs(mockCVEs, 'severity', 'desc');

      expect(sorted[0].severity).toBe('CRITICAL');
      expect(sorted[1].severity).toBe('HIGH');
      expect(sorted[2].severity).toBe('MEDIUM');
    });

    it('should sort by CVSS score', () => {
      const sorted = CVEManager.sortCVEs(mockCVEs, 'cvss_score', 'desc');

      expect(sorted[0].cvss_score).toBe(9.8);
      expect(sorted[1].cvss_score).toBe(8.5);
      expect(sorted[2].cvss_score).toBe(6.0);
    });

    it('should sort by published date', () => {
      const sorted = CVEManager.sortCVEs(mockCVEs, 'published_date', 'desc');

      expect(sorted[0].published_date).toBe('2024-01-22T10:00:00Z');
      expect(sorted[1].published_date).toBe('2024-01-21T10:00:00Z');
      expect(sorted[2].published_date).toBe('2024-01-20T10:00:00Z');
    });

    it('should sort by risk score', () => {
      const sorted = CVEManager.sortCVEs(mockCVEs, 'risk_score', 'desc');

      // CVE-2024-0001 should have highest risk (exploit + trending + no patch)
      expect(sorted[0].id).toBe('CVE-2024-0001');
    });
  });
});