import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import CVEsPage from '@/app/cves/page';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
}));

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('CVEs Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders CVEs page with correct title', async () => {
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      expect(screen.getByText('CVE Feed')).toBeInTheDocument();
    });
  });

  it('displays severity statistics cards', async () => {
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Critical')).toBeInTheDocument();
      expect(screen.getByText('High')).toBeInTheDocument();
      expect(screen.getByText('Medium')).toBeInTheDocument();
      expect(screen.getByText('Low')).toBeInTheDocument();
    });
  });

  it('shows search and filter interface', async () => {
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Filters & Search')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Search CVEs, descriptions...')).toBeInTheDocument();
    });
  });

  it('allows searching for CVEs', async () => {
    const user = userEvent.setup();
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('Search CVEs, descriptions...');
      expect(searchInput).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText('Search CVEs, descriptions...');
    await user.type(searchInput, 'CVE-2024-0001');
    
    expect(searchInput).toHaveValue('CVE-2024-0001');
  });

  it('filters CVEs by severity', async () => {
    const user = userEvent.setup();
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      const criticalButton = screen.getByRole('button', { name: /CRITICAL/i });
      expect(criticalButton).toBeInTheDocument();
    });
    
    const criticalButton = screen.getByRole('button', { name: /CRITICAL/i });
    await user.click(criticalButton);
    
    expect(criticalButton).toHaveClass('bg-red-600');
  });

  it('displays CVE cards with proper information', async () => {
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      // Should show CVE cards after loading
      const cveCards = screen.getAllByTestId(/cve-card/i);
      expect(cveCards.length).toBeGreaterThan(0);
    });
  });

  it('shows loading state initially', () => {
    renderWithProviders(<CVEsPage />);
    
    // Should show loading skeletons
    const loadingElements = screen.getAllByTestId(/loading/i);
    expect(loadingElements.length).toBeGreaterThan(0);
  });

  it('handles empty search results', async () => {
    const user = userEvent.setup();
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('Search CVEs, descriptions...');
      expect(searchInput).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText('Search CVEs, descriptions...');
    await user.type(searchInput, 'nonexistent-cve');
    
    await waitFor(() => {
      expect(screen.getByText('No CVEs Found')).toBeInTheDocument();
    });
  });

  it('supports pagination', async () => {
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      // Check for pagination controls
      const nextButton = screen.getByRole('button', { name: /Next/i });
      const prevButton = screen.getByRole('button', { name: /Previous/i });
      
      expect(nextButton).toBeInTheDocument();
      expect(prevButton).toBeInTheDocument();
    });
  });

  it('allows sorting CVEs', async () => {
    const user = userEvent.setup();
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      const sortSelect = screen.getByDisplayValue('Published Date');
      expect(sortSelect).toBeInTheDocument();
    });
    
    const sortSelect = screen.getByDisplayValue('Published Date');
    await user.click(sortSelect);
    
    await waitFor(() => {
      expect(screen.getByText('CVSS Score')).toBeInTheDocument();
    });
  });

  it('shows watch list functionality', async () => {
    const user = userEvent.setup();
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      const watchButtons = screen.getAllByTestId(/watch-button/i);
      expect(watchButtons.length).toBeGreaterThan(0);
    });
    
    const watchButton = screen.getAllByTestId(/watch-button/i)[0];
    await user.click(watchButton);
    
    // Should update watch status
    expect(watchButton).toHaveAttribute('aria-pressed');
  });

  it('displays export functionality', async () => {
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      const exportButton = screen.getByRole('button', { name: /Export/i });
      expect(exportButton).toBeInTheDocument();
    });
  });

  it('shows refresh functionality', async () => {
    const user = userEvent.setup();
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      const refreshButton = screen.getByRole('button', { name: /Refresh/i });
      expect(refreshButton).toBeInTheDocument();
    });
    
    const refreshButton = screen.getByRole('button', { name: /Refresh/i });
    await user.click(refreshButton);
    
    // Should trigger refresh
    expect(refreshButton).toBeInTheDocument();
  });

  it('filters by date range', async () => {
    const user = userEvent.setup();
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      const dateSelect = screen.getByDisplayValue('All Time');
      expect(dateSelect).toBeInTheDocument();
    });
    
    const dateSelect = screen.getByDisplayValue('All Time');
    await user.click(dateSelect);
    
    await waitFor(() => {
      expect(screen.getByText('Last 7 Days')).toBeInTheDocument();
    });
  });

  it('displays results summary', async () => {
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      const summary = screen.getByText(/Showing \d+ of \d+ CVEs/);
      expect(summary).toBeInTheDocument();
    });
  });

  it('shows watched CVEs count', async () => {
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      const watchedCount = screen.getByText(/Watched: \d+/);
      expect(watchedCount).toBeInTheDocument();
    });
  });

  it('provides clear filters functionality', async () => {
    const user = userEvent.setup();
    renderWithProviders(<CVEsPage />);
    
    // First apply some filters
    await waitFor(() => {
      const criticalButton = screen.getByRole('button', { name: /CRITICAL/i });
      expect(criticalButton).toBeInTheDocument();
    });
    
    const criticalButton = screen.getByRole('button', { name: /CRITICAL/i });
    await user.click(criticalButton);
    
    // Then clear filters
    await waitFor(() => {
      const clearButton = screen.getByRole('button', { name: /Clear Filters/i });
      expect(clearButton).toBeInTheDocument();
    });
  });

  it('handles keyboard navigation', async () => {
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('Search CVEs, descriptions...');
      expect(searchInput).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText('Search CVEs, descriptions...');
    
    // Should be focusable
    searchInput.focus();
    expect(document.activeElement).toBe(searchInput);
  });

  it('displays correct accessibility attributes', async () => {
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      const page = screen.getByTestId('cves-page');
      expect(page).toHaveAttribute('aria-label');
    });
  });

  it('shows proper error handling', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      expect(screen.getByTestId('cves-page')).toBeInTheDocument();
    });
    
    consoleSpy.mockRestore();
  });

  it('supports responsive design', async () => {
    renderWithProviders(<CVEsPage />);
    
    await waitFor(() => {
      const container = screen.getByTestId('cves-page');
      expect(container).toHaveClass('space-y-6');
    });
  });
});
