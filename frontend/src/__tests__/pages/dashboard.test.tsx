import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useAuthStore } from '@/stores/auth-store';
import DashboardPage from '@/app/dashboard/page';

// Mock the auth store
jest.mock('@/stores/auth-store');
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>;

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
}));

// Mock chart components
jest.mock('recharts', () => ({
  ResponsiveContainer: ({ children }: any) => <div data-testid="chart-container">{children}</div>,
  LineChart: ({ children }: any) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
}));

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('Dashboard Page', () => {
  beforeEach(() => {
    mockUseAuthStore.mockReturnValue({
      user: {
        id: '1',
        username: '<EMAIL>',
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'security_analyst',
        is_active: true,
        is_superuser: false,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: null,
      },
      token: 'mock-token',
      isAuthenticated: true,
      login: jest.fn(),
      logout: jest.fn(),
      setUser: jest.fn(),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders dashboard page with correct title', async () => {
    renderWithProviders(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Security Dashboard')).toBeInTheDocument();
    });
  });

  it('displays key security metrics cards', async () => {
    renderWithProviders(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Total CVEs')).toBeInTheDocument();
      expect(screen.getByText('Critical')).toBeInTheDocument();
      expect(screen.getByText('High Risk')).toBeInTheDocument();
      expect(screen.getByText('Applications')).toBeInTheDocument();
    });
  });

  it('shows recent CVEs section', async () => {
    renderWithProviders(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Recent CVEs')).toBeInTheDocument();
    });
  });

  it('displays system status panel', async () => {
    renderWithProviders(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText('System Status')).toBeInTheDocument();
    });
  });

  it('shows quick actions section', async () => {
    renderWithProviders(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    });
  });

  it('renders vulnerability trend chart', async () => {
    renderWithProviders(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByTestId('chart-container')).toBeInTheDocument();
    });
  });

  it('displays loading state initially', () => {
    renderWithProviders(<DashboardPage />);
    
    // Should show loading skeletons
    const loadingElements = screen.getAllByTestId(/loading/i);
    expect(loadingElements.length).toBeGreaterThan(0);
  });

  it('handles unauthenticated user', () => {
    mockUseAuthStore.mockReturnValue({
      user: null,
      token: null,
      isAuthenticated: false,
      login: jest.fn(),
      logout: jest.fn(),
      setUser: jest.fn(),
    });

    renderWithProviders(<DashboardPage />);
    
    // Should redirect or show login prompt
    expect(screen.queryByText('Security Dashboard')).not.toBeInTheDocument();
  });

  it('displays correct data testids for automation', async () => {
    renderWithProviders(<DashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByTestId('dashboard-page')).toBeInTheDocument();
      expect(screen.getByTestId('metrics-cards')).toBeInTheDocument();
      expect(screen.getByTestId('recent-cves')).toBeInTheDocument();
      expect(screen.getByTestId('system-status')).toBeInTheDocument();
    });
  });

  it('shows trend indicators with correct styling', async () => {
    renderWithProviders(<DashboardPage />);
    
    await waitFor(() => {
      // Check for trend arrows
      const trendElements = screen.getAllByTestId(/trend/i);
      expect(trendElements.length).toBeGreaterThan(0);
    });
  });

  it('renders responsive layout correctly', async () => {
    renderWithProviders(<DashboardPage />);
    
    await waitFor(() => {
      const container = screen.getByTestId('dashboard-page');
      expect(container).toHaveClass('space-y-6');
    });
  });

  it('displays correct accessibility attributes', async () => {
    renderWithProviders(<DashboardPage />);
    
    await waitFor(() => {
      const mainHeading = screen.getByRole('heading', { level: 1 });
      expect(mainHeading).toHaveTextContent('Security Dashboard');
    });
  });

  it('shows proper error handling for failed data loads', async () => {
    // Mock a failed API call scenario
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    renderWithProviders(<DashboardPage />);
    
    // Should handle errors gracefully
    await waitFor(() => {
      expect(screen.getByTestId('dashboard-page')).toBeInTheDocument();
    });
    
    consoleSpy.mockRestore();
  });

  it('updates metrics in real-time', async () => {
    renderWithProviders(<DashboardPage />);
    
    await waitFor(() => {
      // Check that metrics are displayed
      expect(screen.getByText('Total CVEs')).toBeInTheDocument();
    });
    
    // Simulate real-time update
    await waitFor(() => {
      const metrics = screen.getAllByTestId(/metric-/i);
      expect(metrics.length).toBeGreaterThan(0);
    });
  });

  it('provides keyboard navigation support', async () => {
    renderWithProviders(<DashboardPage />);
    
    await waitFor(() => {
      const interactiveElements = screen.getAllByRole('button');
      interactiveElements.forEach(element => {
        expect(element).toHaveAttribute('tabIndex');
      });
    });
  });

  it('displays correct ARIA labels for screen readers', async () => {
    renderWithProviders(<DashboardPage />);
    
    await waitFor(() => {
      const dashboard = screen.getByTestId('dashboard-page');
      expect(dashboard).toHaveAttribute('aria-label');
    });
  });
});
