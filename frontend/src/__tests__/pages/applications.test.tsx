import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ApplicationsPage from '@/app/applications/page';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
}));

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('Applications Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders applications page with correct title', async () => {
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Applications')).toBeInTheDocument();
    });
  });

  it('displays application statistics cards', async () => {
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Total Apps')).toBeInTheDocument();
      expect(screen.getByText('Production')).toBeInTheDocument();
      expect(screen.getByText('Critical Risk')).toBeInTheDocument();
      expect(screen.getByText('Active')).toBeInTheDocument();
    });
  });

  it('shows add application button', async () => {
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      const addButton = screen.getByRole('button', { name: /Add Application/i });
      expect(addButton).toBeInTheDocument();
    });
  });

  it('opens create application dialog', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      const addButton = screen.getByRole('button', { name: /Add Application/i });
      expect(addButton).toBeInTheDocument();
    });
    
    const addButton = screen.getByRole('button', { name: /Add Application/i });
    await user.click(addButton);
    
    await waitFor(() => {
      expect(screen.getByText('Create New Application')).toBeInTheDocument();
    });
  });

  it('displays filters and search interface', async () => {
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Filters & Search')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Search applications...')).toBeInTheDocument();
    });
  });

  it('allows searching for applications', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('Search applications...');
      expect(searchInput).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText('Search applications...');
    await user.type(searchInput, 'Customer Portal');
    
    expect(searchInput).toHaveValue('Customer Portal');
  });

  it('filters applications by environment', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      const environmentSelect = screen.getByDisplayValue('All Environments');
      expect(environmentSelect).toBeInTheDocument();
    });
    
    const environmentSelect = screen.getByDisplayValue('All Environments');
    await user.click(environmentSelect);
    
    await waitFor(() => {
      expect(screen.getByText('Production')).toBeInTheDocument();
    });
  });

  it('filters applications by criticality', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      const criticalitySelect = screen.getByDisplayValue('All Levels');
      expect(criticalitySelect).toBeInTheDocument();
    });
    
    const criticalitySelect = screen.getByDisplayValue('All Levels');
    await user.click(criticalitySelect);
    
    await waitFor(() => {
      expect(screen.getByText('Critical')).toBeInTheDocument();
    });
  });

  it('displays application cards with proper information', async () => {
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      // Should show application cards after loading
      const appCards = screen.getAllByTestId(/application-card/i);
      expect(appCards.length).toBeGreaterThan(0);
    });
  });

  it('shows loading state initially', () => {
    renderWithProviders(<ApplicationsPage />);
    
    // Should show loading skeletons
    const loadingElements = screen.getAllByTestId(/loading/i);
    expect(loadingElements.length).toBeGreaterThan(0);
  });

  it('handles empty search results', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('Search applications...');
      expect(searchInput).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText('Search applications...');
    await user.type(searchInput, 'nonexistent-app');
    
    await waitFor(() => {
      expect(screen.getByText('No Applications Found')).toBeInTheDocument();
    });
  });

  it('shows edit application functionality', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      const editButtons = screen.getAllByTestId(/edit-button/i);
      expect(editButtons.length).toBeGreaterThan(0);
    });
    
    const editButton = screen.getAllByTestId(/edit-button/i)[0];
    await user.click(editButton);
    
    await waitFor(() => {
      expect(screen.getByText('Edit Application')).toBeInTheDocument();
    });
  });

  it('shows delete application functionality', async () => {
    const user = userEvent.setup();
    
    // Mock window.confirm
    const confirmSpy = jest.spyOn(window, 'confirm').mockReturnValue(true);
    
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      const deleteButtons = screen.getAllByTestId(/delete-button/i);
      expect(deleteButtons.length).toBeGreaterThan(0);
    });
    
    const deleteButton = screen.getAllByTestId(/delete-button/i)[0];
    await user.click(deleteButton);
    
    expect(confirmSpy).toHaveBeenCalled();
    
    confirmSpy.mockRestore();
  });

  it('displays vulnerability counts for applications', async () => {
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Vulnerabilities')).toBeInTheDocument();
      expect(screen.getByText('Critical')).toBeInTheDocument();
      expect(screen.getByText('High')).toBeInTheDocument();
    });
  });

  it('shows technology stack information', async () => {
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Technologies')).toBeInTheDocument();
    });
  });

  it('displays contact information', async () => {
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      // Should show contact person and business unit
      const contactElements = screen.getAllByTestId(/contact/i);
      expect(contactElements.length).toBeGreaterThan(0);
    });
  });

  it('shows application status badges', async () => {
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      const statusBadges = screen.getAllByTestId(/status-badge/i);
      expect(statusBadges.length).toBeGreaterThan(0);
    });
  });

  it('provides external link functionality', async () => {
    const user = userEvent.setup();
    
    // Mock window.open
    const openSpy = jest.spyOn(window, 'open').mockImplementation(() => null);
    
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      const linkButtons = screen.getAllByTestId(/external-link/i);
      expect(linkButtons.length).toBeGreaterThan(0);
    });
    
    const linkButton = screen.getAllByTestId(/external-link/i)[0];
    await user.click(linkButton);
    
    expect(openSpy).toHaveBeenCalled();
    
    openSpy.mockRestore();
  });

  it('filters by business unit', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      const businessUnitSelect = screen.getByDisplayValue('All Units');
      expect(businessUnitSelect).toBeInTheDocument();
    });
    
    const businessUnitSelect = screen.getByDisplayValue('All Units');
    await user.click(businessUnitSelect);
    
    await waitFor(() => {
      // Should show business unit options
      const options = screen.getAllByRole('option');
      expect(options.length).toBeGreaterThan(1);
    });
  });

  it('displays results summary', async () => {
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      const summary = screen.getByText(/Showing \d+ of \d+ applications/);
      expect(summary).toBeInTheDocument();
    });
  });

  it('provides clear filters functionality', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ApplicationsPage />);
    
    // Apply filters first
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('Search applications...');
      expect(searchInput).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText('Search applications...');
    await user.type(searchInput, 'test');
    
    // Then clear filters
    await waitFor(() => {
      const clearButton = screen.getByRole('button', { name: /Clear Filters/i });
      expect(clearButton).toBeInTheDocument();
    });
  });

  it('handles keyboard navigation', async () => {
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('Search applications...');
      expect(searchInput).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText('Search applications...');
    
    // Should be focusable
    searchInput.focus();
    expect(document.activeElement).toBe(searchInput);
  });

  it('displays correct accessibility attributes', async () => {
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      const page = screen.getByTestId('applications-page');
      expect(page).toHaveAttribute('aria-label');
    });
  });

  it('shows proper error handling', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      expect(screen.getByTestId('applications-page')).toBeInTheDocument();
    });
    
    consoleSpy.mockRestore();
  });

  it('supports responsive design', async () => {
    renderWithProviders(<ApplicationsPage />);
    
    await waitFor(() => {
      const container = screen.getByTestId('applications-page');
      expect(container).toHaveClass('space-y-6');
    });
  });
});
