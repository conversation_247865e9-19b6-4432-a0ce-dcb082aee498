/**
 * Dashboard Playwright E2E Tests
 * End-to-end testing for dashboard functionality
 */

import { test, expect, Page } from '@playwright/test';

// Test data and utilities
const TEST_USER = {
  email: '<EMAIL>',
  password: 'password123'
};

const DASHBOARD_URL = '/dashboard';
const API_BASE = 'http://localhost:8001/api/v1';

// Helper functions
async function loginUser(page: Page) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', TEST_USER.email);
  await page.fill('[data-testid="password-input"]', TEST_USER.password);
  await page.click('[data-testid="login-button"]');
  await page.waitForURL('/dashboard');
}

async function waitForDashboardLoad(page: Page) {
  await page.waitForSelector('[data-testid="dashboard-container"]');
  await page.waitForLoadState('networkidle');
}

test.describe('Dashboard E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route(`${API_BASE}/dashboard/metrics`, async route => {
      await route.fulfill({
        json: {
          total_cves: 1247,
          critical_cves: 23,
          high_cves: 156,
          medium_cves: 489,
          low_cves: 579,
          total_applications: 45,
          vulnerable_applications: 12,
          compliance_score: 87.5,
          last_updated: '2024-01-22T10:30:00Z',
          trends: {
            cves_this_week: 15,
            cves_last_week: 12
          },
          top_vulnerabilities: [
            {
              id: 'CVE-2024-0001',
              severity: 'CRITICAL',
              score: 9.8,
              affected_apps: 3,
              description: 'Critical remote code execution vulnerability'
            }
          ],
          recent_activities: [
            {
              id: '1',
              type: 'cve_discovered',
              description: 'New critical CVE discovered',
              timestamp: '2024-01-22T09:15:00Z',
              user: 'system'
            }
          ]
        }
      });
    });

    await page.route(`${API_BASE}/dashboard/trends*`, async route => {
      await route.fulfill({
        json: [
          { date: '2024-01-15', critical: 20, high: 145, medium: 478, low: 567 },
          { date: '2024-01-16', critical: 21, high: 148, medium: 482, low: 571 },
          { date: '2024-01-17', critical: 22, high: 152, medium: 485, low: 575 },
          { date: '2024-01-18', critical: 23, high: 155, medium: 487, low: 577 },
          { date: '2024-01-19', critical: 23, high: 156, medium: 489, low: 579 }
        ]
      });
    });

    await loginUser(page);
  });

  test('should display dashboard with all metric cards', async ({ page }) => {
    await waitForDashboardLoad(page);

    // Check main heading
    await expect(page.getByRole('heading', { name: /security dashboard/i })).toBeVisible();

    // Check metric cards
    await expect(page.getByTestId('total-cves-card')).toBeVisible();
    await expect(page.getByTestId('critical-cves-card')).toBeVisible();
    await expect(page.getByTestId('high-cves-card')).toBeVisible();
    await expect(page.getByTestId('compliance-score-card')).toBeVisible();

    // Check metric values
    await expect(page.getByText('1,247')).toBeVisible(); // Total CVEs
    await expect(page.getByText('23')).toBeVisible(); // Critical CVEs
    await expect(page.getByText('87.5%')).toBeVisible(); // Compliance score
  });

  test('should display vulnerability trends chart', async ({ page }) => {
    await waitForDashboardLoad(page);

    // Check trends section
    await expect(page.getByTestId('vulnerability-trends-section')).toBeVisible();
    await expect(page.getByTestId('vulnerability-trends-chart')).toBeVisible();

    // Check trend indicators
    await expect(page.getByTestId('trend-indicator')).toBeVisible();
    
    // Check time period selector
    await expect(page.getByTestId('time-period-select')).toBeVisible();
  });

  test('should display top vulnerabilities list', async ({ page }) => {
    await waitForDashboardLoad(page);

    // Check top vulnerabilities section
    await expect(page.getByTestId('top-vulnerabilities-section')).toBeVisible();
    
    // Check vulnerability item
    await expect(page.getByText('CVE-2024-0001')).toBeVisible();
    await expect(page.getByText('9.8')).toBeVisible();
    await expect(page.getByText('CRITICAL')).toBeVisible();
    await expect(page.getByText('3 apps affected')).toBeVisible();
  });

  test('should display recent activities', async ({ page }) => {
    await waitForDashboardLoad(page);

    // Check recent activities section
    await expect(page.getByTestId('recent-activities-section')).toBeVisible();
    
    // Check activity item
    await expect(page.getByText('New critical CVE discovered')).toBeVisible();
    await expect(page.getByTestId('activity-timestamp')).toBeVisible();
  });

  test('should navigate to CVE details when clicking vulnerability', async ({ page }) => {
    await waitForDashboardLoad(page);

    // Click on vulnerability link
    await page.click('[data-testid="vulnerability-link-CVE-2024-0001"]');
    
    // Should navigate to CVE details page
    await expect(page).toHaveURL('/cves/CVE-2024-0001');
  });

  test('should navigate to filtered CVE list when clicking metric card', async ({ page }) => {
    await waitForDashboardLoad(page);

    // Click on critical CVEs card
    await page.click('[data-testid="critical-cves-card"]');
    
    // Should navigate to CVE list with critical filter
    await expect(page).toHaveURL('/cves?severity=critical');
  });

  test('should update trends when changing time period', async ({ page }) => {
    await waitForDashboardLoad(page);

    // Click time period selector
    await page.click('[data-testid="time-period-select"]');
    
    // Select 30 days option
    await page.click('[data-testid="time-period-30-days"]');
    
    // Should make API call with new period
    await page.waitForRequest(request => 
      request.url().includes('/dashboard/trends') && 
      request.url().includes('days=30')
    );
    
    // Chart should update
    await expect(page.getByTestId('vulnerability-trends-chart')).toBeVisible();
  });

  test('should refresh data when clicking refresh button', async ({ page }) => {
    await waitForDashboardLoad(page);

    // Click refresh button
    await page.click('[data-testid="refresh-button"]');
    
    // Should show loading state
    await expect(page.getByTestId('loading-indicator')).toBeVisible();
    
    // Should make API calls to refresh data
    await page.waitForRequest(request => 
      request.url().includes('/dashboard/metrics')
    );
    
    // Loading should disappear
    await expect(page.getByTestId('loading-indicator')).not.toBeVisible();
  });

  test('should handle responsive design on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await waitForDashboardLoad(page);

    // Check mobile layout
    const container = page.getByTestId('dashboard-container');
    await expect(container).toBeVisible();
    
    // Metric cards should stack vertically on mobile
    const metricCards = page.getByTestId(/.*-card$/);
    const firstCard = metricCards.first();
    const secondCard = metricCards.nth(1);
    
    const firstCardBox = await firstCard.boundingBox();
    const secondCardBox = await secondCard.boundingBox();
    
    // Second card should be below first card (not side by side)
    expect(secondCardBox?.y).toBeGreaterThan(firstCardBox?.y + firstCardBox?.height);
  });

  test('should support keyboard navigation', async ({ page }) => {
    await waitForDashboardLoad(page);

    // Tab through interactive elements
    await page.keyboard.press('Tab');
    await expect(page.getByTestId('critical-cves-card')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.getByTestId('high-cves-card')).toBeFocused();
    
    // Enter should activate focused element
    await page.keyboard.press('Enter');
    await expect(page).toHaveURL('/cves?severity=high');
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Mock API error
    await page.route(`${API_BASE}/dashboard/metrics`, async route => {
      await route.fulfill({
        status: 500,
        json: { error: 'Internal server error' }
      });
    });

    await page.goto(DASHBOARD_URL);
    
    // Should show error message
    await expect(page.getByText(/error loading dashboard/i)).toBeVisible();
    await expect(page.getByText(/try again/i)).toBeVisible();
    
    // Retry button should work
    await page.click('[data-testid="retry-button"]');
    await page.waitForRequest(request => 
      request.url().includes('/dashboard/metrics')
    );
  });

  test('should show loading states during data fetch', async ({ page }) => {
    // Add delay to API response
    await page.route(`${API_BASE}/dashboard/metrics`, async route => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.fulfill({
        json: {
          total_cves: 1247,
          critical_cves: 23,
          compliance_score: 87.5
        }
      });
    });

    await page.goto(DASHBOARD_URL);
    
    // Should show loading skeletons
    await expect(page.getByTestId('metrics-skeleton')).toBeVisible();
    
    // Loading should disappear when data loads
    await expect(page.getByTestId('metrics-skeleton')).not.toBeVisible({ timeout: 2000 });
    await expect(page.getByText('1,247')).toBeVisible();
  });

  test('should export dashboard data', async ({ page }) => {
    await waitForDashboardLoad(page);

    // Click export button
    await page.click('[data-testid="export-button"]');
    
    // Should show export options
    await expect(page.getByTestId('export-menu')).toBeVisible();
    
    // Click PDF export
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="export-pdf"]');
    
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toContain('dashboard');
    expect(download.suggestedFilename()).toContain('.pdf');
  });

  test('should show real-time updates', async ({ page }) => {
    await waitForDashboardLoad(page);

    // Simulate real-time update
    await page.route(`${API_BASE}/dashboard/metrics`, async route => {
      await route.fulfill({
        json: {
          total_cves: 1250, // Updated value
          critical_cves: 25, // Updated value
          compliance_score: 86.8
        }
      });
    });

    // Trigger update (could be via WebSocket or polling)
    await page.evaluate(() => {
      window.dispatchEvent(new CustomEvent('dashboard-update'));
    });

    // Should show updated values
    await expect(page.getByText('1,250')).toBeVisible();
    await expect(page.getByText('25')).toBeVisible();
  });

  test('should maintain accessibility standards', async ({ page }) => {
    await waitForDashboardLoad(page);

    // Check for proper heading structure
    const headings = page.getByRole('heading');
    await expect(headings.first()).toHaveAttribute('aria-level', '1');

    // Check for ARIA labels on interactive elements
    const metricCards = page.getByTestId(/.*-card$/);
    for (const card of await metricCards.all()) {
      await expect(card).toHaveAttribute('aria-label');
    }

    // Check for live regions for dynamic content
    await expect(page.getByTestId('metrics-live-region')).toHaveAttribute('aria-live');

    // Check color contrast (basic check)
    const criticalBadge = page.getByText('CRITICAL');
    const backgroundColor = await criticalBadge.evaluate(el => 
      getComputedStyle(el).backgroundColor
    );
    const color = await criticalBadge.evaluate(el => 
      getComputedStyle(el).color
    );
    
    // Should have sufficient contrast (basic check)
    expect(backgroundColor).not.toBe(color);
  });

  test('should handle offline functionality', async ({ page, context }) => {
    await waitForDashboardLoad(page);

    // Go offline
    await context.setOffline(true);

    // Should show offline indicator
    await expect(page.getByTestId('offline-indicator')).toBeVisible();

    // Should still show cached data
    await expect(page.getByText('1,247')).toBeVisible();

    // Go back online
    await context.setOffline(false);

    // Offline indicator should disappear
    await expect(page.getByTestId('offline-indicator')).not.toBeVisible();
  });
});
