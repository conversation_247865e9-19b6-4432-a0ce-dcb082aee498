/**
 * CVE Management Playwright E2E Tests
 * End-to-end testing for CVE management functionality
 */

import { test, expect, Page } from '@playwright/test';

// Test data and utilities
const TEST_USER = {
  email: '<EMAIL>',
  password: 'password123'
};

const API_BASE = 'http://localhost:8001/api/v1';

const mockCVEs = [
  {
    id: 'CVE-2024-0001',
    description: 'Critical remote code execution vulnerability in web framework',
    severity: 'CRITICAL',
    cvss_score: 9.8,
    published_date: '2024-01-20T10:00:00Z',
    status: 'published',
    affected_applications: 5,
    patch_available: true,
    exploit_available: true,
    trending: true,
    tags: ['rce', 'critical', 'web']
  },
  {
    id: 'CVE-2024-0002',
    description: 'High severity privilege escalation in authentication module',
    severity: 'HIGH',
    cvss_score: 8.5,
    published_date: '2024-01-19T14:00:00Z',
    status: 'published',
    affected_applications: 3,
    patch_available: false,
    exploit_available: false,
    trending: false,
    tags: ['privilege-escalation', 'auth']
  }
];

const mockCVEDetails = {
  ...mockCVEs[0],
  technical_details: {
    attack_vector: 'Network',
    attack_complexity: 'Low',
    privileges_required: 'None',
    user_interaction: 'None',
    scope: 'Unchanged',
    confidentiality_impact: 'High',
    integrity_impact: 'High',
    availability_impact: 'High'
  },
  affected_versions: [
    {
      product: 'WebFramework',
      vendor: 'Example Corp',
      versions: ['2.0', '2.1', '2.2'],
      fixed_versions: ['2.3']
    }
  ],
  remediation: {
    patches: [
      {
        version: '2.3',
        release_date: '2024-01-22T00:00:00Z',
        download_url: 'https://example.com/patches/v2.3'
      }
    ],
    workarounds: ['Disable affected module until patch is applied'],
    mitigations: ['Deploy Web Application Firewall rules']
  }
};

// Helper functions
async function loginUser(page: Page) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', TEST_USER.email);
  await page.fill('[data-testid="password-input"]', TEST_USER.password);
  await page.click('[data-testid="login-button"]');
  await page.waitForURL('/dashboard');
}

async function navigateToCVEs(page: Page) {
  await page.click('[data-testid="nav-cves"]');
  await page.waitForURL('/cves');
  await page.waitForSelector('[data-testid="cve-list-container"]');
}

test.describe('CVE Management E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route(`${API_BASE}/cves*`, async route => {
      const url = route.request().url();
      
      if (url.includes('severity=critical')) {
        await route.fulfill({
          json: {
            cves: mockCVEs.filter(cve => cve.severity === 'CRITICAL'),
            total: 1
          }
        });
      } else if (url.includes('trending=true')) {
        await route.fulfill({
          json: {
            cves: mockCVEs.filter(cve => cve.trending),
            total: 1
          }
        });
      } else {
        await route.fulfill({
          json: {
            cves: mockCVEs,
            total: mockCVEs.length
          }
        });
      }
    });

    await page.route(`${API_BASE}/cves/CVE-2024-0001`, async route => {
      await route.fulfill({
        json: mockCVEDetails
      });
    });

    await page.route(`${API_BASE}/cves/stats`, async route => {
      await route.fulfill({
        json: {
          total_cves: 1247,
          by_severity: {
            critical: 23,
            high: 156,
            medium: 489,
            low: 579
          },
          trending_cves: 15
        }
      });
    });

    await loginUser(page);
  });

  test('should display CVE list with essential information', async ({ page }) => {
    await navigateToCVEs(page);

    // Check page title and structure
    await expect(page.getByRole('heading', { name: /cve management/i })).toBeVisible();
    await expect(page.getByTestId('cve-list-container')).toBeVisible();

    // Check CVE items
    await expect(page.getByTestId('cve-item-CVE-2024-0001')).toBeVisible();
    await expect(page.getByText('CVE-2024-0001')).toBeVisible();
    await expect(page.getByText('CRITICAL')).toBeVisible();
    await expect(page.getByText('9.8')).toBeVisible();

    // Check status badges
    await expect(page.getByTestId('trending-badge')).toBeVisible();
    await expect(page.getByTestId('patch-available-badge')).toBeVisible();
    await expect(page.getByTestId('exploit-available-badge')).toBeVisible();
  });

  test('should filter CVEs by severity', async ({ page }) => {
    await navigateToCVEs(page);

    // Apply severity filter
    await page.click('[data-testid="severity-filter"]');
    await page.click('[data-testid="severity-critical"]');

    // Should make API call with severity filter
    await page.waitForRequest(request => 
      request.url().includes('/cves') && 
      request.url().includes('severity=critical')
    );

    // Should show only critical CVEs
    await expect(page.getByTestId('cve-item-CVE-2024-0001')).toBeVisible();
    await expect(page.getByTestId('cve-item-CVE-2024-0002')).not.toBeVisible();

    // Filter indicator should be active
    await expect(page.getByTestId('active-filter-critical')).toBeVisible();
  });

  test('should search CVEs by keyword', async ({ page }) => {
    await navigateToCVEs(page);

    // Enter search term
    await page.fill('[data-testid="cve-search-input"]', 'remote code execution');
    await page.keyboard.press('Enter');

    // Should make API call with search parameter
    await page.waitForRequest(request => 
      request.url().includes('/cves') && 
      request.url().includes('search=remote%20code%20execution')
    );

    // Should highlight search terms in results
    await expect(page.getByTestId('search-highlight')).toBeVisible();
    await expect(page.getByTestId('search-results-count')).toBeVisible();
  });

  test('should sort CVEs by different criteria', async ({ page }) => {
    await navigateToCVEs(page);

    // Sort by CVSS score descending
    await page.click('[data-testid="sort-dropdown"]');
    await page.click('[data-testid="sort-cvss-desc"]');

    // Should make API call with sort parameter
    await page.waitForRequest(request => 
      request.url().includes('/cves') && 
      request.url().includes('sort=cvss_score&order=desc')
    );

    // Sort indicator should show current sort
    await expect(page.getByTestId('sort-indicator-cvss-desc')).toBeVisible();

    // Change to date ascending
    await page.click('[data-testid="sort-dropdown"]');
    await page.click('[data-testid="sort-date-asc"]');

    await expect(page.getByTestId('sort-indicator-date-asc')).toBeVisible();
  });

  test('should navigate to CVE details page', async ({ page }) => {
    await navigateToCVEs(page);

    // Click on CVE ID link
    await page.click('[data-testid="cve-link-CVE-2024-0001"]');

    // Should navigate to details page
    await expect(page).toHaveURL('/cves/CVE-2024-0001');
    await expect(page.getByTestId('cve-details-container')).toBeVisible();
  });

  test('should display comprehensive CVE details', async ({ page }) => {
    await page.goto('/cves/CVE-2024-0001');
    await page.waitForSelector('[data-testid="cve-details-container"]');

    // Check main information
    await expect(page.getByTestId('cve-title')).toHaveText('CVE-2024-0001');
    await expect(page.getByTestId('severity-indicator')).toHaveText('CRITICAL');
    await expect(page.getByTestId('cvss-score')).toHaveText('9.8');

    // Check technical details section
    await expect(page.getByTestId('technical-details-section')).toBeVisible();
    await expect(page.getByText('Attack Vector: Network')).toBeVisible();
    await expect(page.getByText('Attack Complexity: Low')).toBeVisible();

    // Check affected products
    await expect(page.getByTestId('affected-products-section')).toBeVisible();
    await expect(page.getByText('WebFramework')).toBeVisible();
    await expect(page.getByText('Example Corp')).toBeVisible();

    // Check remediation section
    await expect(page.getByTestId('remediation-section')).toBeVisible();
    await expect(page.getByText('Version 2.3')).toBeVisible();
    await expect(page.getByTestId('patch-download-link')).toBeVisible();
  });

  test('should handle CVE watchlist functionality', async ({ page }) => {
    await page.goto('/cves/CVE-2024-0001');

    // Mock watchlist API
    await page.route(`${API_BASE}/cves/CVE-2024-0001/watch`, async route => {
      await route.fulfill({
        json: { message: 'CVE added to watchlist', watching: true }
      });
    });

    // Add to watchlist
    await page.click('[data-testid="add-to-watchlist-button"]');

    // Should make API call
    await page.waitForRequest(request => 
      request.url().includes('/cves/CVE-2024-0001/watch') &&
      request.method() === 'POST'
    );

    // Button should change state
    await expect(page.getByTestId('remove-from-watchlist-button')).toBeVisible();
    await expect(page.getByText('Added to watchlist')).toBeVisible();
  });

  test('should export CVE data', async ({ page }) => {
    await navigateToCVEs(page);

    // Mock export API
    await page.route(`${API_BASE}/cves/export`, async route => {
      await route.fulfill({
        json: {
          export_id: 'export-123',
          status: 'processing',
          download_url: '/downloads/cves-export.csv'
        }
      });
    });

    // Click export button
    await page.click('[data-testid="export-button"]');

    // Should show export options
    await expect(page.getByTestId('export-options-modal')).toBeVisible();

    // Select CSV format
    await page.click('[data-testid="export-format-csv"]');
    await page.click('[data-testid="confirm-export"]');

    // Should make export API call
    await page.waitForRequest(request => 
      request.url().includes('/cves/export') &&
      request.method() === 'POST'
    );

    // Should show export progress
    await expect(page.getByText('Export in progress')).toBeVisible();
  });

  test('should handle trending CVEs filter', async ({ page }) => {
    await navigateToCVEs(page);

    // Click trending filter
    await page.click('[data-testid="trending-filter-toggle"]');

    // Should make API call with trending filter
    await page.waitForRequest(request => 
      request.url().includes('/cves') && 
      request.url().includes('trending=true')
    );

    // Should show only trending CVEs
    await expect(page.getByTestId('cve-item-CVE-2024-0001')).toBeVisible();
    await expect(page.getByTestId('trending-filter-active')).toBeVisible();
  });

  test('should support keyboard navigation', async ({ page }) => {
    await navigateToCVEs(page);

    // Tab through CVE items
    await page.keyboard.press('Tab');
    await expect(page.getByTestId('cve-item-CVE-2024-0001')).toBeFocused();

    // Enter should navigate to details
    await page.keyboard.press('Enter');
    await expect(page).toHaveURL('/cves/CVE-2024-0001');
  });

  test('should handle responsive design on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await navigateToCVEs(page);

    // Check mobile layout
    await expect(page.getByTestId('cve-list-container')).toBeVisible();
    
    // Filters should be collapsible on mobile
    await expect(page.getByTestId('mobile-filter-toggle')).toBeVisible();
    
    // Click to expand filters
    await page.click('[data-testid="mobile-filter-toggle"]');
    await expect(page.getByTestId('filter-panel')).toBeVisible();
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Mock API error
    await page.route(`${API_BASE}/cves*`, async route => {
      await route.fulfill({
        status: 500,
        json: { error: 'Internal server error' }
      });
    });

    await page.goto('/cves');

    // Should show error message
    await expect(page.getByText(/error loading cves/i)).toBeVisible();
    await expect(page.getByTestId('retry-button')).toBeVisible();

    // Retry should work
    await page.click('[data-testid="retry-button"]');
    await page.waitForRequest(request => request.url().includes('/cves'));
  });

  test('should show loading states during data fetch', async ({ page }) => {
    // Add delay to API response
    await page.route(`${API_BASE}/cves*`, async route => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.fulfill({
        json: { cves: mockCVEs, total: mockCVEs.length }
      });
    });

    await page.goto('/cves');

    // Should show loading skeletons
    await expect(page.getByTestId('cve-list-skeleton')).toBeVisible();

    // Loading should disappear when data loads
    await expect(page.getByTestId('cve-list-skeleton')).not.toBeVisible({ timeout: 2000 });
    await expect(page.getByTestId('cve-item-CVE-2024-0001')).toBeVisible();
  });

  test('should handle pagination', async ({ page }) => {
    // Mock paginated response
    await page.route(`${API_BASE}/cves*`, async route => {
      const url = route.request().url();
      const page_param = new URL(url).searchParams.get('page') || '1';
      
      await route.fulfill({
        json: {
          cves: mockCVEs,
          total: 100,
          page: parseInt(page_param),
          total_pages: 10
        }
      });
    });

    await navigateToCVEs(page);

    // Should show pagination controls
    await expect(page.getByTestId('pagination-container')).toBeVisible();
    await expect(page.getByTestId('page-info')).toHaveText('Page 1 of 10');

    // Click next page
    await page.click('[data-testid="next-page-button"]');

    // Should make API call for page 2
    await page.waitForRequest(request => 
      request.url().includes('/cves') && 
      request.url().includes('page=2')
    );

    await expect(page.getByTestId('page-info')).toHaveText('Page 2 of 10');
  });

  test('should maintain accessibility standards', async ({ page }) => {
    await navigateToCVEs(page);

    // Check for proper heading structure
    const headings = page.getByRole('heading');
    await expect(headings.first()).toHaveAttribute('aria-level', '1');

    // Check for ARIA labels on interactive elements
    await expect(page.getByTestId('severity-filter')).toHaveAttribute('aria-label');
    await expect(page.getByTestId('cve-search-input')).toHaveAttribute('aria-label');

    // Check color contrast for severity indicators
    const criticalBadge = page.getByText('CRITICAL');
    const styles = await criticalBadge.evaluate(el => {
      const computed = getComputedStyle(el);
      return {
        backgroundColor: computed.backgroundColor,
        color: computed.color
      };
    });

    // Should have sufficient contrast (basic check)
    expect(styles.backgroundColor).not.toBe(styles.color);
  });

  test('should handle real-time CVE updates', async ({ page }) => {
    await navigateToCVEs(page);

    // Simulate real-time update via WebSocket or polling
    await page.evaluate(() => {
      window.dispatchEvent(new CustomEvent('cve-update', {
        detail: {
          cveId: 'CVE-2024-0001',
          type: 'severity_change',
          newSeverity: 'CRITICAL'
        }
      }));
    });

    // Should show update notification
    await expect(page.getByTestId('update-notification')).toBeVisible();
    await expect(page.getByText('CVE updated')).toBeVisible();
  });

  test('should support advanced filtering combinations', async ({ page }) => {
    await navigateToCVEs(page);

    // Apply multiple filters
    await page.click('[data-testid="severity-filter"]');
    await page.click('[data-testid="severity-critical"]');
    
    await page.click('[data-testid="patch-filter"]');
    await page.click('[data-testid="patch-available"]');
    
    await page.click('[data-testid="trending-filter-toggle"]');

    // Should make API call with all filters
    await page.waitForRequest(request => {
      const url = request.url();
      return url.includes('/cves') && 
             url.includes('severity=critical') &&
             url.includes('patch_available=true') &&
             url.includes('trending=true');
    });

    // Should show active filter indicators
    await expect(page.getByTestId('active-filters-count')).toHaveText('3 filters active');
    
    // Clear all filters
    await page.click('[data-testid="clear-all-filters"]');
    await expect(page.getByTestId('active-filters-count')).not.toBeVisible();
  });
});
