/**
 * CVE Management Playwright+Behave Step Definitions
 * Integration of Playwright E2E tests with Behave BDD scenarios for CVE management
 */

import { Given, When, Then, Before, After } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { <PERSON>, Browser, BrowserContext } from '@playwright/test';
import { chromium } from '@playwright/test';

// World context for sharing state between steps
interface CVEWorld {
  browser: Browser;
  context: BrowserContext;
  page: Page;
  currentCVE: any;
  searchResults: any[];
  appliedFilters: Record<string, any>;
  currentUser: any;
}

let world: CVEWorld;

// Test data
const TEST_USERS = {
  security_analyst: {
    email: '<EMAIL>',
    password: 'password123',
    role: 'security_analyst'
  }
};

const MOCK_CVES = [
  {
    id: 'CVE-2024-0001',
    description: 'Critical remote code execution vulnerability in web framework',
    severity: 'CRITICAL',
    cvss_score: 9.8,
    published_date: '2024-01-20T10:00:00Z',
    status: 'published',
    affected_applications: 5,
    patch_available: true,
    exploit_available: true,
    trending: true,
    tags: ['rce', 'critical', 'web'],
    remediation_status: 'available'
  },
  {
    id: 'CVE-2024-0002',
    description: 'High severity privilege escalation in authentication module',
    severity: 'HIGH',
    cvss_score: 8.5,
    published_date: '2024-01-19T14:00:00Z',
    status: 'published',
    affected_applications: 3,
    patch_available: false,
    exploit_available: false,
    trending: false,
    tags: ['privilege-escalation', 'auth'],
    remediation_status: 'none'
  },
  {
    id: 'CVE-2024-0003',
    description: 'Medium severity SQL injection in database module',
    severity: 'MEDIUM',
    cvss_score: 6.5,
    published_date: '2024-01-18T09:00:00Z',
    status: 'published',
    affected_applications: 2,
    patch_available: true,
    exploit_available: false,
    trending: true,
    tags: ['sql-injection', 'database'],
    remediation_status: 'available'
  }
];

const MOCK_CVE_DETAILS = {
  ...MOCK_CVES[0],
  technical_details: {
    attack_vector: 'Network',
    attack_complexity: 'Low',
    privileges_required: 'None',
    user_interaction: 'None',
    scope: 'Unchanged',
    confidentiality_impact: 'High',
    integrity_impact: 'High',
    availability_impact: 'High'
  },
  affected_versions: [
    {
      product: 'WebFramework',
      vendor: 'Example Corp',
      versions: ['2.0', '2.1', '2.2'],
      fixed_versions: ['2.3']
    }
  ],
  timeline: [
    {
      date: '2024-01-15T00:00:00Z',
      event: 'vulnerability_discovered',
      description: 'Vulnerability discovered by security researcher'
    },
    {
      date: '2024-01-20T10:00:00Z',
      event: 'cve_published',
      description: 'CVE published in NVD database'
    }
  ],
  remediation: {
    patches: [
      {
        version: '2.3',
        release_date: '2024-01-22T00:00:00Z',
        download_url: 'https://example.com/patches/v2.3'
      }
    ],
    workarounds: ['Disable affected module until patch is applied'],
    mitigations: ['Deploy Web Application Firewall rules']
  }
};

// Setup and teardown
Before(async function() {
  world = {} as CVEWorld;
  world.browser = await chromium.launch({ headless: true });
  world.context = await world.browser.newContext();
  world.page = await world.context.newPage();
  world.appliedFilters = {};
  
  // Setup API mocking
  await setupApiMocks(world.page);
});

After(async function() {
  if (world.page) await world.page.close();
  if (world.context) await world.context.close();
  if (world.browser) await world.browser.close();
});

async function setupApiMocks(page: Page) {
  const API_BASE = 'http://localhost:8001/api/v1';
  
  // Mock CVE list endpoint
  await page.route(`${API_BASE}/cves*`, async route => {
    const url = route.request().url();
    const searchParams = new URL(url).searchParams;
    
    let filteredCVEs = [...MOCK_CVES];
    
    // Apply filters based on query parameters
    const severity = searchParams.get('severity');
    const trending = searchParams.get('trending');
    const patch_available = searchParams.get('patch_available');
    const search = searchParams.get('search');
    
    if (severity) {
      filteredCVEs = filteredCVEs.filter(cve => cve.severity === severity.toUpperCase());
    }
    if (trending === 'true') {
      filteredCVEs = filteredCVEs.filter(cve => cve.trending);
    }
    if (patch_available === 'true') {
      filteredCVEs = filteredCVEs.filter(cve => cve.patch_available);
    }
    if (search) {
      const searchLower = search.toLowerCase();
      filteredCVEs = filteredCVEs.filter(cve => 
        cve.description.toLowerCase().includes(searchLower) ||
        cve.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }
    
    await route.fulfill({
      json: {
        cves: filteredCVEs,
        total: filteredCVEs.length,
        page: 1,
        total_pages: 1
      }
    });
  });

  // Mock CVE details endpoint
  await page.route(`${API_BASE}/cves/CVE-2024-0001`, async route => {
    await route.fulfill({
      json: MOCK_CVE_DETAILS
    });
  });

  // Mock CVE statistics
  await page.route(`${API_BASE}/cves/stats`, async route => {
    await route.fulfill({
      json: {
        total_cves: 1247,
        by_severity: {
          critical: 23,
          high: 156,
          medium: 489,
          low: 579
        },
        trending_cves: 15
      }
    });
  });

  // Mock authentication
  await page.route(`${API_BASE}/auth/login`, async route => {
    await route.fulfill({
      json: {
        access_token: 'mock-token',
        token_type: 'bearer',
        user: TEST_USERS.security_analyst
      }
    });
  });
}

// Step definitions

// Background steps
Given('I am logged in as a security analyst', async function() {
  world.currentUser = TEST_USERS.security_analyst;
  
  await world.page.goto('/login');
  await world.page.fill('[data-testid="email-input"]', world.currentUser.email);
  await world.page.fill('[data-testid="password-input"]', world.currentUser.password);
  await world.page.click('[data-testid="login-button"]');
  await world.page.waitForURL('/dashboard');
});

Given('I have access to the CVE management system', async function() {
  // Verify user has CVE management access
  await world.page.click('[data-testid="nav-cves"]');
  await world.page.waitForURL('/cves');
  await expect(world.page.getByTestId('cve-list-container')).toBeVisible();
});

// Data setup steps
Given('there are CVEs in the system', async function() {
  // CVEs are already mocked in setupApiMocks
  expect(MOCK_CVES.length).toBeGreaterThan(0);
});

Given('there are CVEs with different severity levels', async function() {
  const severities = [...new Set(MOCK_CVES.map(cve => cve.severity))];
  expect(severities.length).toBeGreaterThan(1);
});

Given('there are CVEs with various attributes', async function() {
  // Verify we have CVEs with different attributes for filtering
  const hasTrending = MOCK_CVES.some(cve => cve.trending);
  const hasPatches = MOCK_CVES.some(cve => cve.patch_available);
  expect(hasTrending).toBe(true);
  expect(hasPatches).toBe(true);
});

Given('there is a CVE {string} in the system', async function(cveId: string) {
  const cve = MOCK_CVES.find(c => c.id === cveId);
  expect(cve).toBeDefined();
  world.currentCVE = cve;
});

Given('there are multiple CVEs in the system', async function() {
  expect(MOCK_CVES.length).toBeGreaterThan(1);
});

Given('there are trending CVEs in the system', async function() {
  const trendingCVEs = MOCK_CVES.filter(cve => cve.trending);
  expect(trendingCVEs.length).toBeGreaterThan(0);
});

// Navigation steps
When('I navigate to the CVE list page', async function() {
  await world.page.goto('/cves');
  await world.page.waitForSelector('[data-testid="cve-list-container"]');
});

When('I navigate to the trending CVEs section', async function() {
  await world.page.click('[data-testid="trending-filter-toggle"]');
  await world.page.waitForRequest(request => 
    request.url().includes('/cves') && request.url().includes('trending=true')
  );
});

When('I navigate to the CVE statistics page', async function() {
  await world.page.goto('/cves/stats');
  await world.page.waitForSelector('[data-testid="cve-stats-container"]');
});

// Filter and search steps
When('I filter CVEs by {string} severity', async function(severity: string) {
  await world.page.click('[data-testid="severity-filter"]');
  await world.page.click(`[data-testid="severity-${severity.toLowerCase()}"]`);
  
  world.appliedFilters.severity = severity.toLowerCase();
  
  await world.page.waitForRequest(request => 
    request.url().includes('/cves') && 
    request.url().includes(`severity=${severity.toLowerCase()}`)
  );
});

When('I apply multiple filters:', async function(dataTable: any) {
  const filters = dataTable.hashes();
  
  for (const filter of filters) {
    const filterType = filter['Filter Type'];
    const value = filter['Value'];
    
    switch (filterType) {
      case 'Severity':
        const severities = value.split(', ');
        for (const severity of severities) {
          await world.page.click('[data-testid="severity-filter"]');
          await world.page.click(`[data-testid="severity-${severity.toLowerCase()}"]`);
        }
        world.appliedFilters.severity = severities;
        break;
        
      case 'Patch Available':
        if (value === 'Yes') {
          await world.page.click('[data-testid="patch-filter"]');
          await world.page.click('[data-testid="patch-available"]');
          world.appliedFilters.patch_available = true;
        }
        break;
        
      case 'Trending':
        if (value === 'Yes') {
          await world.page.click('[data-testid="trending-filter-toggle"]');
          world.appliedFilters.trending = true;
        }
        break;
    }
  }
});

When('I search for {string}', async function(searchTerm: string) {
  await world.page.fill('[data-testid="cve-search-input"]', searchTerm);
  await world.page.keyboard.press('Enter');
  
  await world.page.waitForRequest(request => 
    request.url().includes('/cves') && 
    request.url().includes(`search=${encodeURIComponent(searchTerm)}`)
  );
});

When('I sort CVEs by CVSS score in descending order', async function() {
  await world.page.click('[data-testid="sort-dropdown"]');
  await world.page.click('[data-testid="sort-cvss-desc"]');
  
  await world.page.waitForRequest(request => 
    request.url().includes('/cves') && 
    request.url().includes('sort=cvss_score&order=desc')
  );
});

When('I change sort to publication date ascending', async function() {
  await world.page.click('[data-testid="sort-dropdown"]');
  await world.page.click('[data-testid="sort-date-asc"]');
  
  await world.page.waitForRequest(request => 
    request.url().includes('/cves') && 
    request.url().includes('sort=published_date&order=asc')
  );
});

// Interaction steps
When('I click on the CVE ID', async function() {
  await world.page.click(`[data-testid="cve-link-${world.currentCVE.id}"]`);
});

When('I view the CVE details', async function() {
  await world.page.goto(`/cves/${world.currentCVE.id}`);
  await world.page.waitForSelector('[data-testid="cve-details-container"]');
});

When('I examine the technical details section', async function() {
  await expect(world.page.getByTestId('technical-details-section')).toBeVisible();
});

When('I view the CVE timeline', async function() {
  await expect(world.page.getByTestId('cve-timeline-section')).toBeVisible();
});

When('I click the export button', async function() {
  await world.page.click('[data-testid="export-button"]');
});

When('I click the {string} button', async function(buttonText: string) {
  const buttonMap: Record<string, string> = {
    'Add to Watchlist': 'add-to-watchlist-button',
    'export': 'export-button',
    'refresh': 'refresh-button'
  };
  
  const testId = buttonMap[buttonText] || buttonText.toLowerCase().replace(/\s+/g, '-') + '-button';
  await world.page.click(`[data-testid="${testId}"]`);
});

// Assertion steps
Then('I should see a list of CVEs', async function() {
  await expect(world.page.getByTestId('cve-list-container')).toBeVisible();
  const cveItems = world.page.getByTestId(/cve-item-/);
  await expect(cveItems.first()).toBeVisible();
});

Then('each CVE should display its ID', async function() {
  for (const cve of MOCK_CVES) {
    await expect(world.page.getByText(cve.id)).toBeVisible();
  }
});

Then('each CVE should display its severity level', async function() {
  for (const cve of MOCK_CVES) {
    await expect(world.page.getByText(cve.severity)).toBeVisible();
  }
});

Then('each CVE should display its CVSS score', async function() {
  for (const cve of MOCK_CVES) {
    await expect(world.page.getByText(cve.cvss_score.toString())).toBeVisible();
  }
});

Then('each CVE should display its publication date', async function() {
  await expect(world.page.getByTestId('cve-published-date')).toBeVisible();
});

Then('each CVE should display affected applications count', async function() {
  for (const cve of MOCK_CVES) {
    await expect(world.page.getByText(`${cve.affected_applications} apps`)).toBeVisible();
  }
});

Then('I should only see CVEs with critical severity', async function() {
  const criticalCVEs = MOCK_CVES.filter(cve => cve.severity === 'CRITICAL');
  
  for (const cve of criticalCVEs) {
    await expect(world.page.getByTestId(`cve-item-${cve.id}`)).toBeVisible();
  }
  
  // Verify non-critical CVEs are not visible
  const nonCriticalCVEs = MOCK_CVES.filter(cve => cve.severity !== 'CRITICAL');
  for (const cve of nonCriticalCVEs) {
    await expect(world.page.getByTestId(`cve-item-${cve.id}`)).not.toBeVisible();
  }
});

Then('the CVE count should reflect the filtered results', async function() {
  const expectedCount = MOCK_CVES.filter(cve => cve.severity === 'CRITICAL').length;
  await expect(world.page.getByTestId('cve-count')).toHaveText(`${expectedCount} CVEs`);
});

Then('the filter should be visually indicated', async function() {
  await expect(world.page.getByTestId('active-filter-critical')).toBeVisible();
});

Then('I should see CVEs matching all criteria', async function() {
  // This would depend on the specific filters applied
  await expect(world.page.getByTestId('cve-list-container')).toBeVisible();
  
  // Verify filter indicators are shown
  if (world.appliedFilters.severity) {
    await expect(world.page.getByTestId('active-filters-count')).toBeVisible();
  }
});

Then('the applied filters should be clearly displayed', async function() {
  await expect(world.page.getByTestId('active-filters-panel')).toBeVisible();
});

Then('I should be able to clear individual filters', async function() {
  await expect(world.page.getByTestId('clear-filter-button')).toBeVisible();
});

Then('I should see CVEs containing the search term', async function() {
  await expect(world.page.getByTestId('search-results')).toBeVisible();
});

Then('the search term should be highlighted in results', async function() {
  await expect(world.page.getByTestId('search-highlight')).toBeVisible();
});

Then('I should see the total number of search results', async function() {
  await expect(world.page.getByTestId('search-results-count')).toBeVisible();
});

Then('CVEs should be ordered from highest to lowest score', async function() {
  const scores = await world.page.getByTestId('cve-score').allTextContents();
  const numericScores = scores.map(score => parseFloat(score));
  
  for (let i = 1; i < numericScores.length; i++) {
    expect(numericScores[i-1]).toBeGreaterThanOrEqual(numericScores[i]);
  }
});

Then('the sort indicator should show the current sort direction', async function() {
  await expect(world.page.getByTestId('sort-indicator-cvss-desc')).toBeVisible();
});

Then('CVEs should be ordered from oldest to newest', async function() {
  await expect(world.page.getByTestId('sort-indicator-date-asc')).toBeVisible();
});

Then('I should navigate to the CVE details page', async function() {
  await expect(world.page).toHaveURL(`/cves/${world.currentCVE.id}`);
  await expect(world.page.getByTestId('cve-details-container')).toBeVisible();
});

Then('I should see comprehensive CVE information:', async function(dataTable: any) {
  const fields = dataTable.hashes();
  
  for (const field of fields) {
    const fieldName = field['Field'];
    const present = field['Present'] === 'Yes';
    
    if (present) {
      switch (fieldName) {
        case 'Description':
          await expect(world.page.getByTestId('cve-description')).toBeVisible();
          break;
        case 'CVSS Vector':
          await expect(world.page.getByTestId('cvss-vector')).toBeVisible();
          break;
        case 'Affected Products':
          await expect(world.page.getByTestId('affected-products-section')).toBeVisible();
          break;
        case 'References':
          await expect(world.page.getByTestId('references-section')).toBeVisible();
          break;
        case 'CWE Categories':
          await expect(world.page.getByTestId('cwe-categories')).toBeVisible();
          break;
        case 'Timeline':
          await expect(world.page.getByTestId('cve-timeline-section')).toBeVisible();
          break;
        case 'Remediation':
          await expect(world.page.getByTestId('remediation-section')).toBeVisible();
          break;
      }
    }
  }
});

Then('I should see a CVSS v3.1 breakdown:', async function(dataTable: any) {
  const metrics = dataTable.hashes();
  
  for (const metric of metrics) {
    const metricName = metric['Metric'];
    const displayed = metric['Displayed'] === 'Yes';
    
    if (displayed) {
      const testId = metricName.toLowerCase().replace(/\s+/g, '-');
      await expect(world.page.getByTestId(`cvss-${testId}`)).toBeVisible();
    }
  }
});

export { world };
