/**
 * CVE Management API Tests
 * Comprehensive API testing for CVE management endpoints
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { rest } from 'msw';
import { setupServer } from 'msw/node';

// Mock data
const mockCVEs = [
  {
    id: 'CVE-2024-0001',
    description: 'Critical remote code execution vulnerability in web framework',
    severity: 'CRITICAL',
    cvss_score: 9.8,
    cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',
    published_date: '2024-01-20T10:00:00Z',
    modified_date: '2024-01-22T15:30:00Z',
    status: 'published',
    affected_products: ['WebFramework 2.x', 'WebFramework 3.0-3.2'],
    references: [
      'https://nvd.nist.gov/vuln/detail/CVE-2024-0001',
      'https://security.example.com/advisory/2024-001'
    ],
    cwe_ids: ['CWE-78', 'CWE-94'],
    affected_applications: 5,
    remediation_status: 'available',
    patch_available: true,
    exploit_available: true,
    trending: true,
    tags: ['rce', 'critical', 'web'],
    created_at: '2024-01-20T10:00:00Z',
    updated_at: '2024-01-22T15:30:00Z'
  },
  {
    id: 'CVE-2024-0002',
    description: 'High severity privilege escalation in authentication module',
    severity: 'HIGH',
    cvss_score: 8.5,
    cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N',
    published_date: '2024-01-19T14:00:00Z',
    modified_date: '2024-01-21T09:15:00Z',
    status: 'published',
    affected_products: ['AuthModule 1.x'],
    references: [
      'https://nvd.nist.gov/vuln/detail/CVE-2024-0002'
    ],
    cwe_ids: ['CWE-269'],
    affected_applications: 3,
    remediation_status: 'in_progress',
    patch_available: false,
    exploit_available: false,
    trending: false,
    tags: ['privilege-escalation', 'auth'],
    created_at: '2024-01-19T14:00:00Z',
    updated_at: '2024-01-21T09:15:00Z'
  }
];

const mockCVEDetails = {
  ...mockCVEs[0],
  technical_details: {
    attack_vector: 'Network',
    attack_complexity: 'Low',
    privileges_required: 'None',
    user_interaction: 'None',
    scope: 'Unchanged',
    confidentiality_impact: 'High',
    integrity_impact: 'High',
    availability_impact: 'High'
  },
  affected_versions: [
    {
      product: 'WebFramework',
      vendor: 'Example Corp',
      versions: ['2.0', '2.1', '2.2', '3.0', '3.1', '3.2'],
      fixed_versions: ['2.3', '3.3']
    }
  ],
  timeline: [
    {
      date: '2024-01-15T00:00:00Z',
      event: 'vulnerability_discovered',
      description: 'Vulnerability discovered by security researcher'
    },
    {
      date: '2024-01-18T00:00:00Z',
      event: 'vendor_notified',
      description: 'Vendor notified through responsible disclosure'
    },
    {
      date: '2024-01-20T10:00:00Z',
      event: 'cve_published',
      description: 'CVE published in NVD database'
    }
  ],
  remediation: {
    patches: [
      {
        version: '2.3',
        release_date: '2024-01-22T00:00:00Z',
        download_url: 'https://example.com/patches/v2.3'
      },
      {
        version: '3.3',
        release_date: '2024-01-22T00:00:00Z',
        download_url: 'https://example.com/patches/v3.3'
      }
    ],
    workarounds: [
      'Disable affected module until patch is applied',
      'Implement input validation at application level'
    ],
    mitigations: [
      'Deploy Web Application Firewall rules',
      'Monitor for exploitation attempts'
    ]
  },
  exploits: [
    {
      type: 'proof_of_concept',
      source: 'security_researcher',
      availability: 'public',
      url: 'https://github.com/security/poc-cve-2024-0001'
    }
  ]
};

const mockCVEStats = {
  total_cves: 1247,
  by_severity: {
    critical: 23,
    high: 156,
    medium: 489,
    low: 579
  },
  by_status: {
    published: 1200,
    modified: 35,
    rejected: 12
  },
  trending_cves: 15,
  new_this_week: 8,
  patched_this_week: 12,
  exploited_in_wild: 5
};

// Setup MSW server
const server = setupServer(
  // Get CVEs list
  rest.get('/api/v1/cves', (req, res, ctx) => {
    const page = req.url.searchParams.get('page') || '1';
    const limit = req.url.searchParams.get('limit') || '10';
    const severity = req.url.searchParams.get('severity');
    const status = req.url.searchParams.get('status');
    const search = req.url.searchParams.get('search');
    const trending = req.url.searchParams.get('trending');
    const patch_available = req.url.searchParams.get('patch_available');
    const exploit_available = req.url.searchParams.get('exploit_available');

    let filteredCVEs = [...mockCVEs];

    if (severity) {
      filteredCVEs = filteredCVEs.filter(cve => cve.severity === severity.toUpperCase());
    }
    if (status) {
      filteredCVEs = filteredCVEs.filter(cve => cve.status === status);
    }
    if (trending === 'true') {
      filteredCVEs = filteredCVEs.filter(cve => cve.trending);
    }
    if (patch_available === 'true') {
      filteredCVEs = filteredCVEs.filter(cve => cve.patch_available);
    }
    if (exploit_available === 'true') {
      filteredCVEs = filteredCVEs.filter(cve => cve.exploit_available);
    }
    if (search) {
      const searchLower = search.toLowerCase();
      filteredCVEs = filteredCVEs.filter(cve => 
        cve.id.toLowerCase().includes(searchLower) ||
        cve.description.toLowerCase().includes(searchLower) ||
        cve.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    return res(ctx.json({
      cves: filteredCVEs,
      total: filteredCVEs.length,
      page: parseInt(page),
      limit: parseInt(limit),
      total_pages: Math.ceil(filteredCVEs.length / parseInt(limit))
    }));
  }),

  // Get CVE details
  rest.get('/api/v1/cves/:id', (req, res, ctx) => {
    const { id } = req.params;
    if (id === 'CVE-2024-0001') {
      return res(ctx.json(mockCVEDetails));
    }
    return res(ctx.status(404), ctx.json({ error: 'CVE not found' }));
  }),

  // Get CVE statistics
  rest.get('/api/v1/cves/stats', (req, res, ctx) => {
    return res(ctx.json(mockCVEStats));
  }),

  // Get trending CVEs
  rest.get('/api/v1/cves/trending', (req, res, ctx) => {
    const trendingCVEs = mockCVEs.filter(cve => cve.trending);
    return res(ctx.json({
      cves: trendingCVEs,
      total: trendingCVEs.length
    }));
  }),

  // Get CVE timeline
  rest.get('/api/v1/cves/:id/timeline', (req, res, ctx) => {
    return res(ctx.json({
      timeline: mockCVEDetails.timeline
    }));
  }),

  // Get affected applications for CVE
  rest.get('/api/v1/cves/:id/applications', (req, res, ctx) => {
    return res(ctx.json({
      applications: [
        {
          id: '1',
          name: 'Customer Portal',
          version: '2.1.0',
          environment: 'production',
          risk_level: 'high'
        },
        {
          id: '2',
          name: 'Admin Dashboard',
          version: '3.1.0',
          environment: 'production',
          risk_level: 'critical'
        }
      ],
      total: 2
    }));
  }),

  // Update CVE status
  rest.patch('/api/v1/cves/:id/status', (req, res, ctx) => {
    return res(ctx.json({
      id: req.params.id,
      status: 'acknowledged',
      updated_at: new Date().toISOString()
    }));
  }),

  // Add CVE to watchlist
  rest.post('/api/v1/cves/:id/watch', (req, res, ctx) => {
    return res(ctx.json({
      message: 'CVE added to watchlist',
      watching: true
    }));
  }),

  // Export CVEs
  rest.post('/api/v1/cves/export', (req, res, ctx) => {
    return res(ctx.json({
      export_id: 'export-123',
      status: 'processing',
      estimated_completion: '2024-01-22T16:00:00Z'
    }));
  }),

  // Error scenarios
  rest.get('/api/v1/cves-error', (req, res, ctx) => {
    return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
  }),

  rest.get('/api/v1/cves-unauthorized', (req, res, ctx) => {
    return res(ctx.status(401), ctx.json({ error: 'Unauthorized' }));
  })
);

describe('CVE Management API Tests', () => {
  beforeEach(() => {
    server.listen();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  describe('GET /api/v1/cves', () => {
    it('should fetch CVEs list successfully', async () => {
      const response = await fetch('/api/v1/cves');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.cves).toHaveLength(2);
      expect(data.total).toBe(2);
      expect(data.cves[0]).toHaveProperty('id');
      expect(data.cves[0]).toHaveProperty('severity');
      expect(data.cves[0]).toHaveProperty('cvss_score');
    });

    it('should support pagination', async () => {
      const response = await fetch('/api/v1/cves?page=1&limit=1');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.page).toBe(1);
      expect(data.limit).toBe(1);
      expect(data.total_pages).toBe(2);
    });

    it('should filter by severity', async () => {
      const response = await fetch('/api/v1/cves?severity=critical');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.cves).toHaveLength(1);
      expect(data.cves[0].severity).toBe('CRITICAL');
    });

    it('should filter by status', async () => {
      const response = await fetch('/api/v1/cves?status=published');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.cves.every(cve => cve.status === 'published')).toBe(true);
    });

    it('should filter trending CVEs', async () => {
      const response = await fetch('/api/v1/cves?trending=true');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.cves.every(cve => cve.trending === true)).toBe(true);
    });

    it('should filter by patch availability', async () => {
      const response = await fetch('/api/v1/cves?patch_available=true');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.cves.every(cve => cve.patch_available === true)).toBe(true);
    });

    it('should support search functionality', async () => {
      const response = await fetch('/api/v1/cves?search=remote');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.cves).toHaveLength(1);
      expect(data.cves[0].description).toContain('remote');
    });

    it('should combine multiple filters', async () => {
      const response = await fetch('/api/v1/cves?severity=critical&patch_available=true');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.cves.every(cve => 
        cve.severity === 'CRITICAL' && cve.patch_available === true
      )).toBe(true);
    });
  });

  describe('GET /api/v1/cves/:id', () => {
    it('should fetch CVE details successfully', async () => {
      const response = await fetch('/api/v1/cves/CVE-2024-0001');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.id).toBe('CVE-2024-0001');
      expect(data).toHaveProperty('technical_details');
      expect(data).toHaveProperty('affected_versions');
      expect(data).toHaveProperty('timeline');
      expect(data).toHaveProperty('remediation');
      expect(data).toHaveProperty('exploits');
    });

    it('should return 404 for non-existent CVE', async () => {
      const response = await fetch('/api/v1/cves/CVE-9999-9999');
      
      expect(response.status).toBe(404);
      const data = await response.json();
      expect(data.error).toBe('CVE not found');
    });

    it('should include technical details', async () => {
      const response = await fetch('/api/v1/cves/CVE-2024-0001');
      const data = await response.json();

      expect(data.technical_details).toHaveProperty('attack_vector');
      expect(data.technical_details).toHaveProperty('attack_complexity');
      expect(data.technical_details).toHaveProperty('privileges_required');
      expect(data.technical_details).toHaveProperty('user_interaction');
    });

    it('should include remediation information', async () => {
      const response = await fetch('/api/v1/cves/CVE-2024-0001');
      const data = await response.json();

      expect(data.remediation).toHaveProperty('patches');
      expect(data.remediation).toHaveProperty('workarounds');
      expect(data.remediation).toHaveProperty('mitigations');
      expect(Array.isArray(data.remediation.patches)).toBe(true);
    });
  });

  describe('GET /api/v1/cves/stats', () => {
    it('should fetch CVE statistics', async () => {
      const response = await fetch('/api/v1/cves/stats');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty('total_cves');
      expect(data).toHaveProperty('by_severity');
      expect(data).toHaveProperty('by_status');
      expect(data).toHaveProperty('trending_cves');
      expect(data.total_cves).toBe(1247);
    });

    it('should include severity breakdown', async () => {
      const response = await fetch('/api/v1/cves/stats');
      const data = await response.json();

      expect(data.by_severity).toHaveProperty('critical');
      expect(data.by_severity).toHaveProperty('high');
      expect(data.by_severity).toHaveProperty('medium');
      expect(data.by_severity).toHaveProperty('low');
    });
  });

  describe('GET /api/v1/cves/trending', () => {
    it('should fetch trending CVEs', async () => {
      const response = await fetch('/api/v1/cves/trending');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.cves.every(cve => cve.trending === true)).toBe(true);
    });
  });

  describe('GET /api/v1/cves/:id/applications', () => {
    it('should fetch affected applications', async () => {
      const response = await fetch('/api/v1/cves/CVE-2024-0001/applications');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.applications).toHaveLength(2);
      expect(data.applications[0]).toHaveProperty('id');
      expect(data.applications[0]).toHaveProperty('name');
      expect(data.applications[0]).toHaveProperty('risk_level');
    });
  });

  describe('PATCH /api/v1/cves/:id/status', () => {
    it('should update CVE status', async () => {
      const response = await fetch('/api/v1/cves/CVE-2024-0001/status', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'acknowledged' })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.status).toBe('acknowledged');
      expect(data).toHaveProperty('updated_at');
    });
  });

  describe('POST /api/v1/cves/:id/watch', () => {
    it('should add CVE to watchlist', async () => {
      const response = await fetch('/api/v1/cves/CVE-2024-0001/watch', {
        method: 'POST'
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.watching).toBe(true);
      expect(data.message).toBe('CVE added to watchlist');
    });
  });

  describe('POST /api/v1/cves/export', () => {
    it('should initiate CVE export', async () => {
      const response = await fetch('/api/v1/cves/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          format: 'csv',
          filters: { severity: 'critical' }
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.export_id).toBe('export-123');
      expect(data.status).toBe('processing');
    });
  });

  describe('Error Handling', () => {
    it('should handle server errors gracefully', async () => {
      const response = await fetch('/api/v1/cves-error');
      
      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data).toHaveProperty('error');
    });

    it('should handle unauthorized access', async () => {
      const response = await fetch('/api/v1/cves-unauthorized');
      
      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.error).toBe('Unauthorized');
    });
  });

  describe('Data Validation', () => {
    it('should validate CVE data structure', async () => {
      const response = await fetch('/api/v1/cves');
      const data = await response.json();

      data.cves.forEach((cve: any) => {
        expect(cve).toHaveProperty('id');
        expect(cve).toHaveProperty('description');
        expect(cve).toHaveProperty('severity');
        expect(cve).toHaveProperty('cvss_score');
        expect(cve).toHaveProperty('published_date');
        expect(typeof cve.cvss_score).toBe('number');
        expect(cve.cvss_score).toBeGreaterThanOrEqual(0);
        expect(cve.cvss_score).toBeLessThanOrEqual(10);
      });
    });

    it('should validate severity values', async () => {
      const response = await fetch('/api/v1/cves');
      const data = await response.json();

      const validSeverities = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'];
      data.cves.forEach((cve: any) => {
        expect(validSeverities).toContain(cve.severity);
      });
    });

    it('should validate CVSS vector format', async () => {
      const response = await fetch('/api/v1/cves');
      const data = await response.json();

      data.cves.forEach((cve: any) => {
        if (cve.cvss_vector) {
          expect(cve.cvss_vector).toMatch(/^CVSS:3\.[01]\//);
        }
      });
    });
  });

  describe('Performance Tests', () => {
    it('should respond within acceptable time limits', async () => {
      const startTime = Date.now();
      const response = await fetch('/api/v1/cves');
      const endTime = Date.now();

      expect(response.status).toBe(200);
      expect(endTime - startTime).toBeLessThan(1000);
    });

    it('should handle large result sets efficiently', async () => {
      // Mock large dataset
      const largeCVESet = Array(1000).fill(null).map((_, index) => ({
        ...mockCVEs[0],
        id: `CVE-2024-${String(index).padStart(4, '0')}`,
        description: `Test vulnerability ${index}`
      }));

      server.use(
        rest.get('/api/v1/cves', (req, res, ctx) => {
          return res(ctx.json({
            cves: largeCVESet.slice(0, 100), // Paginated
            total: largeCVESet.length
          }));
        })
      );

      const startTime = Date.now();
      const response = await fetch('/api/v1/cves?limit=100');
      const endTime = Date.now();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.cves).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(2000);
    });
  });
});
