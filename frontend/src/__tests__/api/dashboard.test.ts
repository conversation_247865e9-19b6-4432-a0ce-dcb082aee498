/**
 * Dashboard API Tests
 * Comprehensive API testing for dashboard endpoints
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { rest } from 'msw';
import { setupServer } from 'msw/node';

// Mock data
const mockDashboardMetrics = {
  total_cves: 1247,
  critical_cves: 23,
  high_cves: 156,
  medium_cves: 489,
  low_cves: 579,
  total_applications: 45,
  vulnerable_applications: 12,
  compliance_score: 87.5,
  last_updated: '2024-01-22T10:30:00Z',
  trends: {
    cves_this_week: 15,
    cves_last_week: 12,
    new_vulnerabilities: 8,
    resolved_vulnerabilities: 5
  },
  top_vulnerabilities: [
    {
      id: 'CVE-2024-0001',
      severity: 'CRITICAL',
      score: 9.8,
      affected_apps: 3,
      description: 'Critical remote code execution vulnerability'
    },
    {
      id: 'CVE-2024-0002',
      severity: 'HIGH',
      score: 8.5,
      affected_apps: 5,
      description: 'High severity privilege escalation'
    }
  ],
  recent_activities: [
    {
      id: '1',
      type: 'cve_discovered',
      description: 'New critical CVE discovered',
      timestamp: '2024-01-22T09:15:00Z',
      user: 'system'
    },
    {
      id: '2',
      type: 'application_updated',
      description: 'Customer Portal updated to v2.1.0',
      timestamp: '2024-01-22T08:30:00Z',
      user: '<EMAIL>'
    }
  ]
};

const mockApplicationMetrics = {
  total: 45,
  by_environment: {
    production: 15,
    staging: 18,
    development: 12
  },
  by_criticality: {
    critical: 8,
    high: 15,
    medium: 18,
    low: 4
  },
  vulnerable: 12,
  compliance_status: {
    compliant: 33,
    non_compliant: 12
  }
};

const mockVulnerabilityTrends = [
  { date: '2024-01-15', critical: 20, high: 145, medium: 478, low: 567 },
  { date: '2024-01-16', critical: 21, high: 148, medium: 482, low: 571 },
  { date: '2024-01-17', critical: 22, high: 152, medium: 485, low: 575 },
  { date: '2024-01-18', critical: 23, high: 155, medium: 487, low: 577 },
  { date: '2024-01-19', critical: 23, high: 156, medium: 489, low: 579 }
];

// Setup MSW server
const server = setupServer(
  // Dashboard metrics endpoint
  rest.get('/api/v1/dashboard/metrics', (req, res, ctx) => {
    return res(ctx.json(mockDashboardMetrics));
  }),

  // Application metrics endpoint
  rest.get('/api/v1/dashboard/applications', (req, res, ctx) => {
    return res(ctx.json(mockApplicationMetrics));
  }),

  // Vulnerability trends endpoint
  rest.get('/api/v1/dashboard/trends', (req, res, ctx) => {
    const days = req.url.searchParams.get('days') || '7';
    return res(ctx.json(mockVulnerabilityTrends.slice(-parseInt(days))));
  }),

  // Error scenarios
  rest.get('/api/v1/dashboard/metrics-error', (req, res, ctx) => {
    return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
  }),

  rest.get('/api/v1/dashboard/metrics-unauthorized', (req, res, ctx) => {
    return res(ctx.status(401), ctx.json({ error: 'Unauthorized' }));
  })
);

describe('Dashboard API Tests', () => {
  beforeEach(() => {
    server.listen();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  describe('GET /api/v1/dashboard/metrics', () => {
    it('should fetch dashboard metrics successfully', async () => {
      const response = await fetch('/api/v1/dashboard/metrics');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(mockDashboardMetrics);
      expect(data.total_cves).toBe(1247);
      expect(data.critical_cves).toBe(23);
      expect(data.compliance_score).toBe(87.5);
    });

    it('should include all required metric fields', async () => {
      const response = await fetch('/api/v1/dashboard/metrics');
      const data = await response.json();

      expect(data).toHaveProperty('total_cves');
      expect(data).toHaveProperty('critical_cves');
      expect(data).toHaveProperty('high_cves');
      expect(data).toHaveProperty('medium_cves');
      expect(data).toHaveProperty('low_cves');
      expect(data).toHaveProperty('total_applications');
      expect(data).toHaveProperty('vulnerable_applications');
      expect(data).toHaveProperty('compliance_score');
      expect(data).toHaveProperty('last_updated');
      expect(data).toHaveProperty('trends');
      expect(data).toHaveProperty('top_vulnerabilities');
      expect(data).toHaveProperty('recent_activities');
    });

    it('should handle server errors gracefully', async () => {
      const response = await fetch('/api/v1/dashboard/metrics-error');
      
      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data).toHaveProperty('error');
    });

    it('should handle unauthorized access', async () => {
      const response = await fetch('/api/v1/dashboard/metrics-unauthorized');
      
      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.error).toBe('Unauthorized');
    });
  });

  describe('GET /api/v1/dashboard/applications', () => {
    it('should fetch application metrics successfully', async () => {
      const response = await fetch('/api/v1/dashboard/applications');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(mockApplicationMetrics);
      expect(data.total).toBe(45);
      expect(data.vulnerable).toBe(12);
    });

    it('should include environment breakdown', async () => {
      const response = await fetch('/api/v1/dashboard/applications');
      const data = await response.json();

      expect(data.by_environment).toHaveProperty('production');
      expect(data.by_environment).toHaveProperty('staging');
      expect(data.by_environment).toHaveProperty('development');
      expect(data.by_environment.production).toBe(15);
    });

    it('should include criticality breakdown', async () => {
      const response = await fetch('/api/v1/dashboard/applications');
      const data = await response.json();

      expect(data.by_criticality).toHaveProperty('critical');
      expect(data.by_criticality).toHaveProperty('high');
      expect(data.by_criticality).toHaveProperty('medium');
      expect(data.by_criticality).toHaveProperty('low');
    });
  });

  describe('GET /api/v1/dashboard/trends', () => {
    it('should fetch vulnerability trends with default period', async () => {
      const response = await fetch('/api/v1/dashboard/trends');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBeGreaterThan(0);
      expect(data[0]).toHaveProperty('date');
      expect(data[0]).toHaveProperty('critical');
      expect(data[0]).toHaveProperty('high');
      expect(data[0]).toHaveProperty('medium');
      expect(data[0]).toHaveProperty('low');
    });

    it('should fetch trends for custom period', async () => {
      const response = await fetch('/api/v1/dashboard/trends?days=3');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(Array.isArray(data)).toBe(true);
      expect(data.length).toBe(3);
    });

    it('should return trends in chronological order', async () => {
      const response = await fetch('/api/v1/dashboard/trends?days=5');
      const data = await response.json();

      for (let i = 1; i < data.length; i++) {
        const prevDate = new Date(data[i - 1].date);
        const currDate = new Date(data[i].date);
        expect(currDate.getTime()).toBeGreaterThanOrEqual(prevDate.getTime());
      }
    });
  });

  describe('Data Validation', () => {
    it('should validate metric data types', async () => {
      const response = await fetch('/api/v1/dashboard/metrics');
      const data = await response.json();

      expect(typeof data.total_cves).toBe('number');
      expect(typeof data.critical_cves).toBe('number');
      expect(typeof data.compliance_score).toBe('number');
      expect(typeof data.last_updated).toBe('string');
      expect(Array.isArray(data.top_vulnerabilities)).toBe(true);
      expect(Array.isArray(data.recent_activities)).toBe(true);
    });

    it('should validate vulnerability data structure', async () => {
      const response = await fetch('/api/v1/dashboard/metrics');
      const data = await response.json();

      data.top_vulnerabilities.forEach((vuln: any) => {
        expect(vuln).toHaveProperty('id');
        expect(vuln).toHaveProperty('severity');
        expect(vuln).toHaveProperty('score');
        expect(vuln).toHaveProperty('affected_apps');
        expect(vuln).toHaveProperty('description');
        expect(typeof vuln.score).toBe('number');
        expect(typeof vuln.affected_apps).toBe('number');
      });
    });

    it('should validate activity data structure', async () => {
      const response = await fetch('/api/v1/dashboard/metrics');
      const data = await response.json();

      data.recent_activities.forEach((activity: any) => {
        expect(activity).toHaveProperty('id');
        expect(activity).toHaveProperty('type');
        expect(activity).toHaveProperty('description');
        expect(activity).toHaveProperty('timestamp');
        expect(activity).toHaveProperty('user');
      });
    });
  });

  describe('Performance Tests', () => {
    it('should respond within acceptable time limits', async () => {
      const startTime = Date.now();
      const response = await fetch('/api/v1/dashboard/metrics');
      const endTime = Date.now();

      expect(response.status).toBe(200);
      expect(endTime - startTime).toBeLessThan(1000); // Should respond within 1 second
    });

    it('should handle concurrent requests', async () => {
      const requests = Array(5).fill(null).map(() => 
        fetch('/api/v1/dashboard/metrics')
      );

      const responses = await Promise.all(requests);
      
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty response data', async () => {
      server.use(
        rest.get('/api/v1/dashboard/metrics', (req, res, ctx) => {
          return res(ctx.json({}));
        })
      );

      const response = await fetch('/api/v1/dashboard/metrics');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(typeof data).toBe('object');
    });

    it('should handle malformed JSON response', async () => {
      server.use(
        rest.get('/api/v1/dashboard/metrics', (req, res, ctx) => {
          return res(ctx.text('invalid json'));
        })
      );

      const response = await fetch('/api/v1/dashboard/metrics');
      
      expect(response.status).toBe(200);
      await expect(response.json()).rejects.toThrow();
    });
  });
});
