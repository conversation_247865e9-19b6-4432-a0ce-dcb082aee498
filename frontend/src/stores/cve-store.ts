import { create } from 'zustand';
import type { CVE, CVEListResponse } from '@/types';

interface CVEFilters {
  limit: number;
  skip: number;
  severity?: string;
  search?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

interface CVEPagination {
  current_page: number;
  total_pages: number;
  total_items: number;
  items_per_page: number;
}

interface CVEState {
  cves: CVE[];
  loading: boolean;
  error: string | null;
  filters: CVEFilters;
  pagination: CVEPagination;
  selected_cves: string[];
}

interface CVEActions {
  fetchCVEs: () => Promise<void>;
  setCVEs: (cves: CVE[]) => void;
  addCVE: (cve: CVE) => void;
  updateCVE: (cve: CVE) => void;
  removeCVE: (id: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  setFilters: (filters: Partial<CVEFilters>) => void;
  resetFilters: () => void;
  setPagination: (pagination: Partial<CVEPagination>) => void;
  setCurrentPage: (page: number) => void;
  selectCVE: (id: string) => void;
  deselectCVE: (id: string) => void;
  selectAllCVEs: () => void;
  deselectAllCVEs: () => void;
  toggleCVESelection: (id: string) => void;
}

type CVEStore = CVEState & CVEActions;

const initialFilters: CVEFilters = {
  limit: 20,
  skip: 0,
  sort_by: 'published_date',
  sort_order: 'desc',
};

const initialPagination: CVEPagination = {
  current_page: 1,
  total_pages: 0,
  total_items: 0,
  items_per_page: 20,
};

export const useCVEStore = create<CVEStore>((set, get) => ({
  // Initial state
  cves: [],
  loading: false,
  error: null,
  filters: initialFilters,
  pagination: initialPagination,
  selected_cves: [],

  // Actions
  fetchCVEs: async () => {
    const { filters } = get();
    set({ loading: true, error: null });

    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/v1/cves/?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch CVEs');
      }

      const data: CVEListResponse = await response.json();
      
      set({
        cves: data.cves,
        loading: false,
        pagination: {
          ...get().pagination,
          total_items: data.total,
          total_pages: Math.ceil(data.total / filters.limit),
          current_page: Math.floor(data.skip / filters.limit) + 1,
        },
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch CVEs';
      set({
        loading: false,
        error: errorMessage,
      });
    }
  },

  setCVEs: (cves: CVE[]) => {
    set({ cves });
  },

  addCVE: (cve: CVE) => {
    set((state) => ({
      cves: [cve, ...state.cves],
    }));
  },

  updateCVE: (updatedCVE: CVE) => {
    set((state) => ({
      cves: state.cves.map((cve) =>
        cve.id === updatedCVE.id ? updatedCVE : cve
      ),
    }));
  },

  removeCVE: (id: string) => {
    set((state) => ({
      cves: state.cves.filter((cve) => cve.id !== id),
      selected_cves: state.selected_cves.filter((selectedId) => selectedId !== id),
    }));
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  clearError: () => {
    set({ error: null });
  },

  setFilters: (newFilters: Partial<CVEFilters>) => {
    set((state) => ({
      filters: { ...state.filters, ...newFilters },
    }));
  },

  resetFilters: () => {
    set({ filters: initialFilters });
  },

  setPagination: (newPagination: Partial<CVEPagination>) => {
    set((state) => ({
      pagination: { ...state.pagination, ...newPagination },
    }));
  },

  setCurrentPage: (page: number) => {
    const { pagination, filters } = get();
    const newSkip = (page - 1) * pagination.items_per_page;
    
    set({
      pagination: { ...pagination, current_page: page },
      filters: { ...filters, skip: newSkip },
    });
  },

  selectCVE: (id: string) => {
    set((state) => ({
      selected_cves: state.selected_cves.includes(id)
        ? state.selected_cves
        : [...state.selected_cves, id],
    }));
  },

  deselectCVE: (id: string) => {
    set((state) => ({
      selected_cves: state.selected_cves.filter((selectedId) => selectedId !== id),
    }));
  },

  selectAllCVEs: () => {
    set((state) => ({
      selected_cves: state.cves.map((cve) => cve.id),
    }));
  },

  deselectAllCVEs: () => {
    set({ selected_cves: [] });
  },

  toggleCVESelection: (id: string) => {
    set((state) => ({
      selected_cves: state.selected_cves.includes(id)
        ? state.selected_cves.filter((selectedId) => selectedId !== id)
        : [...state.selected_cves, id],
    }));
  },
}));

// Helper hooks
export const useCVEs = () => useCVEStore((state) => state.cves);
export const useCVELoading = () => useCVEStore((state) => state.loading);
export const useCVEError = () => useCVEStore((state) => state.error);
export const useCVEFilters = () => useCVEStore((state) => state.filters);
export const useCVEPagination = () => useCVEStore((state) => state.pagination);
export const useSelectedCVEs = () => useCVEStore((state) => state.selected_cves);

export const useCVEById = (id: string) =>
  useCVEStore((state) => state.cves.find((cve) => cve.id === id));

export const useCVEActions = () => {
  const store = useCVEStore();
  return {
    fetchCVEs: store.fetchCVEs,
    addCVE: store.addCVE,
    updateCVE: store.updateCVE,
    removeCVE: store.removeCVE,
    setFilters: store.setFilters,
    resetFilters: store.resetFilters,
    setCurrentPage: store.setCurrentPage,
    selectCVE: store.selectCVE,
    deselectCVE: store.deselectCVE,
    selectAllCVEs: store.selectAllCVEs,
    deselectAllCVEs: store.deselectAllCVEs,
    toggleCVESelection: store.toggleCVESelection,
    clearError: store.clearError,
  };
};
