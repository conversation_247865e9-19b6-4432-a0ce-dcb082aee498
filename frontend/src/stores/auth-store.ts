import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { User, LoginRequest, LoginResponse } from '@/types';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  lastActivity: number | null;
}

interface AuthActions {
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  updateLastActivity: () => void;
  checkSession: () => void;
  setUser: (user: User) => void;
  setToken: (token: string) => void;
  refreshToken: () => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
}

type AuthStore = AuthState & AuthActions;

const SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      lastActivity: null,

      // Actions
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true, error: null });

        try {
          const response = await fetch('/api/v1/auth/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
              username: credentials.username,
              password: credentials.password,
            }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || 'Login failed');
          }

          const loginResponse: LoginResponse = await response.json();

          set({
            user: loginResponse.user,
            token: loginResponse.access_token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
            lastActivity: Date.now(),
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Login failed';
          set({
            isLoading: false,
            error: errorMessage,
            isAuthenticated: false,
            user: null,
            token: null,
          });
          throw error;
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null,
          lastActivity: null,
        });
        // Clear localStorage
        localStorage.removeItem('access_token');
        localStorage.removeItem('user');
      },

      clearError: () => {
        set({ error: null });
      },

      updateLastActivity: () => {
        set({ lastActivity: Date.now() });
      },

      checkSession: () => {
        const { lastActivity, isAuthenticated } = get();
        
        if (!isAuthenticated || !lastActivity) {
          return;
        }

        const now = Date.now();
        if (now - lastActivity > SESSION_TIMEOUT) {
          // Session expired
          set({
            isAuthenticated: false,
            user: null,
            token: null,
            error: 'Session expired. Please log in again.',
            lastActivity: null,
          });
        }
      },

      setUser: (user: User) => {
        set({ user, isAuthenticated: true });
      },

      setToken: (token: string) => {
        set({ token, isAuthenticated: true });
      },

      refreshToken: async () => {
        const { token } = get();

        if (!token) {
          throw new Error('No token available for refresh');
        }

        try {
          const response = await fetch('/api/v1/auth/refresh', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });

          if (!response.ok) {
            throw new Error('Token refresh failed');
          }

          const refreshResponse = await response.json();

          set({
            token: refreshResponse.access_token,
            lastActivity: Date.now(),
          });
        } catch (error) {
          // If refresh fails, logout the user
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            error: 'Session expired. Please log in again.',
            lastActivity: null,
          });
          throw error;
        }
      },

      register: async (data: RegisterRequest) => {
        set({ isLoading: true, error: null });

        try {
          const response = await fetch('/api/v1/auth/register', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || 'Registration failed');
          }

          set({
            isLoading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Registration failed';
          set({
            isLoading: false,
            error: errorMessage,
          });
          throw error;
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
        lastActivity: state.lastActivity,
      }),
    }
  )
);

// Helper hooks for common auth checks
export const useAuth = () => {
  const store = useAuthStore();
  return {
    user: store.user,
    token: store.token,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    error: store.error,
    login: store.login,
    logout: store.logout,
    clearError: store.clearError,
    updateLastActivity: store.updateLastActivity,
    checkSession: store.checkSession,
    refreshToken: store.refreshToken,
    register: store.register,
  };
};

export const useUser = () => useAuthStore((state) => state.user);
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated);
export const useAuthError = () => useAuthStore((state) => state.error);
export const useAuthLoading = () => useAuthStore((state) => state.isLoading);

// Permission helpers
export const useUserPermissions = () => {
  const user = useUser();
  
  if (!user) return [];
  
  switch (user.role) {
    case 'security_analyst':
      return ['read', 'write', 'analyze'];
    case 'it_admin':
      return ['read', 'write', 'delete', 'admin'];
    default:
      return ['read'];
  }
};

export const useHasPermission = (permission: string) => {
  const permissions = useUserPermissions();
  return permissions.includes(permission);
};
