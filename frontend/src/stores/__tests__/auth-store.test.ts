import { renderHook, act } from '@testing-library/react';
import { useAuthStore, useAuth } from '../auth-store';
import type { User, LoginResponse } from '@/types';

// Mock fetch
global.fetch = jest.fn();

const mockUser: User = {
  id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  full_name: 'Test User',
  role: 'security_analyst',
  is_active: true,
  is_superuser: false,
  created_at: '2023-01-01T00:00:00Z',
};

const mockLoginResponse: LoginResponse = {
  access_token: 'mock-token',
  token_type: 'bearer',
  user: mockUser,
};

describe('AuthStore', () => {
  beforeEach(() => {
    // Clear the store before each test
    useAuthStore.getState().logout();
    (fetch as jest.Mock).mockClear();
    localStorage.clear();
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useAuth());

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });
  });

  describe('login', () => {
    it('should login successfully', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockLoginResponse,
      });

      const { result } = renderHook(() => useAuth());

      await act(async () => {
        await result.current.login({
          username: 'testuser',
          password: 'password123',
        });
      });

      expect(result.current.user).toEqual(mockUser);
      expect(result.current.token).toBe('mock-token');
      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should handle login failure', async () => {
      const errorResponse = { detail: 'Invalid credentials' };
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        json: async () => errorResponse,
      });

      const { result } = renderHook(() => useAuth());

      await act(async () => {
        try {
          await result.current.login({
            username: 'testuser',
            password: 'wrongpassword',
          });
        } catch (error) {
          // Expected to throw
        }
      });

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe('Invalid credentials');
    });

    it('should set loading state during login', async () => {
      let resolvePromise: (value: any) => void;
      const promise = new Promise((resolve) => {
        resolvePromise = resolve;
      });

      (fetch as jest.Mock).mockReturnValueOnce(promise);

      const { result } = renderHook(() => useAuth());

      act(() => {
        result.current.login({
          username: 'testuser',
          password: 'password123',
        });
      });

      expect(result.current.isLoading).toBe(true);

      await act(async () => {
        resolvePromise!({
          ok: true,
          json: async () => mockLoginResponse,
        });
        await promise;
      });

      expect(result.current.isLoading).toBe(false);
    });

    it('should make correct API call', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockLoginResponse,
      });

      const { result } = renderHook(() => useAuth());

      await act(async () => {
        await result.current.login({
          username: 'testuser',
          password: 'password123',
        });
      });

      expect(fetch).toHaveBeenCalledWith('/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          username: 'testuser',
          password: 'password123',
        }),
      });
    });
  });

  describe('logout', () => {
    it('should logout and clear state', () => {
      const { result } = renderHook(() => useAuth());

      // First set some state
      act(() => {
        useAuthStore.getState().setUser(mockUser);
        useAuthStore.getState().setToken('mock-token');
      });

      expect(result.current.isAuthenticated).toBe(true);

      // Then logout
      act(() => {
        result.current.logout();
      });

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.error).toBeNull();
    });
  });

  describe('clearError', () => {
    it('should clear error state', () => {
      const { result } = renderHook(() => useAuth());

      // Set an error
      act(() => {
        useAuthStore.setState({ error: 'Some error' });
      });

      expect(result.current.error).toBe('Some error');

      // Clear the error
      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
    });
  });

  describe('updateLastActivity', () => {
    it('should update last activity timestamp', () => {
      const { result } = renderHook(() => useAuth());
      const beforeTime = Date.now();

      act(() => {
        result.current.updateLastActivity();
      });

      const lastActivity = useAuthStore.getState().lastActivity;
      expect(lastActivity).toBeGreaterThanOrEqual(beforeTime);
      expect(lastActivity).toBeLessThanOrEqual(Date.now());
    });
  });

  describe('checkSession', () => {
    it('should not expire valid session', () => {
      const { result } = renderHook(() => useAuth());

      // Set up authenticated state with recent activity
      act(() => {
        useAuthStore.setState({
          user: mockUser,
          token: 'mock-token',
          isAuthenticated: true,
          lastActivity: Date.now() - 1000, // 1 second ago
        });
      });

      act(() => {
        result.current.checkSession();
      });

      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.user).toEqual(mockUser);
    });

    it('should expire old session', () => {
      const { result } = renderHook(() => useAuth());

      // Set up authenticated state with old activity (25 hours ago)
      act(() => {
        useAuthStore.setState({
          user: mockUser,
          token: 'mock-token',
          isAuthenticated: true,
          lastActivity: Date.now() - 25 * 60 * 60 * 1000,
        });
      });

      act(() => {
        result.current.checkSession();
      });

      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.error).toBe('Session expired. Please log in again.');
    });

    it('should not affect unauthenticated state', () => {
      const { result } = renderHook(() => useAuth());

      act(() => {
        result.current.checkSession();
      });

      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.user).toBeNull();
    });
  });

  describe('persistence', () => {
    it('should persist auth state to localStorage', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockLoginResponse,
      });

      const { result } = renderHook(() => useAuth());

      await act(async () => {
        await result.current.login({
          username: 'testuser',
          password: 'password123',
        });
      });

      // Check if state is persisted (this would be handled by zustand persist middleware)
      expect(result.current.user).toEqual(mockUser);
      expect(result.current.token).toBe('mock-token');
      expect(result.current.isAuthenticated).toBe(true);
    });
  });
});
