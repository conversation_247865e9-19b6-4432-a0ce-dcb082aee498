import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { authApi, cveApi, applicationApi, dashboardApi } from '@/lib/api';
import type { LoginRequest, ApplicationCreateRequest } from '@/types';

// Query keys
export const queryKeys = {
  auth: {
    currentUser: ['auth', 'currentUser'] as const,
  },
  cves: {
    all: ['cves'] as const,
    list: (params: any) => ['cves', 'list', params] as const,
    detail: (id: string) => ['cves', 'detail', id] as const,
  },
  applications: {
    all: ['applications'] as const,
    list: ['applications', 'list'] as const,
    detail: (id: string) => ['applications', 'detail', id] as const,
  },
  dashboard: {
    stats: ['dashboard', 'stats'] as const,
    activity: ['dashboard', 'activity'] as const,
  },
} as const;

// Auth hooks
export const useCurrentUser = () => {
  return useQuery({
    queryKey: queryKeys.auth.currentUser,
    queryFn: authApi.getCurrentUser,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useLogin = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (credentials: LoginRequest) => authApi.login(credentials),
    onSuccess: () => {
      // Invalidate and refetch user data
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.currentUser });
    },
  });
};

export const useLogout = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: authApi.logout,
    onSuccess: () => {
      // Clear all cached data
      queryClient.clear();
    },
  });
};

// CVE hooks
export const useCVEs = (params: {
  skip?: number;
  limit?: number;
  severity?: string;
  search?: string;
} = {}) => {
  return useQuery({
    queryKey: queryKeys.cves.list(params),
    queryFn: () => cveApi.getCVEs(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useCVE = (id: string) => {
  return useQuery({
    queryKey: queryKeys.cves.detail(id),
    queryFn: () => cveApi.getCVEById(id),
    enabled: !!id,
  });
};

// Application hooks
export const useApplications = () => {
  return useQuery({
    queryKey: queryKeys.applications.list,
    queryFn: applicationApi.getApplications,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useApplication = (id: string) => {
  return useQuery({
    queryKey: queryKeys.applications.detail(id),
    queryFn: () => applicationApi.getApplicationById(id),
    enabled: !!id,
  });
};

export const useCreateApplication = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: ApplicationCreateRequest) => applicationApi.createApplication(data),
    onSuccess: () => {
      // Invalidate applications list
      queryClient.invalidateQueries({ queryKey: queryKeys.applications.list });
    },
  });
};

export const useUpdateApplication = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<ApplicationCreateRequest> }) =>
      applicationApi.updateApplication(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate specific application and list
      queryClient.invalidateQueries({ queryKey: queryKeys.applications.detail(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.applications.list });
    },
  });
};

export const useDeleteApplication = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => applicationApi.deleteApplication(id),
    onSuccess: () => {
      // Invalidate applications list
      queryClient.invalidateQueries({ queryKey: queryKeys.applications.list });
    },
  });
};

// Dashboard hooks
export const useDashboardStats = () => {
  return useQuery({
    queryKey: queryKeys.dashboard.stats,
    queryFn: dashboardApi.getStats,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useDashboardActivity = () => {
  return useQuery({
    queryKey: queryKeys.dashboard.activity,
    queryFn: dashboardApi.getRecentActivity,
    staleTime: 30 * 1000, // 30 seconds
  });
};

// Utility hooks for common patterns
export const useInvalidateQueries = () => {
  const queryClient = useQueryClient();
  
  return {
    invalidateAuth: () => queryClient.invalidateQueries({ queryKey: queryKeys.auth.currentUser }),
    invalidateCVEs: () => queryClient.invalidateQueries({ queryKey: queryKeys.cves.all }),
    invalidateApplications: () => queryClient.invalidateQueries({ queryKey: queryKeys.applications.all }),
    invalidateDashboard: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.stats });
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.activity });
    },
    invalidateAll: () => queryClient.invalidateQueries(),
  };
};
