'use client';

import * as React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { X, Plus, Globe, Code, Shield } from 'lucide-react';
import type { ApplicationCreateRequest } from '@/types';

const applicationSchema = z.object({
  name: z
    .string()
    .min(1, 'Application name is required')
    .min(3, 'Application name must be at least 3 characters')
    .max(100, 'Application name must be less than 100 characters'),
  description: z
    .string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  version: z
    .string()
    .min(1, 'Version is required')
    .regex(/^\d+\.\d+\.\d+/, 'Version must follow semantic versioning (e.g., 1.0.0)'),
  environment: z
    .enum(['development', 'staging', 'production'], {
      required_error: 'Environment is required',
    }),
  criticality: z
    .enum(['low', 'medium', 'high', 'critical'], {
      required_error: 'Criticality level is required',
    }),
  owner_email: z
    .string()
    .email('Please enter a valid email address')
    .min(1, 'Owner email is required'),
  contact_person: z
    .string()
    .min(1, 'Contact person is required')
    .max(100, 'Contact person name must be less than 100 characters'),
  business_unit: z
    .string()
    .min(1, 'Business unit is required')
    .max(100, 'Business unit must be less than 100 characters'),
  repository_url: z
    .string()
    .url('Please enter a valid repository URL')
    .optional()
    .or(z.literal('')),
  deployment_url: z
    .string()
    .url('Please enter a valid deployment URL')
    .optional()
    .or(z.literal('')),
  technologies: z
    .array(z.string())
    .min(1, 'At least one technology must be specified'),
  compliance_requirements: z
    .array(z.string())
    .optional(),
});

type ApplicationFormData = z.infer<typeof applicationSchema>;

export interface ApplicationFormProps {
  onSubmit: (data: ApplicationCreateRequest) => void | Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  error?: string | null;
  initialData?: Partial<ApplicationCreateRequest>;
  mode?: 'create' | 'edit';
  className?: string;
}

export function ApplicationForm({
  onSubmit,
  loading = false,
  error,
  initialData,
  mode = 'create',
  className,
  onCancel,
}: ApplicationFormProps) {
  const [technologies, setTechnologies] = React.useState<string[]>(initialData?.technologies || []);
  const [complianceRequirements, setComplianceRequirements] = React.useState<string[]>(initialData?.compliance_requirements || []);
  const [newTechnology, setNewTechnology] = React.useState('');
  const [newCompliance, setNewCompliance] = React.useState('');

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    clearErrors,
    reset,
    setValue,
    watch,
  } = useForm<ApplicationFormData>({
    resolver: zodResolver(applicationSchema),
    mode: 'onChange',
    defaultValues: {
      name: initialData?.name || '',
      description: initialData?.description || '',
      version: initialData?.version || '1.0.0',
      environment: (initialData?.environment as 'development' | 'staging' | 'production') || 'development',
      criticality: (initialData?.criticality as 'low' | 'medium' | 'high' | 'critical') || 'medium',
      owner_email: initialData?.owner_email || '',
      contact_person: initialData?.contact_person || '',
      business_unit: initialData?.business_unit || '',
      repository_url: initialData?.repository_url || '',
      deployment_url: initialData?.deployment_url || '',
      technologies: technologies,
      compliance_requirements: complianceRequirements,
    },
  });

  // Update form values when arrays change
  React.useEffect(() => {
    setValue('technologies', technologies);
  }, [technologies, setValue]);

  React.useEffect(() => {
    setValue('compliance_requirements', complianceRequirements);
  }, [complianceRequirements, setValue]);

  const handleFormSubmit = async (data: ApplicationFormData) => {
    try {
      await onSubmit(data);
      if (mode === 'create') {
        reset(); // Reset form after successful creation
      }
    } catch (err) {
      // Error handling is done by parent component
    }
  };

  // Clear errors when user starts typing
  const handleInputChange = () => {
    if (error) {
      clearErrors();
    }
  };

  const addTechnology = () => {
    if (newTechnology.trim() && !technologies.includes(newTechnology.trim())) {
      setTechnologies([...technologies, newTechnology.trim()]);
      setNewTechnology('');
    }
  };

  const removeTechnology = (tech: string) => {
    setTechnologies(technologies.filter(t => t !== tech));
  };

  const addComplianceRequirement = () => {
    if (newCompliance.trim() && !complianceRequirements.includes(newCompliance.trim())) {
      setComplianceRequirements([...complianceRequirements, newCompliance.trim()]);
      setNewCompliance('');
    }
  };

  const removeComplianceRequirement = (req: string) => {
    setComplianceRequirements(complianceRequirements.filter(r => r !== req));
  };

  const predefinedTechnologies = [
    'React', 'Vue.js', 'Angular', 'Node.js', 'Python', 'Java', 'Go', 'Rust',
    'PostgreSQL', 'MySQL', 'MongoDB', 'Redis', 'Docker', 'Kubernetes',
    'AWS', 'Azure', 'GCP', 'Terraform', 'Jenkins', 'GitHub Actions'
  ];

  const predefinedCompliance = [
    'SOX', 'PCI-DSS', 'HIPAA', 'GDPR', 'SOC2', 'ISO 27001', 'NIST', 'FedRAMP'
  ];

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>
          {mode === 'create' ? 'Create New Application' : 'Edit Application'}
        </CardTitle>
        <CardDescription>
          {mode === 'create' 
            ? 'Add a new application to monitor for vulnerabilities'
            : 'Update application information and settings'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {error && (
            <div
              className="bg-red-950/50 border border-red-800 text-red-400 px-4 py-3 rounded-lg"
              role="alert"
              data-testid="error-message"
            >
              <p className="text-sm">{error}</p>
            </div>
          )}

          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-slate-50 border-b border-slate-700 pb-2">
              Basic Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-slate-300">Application Name *</Label>
                <Input
                  {...register('name')}
                  id="name"
                  placeholder="e.g., Customer Portal"
                  className="bg-slate-700 border-slate-600 text-slate-50"
                  onChange={(e) => {
                    register('name').onChange(e);
                    handleInputChange();
                  }}
                  data-testid="name-input"
                />
                {errors.name && (
                  <p className="text-sm text-red-400">{errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="version" className="text-slate-300">Version *</Label>
                <Input
                  {...register('version')}
                  id="version"
                  placeholder="e.g., 1.0.0"
                  className="bg-slate-700 border-slate-600 text-slate-50"
                  onChange={(e) => {
                    register('version').onChange(e);
                    handleInputChange();
                  }}
                  data-testid="version-input"
                />
                {errors.version && (
                  <p className="text-sm text-red-400">{errors.version.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-slate-300">Description</Label>
              <textarea
                {...register('description')}
                id="description"
                placeholder="Brief description of the application and its purpose"
                className="flex min-h-[80px] w-full rounded-md border border-slate-600 bg-slate-700 px-3 py-2 text-sm text-slate-50 placeholder:text-slate-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-cyan-400 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                onChange={(e) => {
                  register('description').onChange(e);
                  handleInputChange();
                }}
                data-testid="description-input"
              />
              {errors.description && (
                <p className="text-sm text-red-400">{errors.description.message}</p>
              )}
            </div>
          </div>

          {/* Environment & Risk Assessment */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-slate-50 border-b border-slate-700 pb-2">
              Environment & Risk Assessment
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="environment" className="text-slate-300">Environment *</Label>
                <select
                  {...register('environment')}
                  id="environment"
                  className="flex h-10 w-full rounded-md border border-slate-600 bg-slate-700 px-3 py-2 text-sm text-slate-50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-cyan-400 focus-visible:ring-offset-2"
                  onChange={(e) => {
                    register('environment').onChange(e);
                    handleInputChange();
                  }}
                  data-testid="environment-select"
                >
                  <option value="development">Development</option>
                  <option value="staging">Staging</option>
                  <option value="production">Production</option>
                </select>
                {errors.environment && (
                  <p className="text-sm text-red-400">{errors.environment.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="criticality" className="text-slate-300">Criticality Level *</Label>
                <select
                  {...register('criticality')}
                  id="criticality"
                  className="flex h-10 w-full rounded-md border border-slate-600 bg-slate-700 px-3 py-2 text-sm text-slate-50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-cyan-400 focus-visible:ring-offset-2"
                  onChange={(e) => {
                    register('criticality').onChange(e);
                    handleInputChange();
                  }}
                  data-testid="criticality-select"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="critical">Critical</option>
                </select>
                {errors.criticality && (
                  <p className="text-sm text-red-400">{errors.criticality.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-slate-50 border-b border-slate-700 pb-2">
              Contact Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="owner_email" className="text-slate-300">Owner Email *</Label>
                <Input
                  {...register('owner_email')}
                  id="owner_email"
                  type="email"
                  placeholder="<EMAIL>"
                  className="bg-slate-700 border-slate-600 text-slate-50"
                  onChange={(e) => {
                    register('owner_email').onChange(e);
                    handleInputChange();
                  }}
                  data-testid="owner-email-input"
                />
                {errors.owner_email && (
                  <p className="text-sm text-red-400">{errors.owner_email.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="contact_person" className="text-slate-300">Contact Person *</Label>
                <Input
                  {...register('contact_person')}
                  id="contact_person"
                  placeholder="John Doe"
                  className="bg-slate-700 border-slate-600 text-slate-50"
                  onChange={(e) => {
                    register('contact_person').onChange(e);
                    handleInputChange();
                  }}
                  data-testid="contact-person-input"
                />
                {errors.contact_person && (
                  <p className="text-sm text-red-400">{errors.contact_person.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="business_unit" className="text-slate-300">Business Unit *</Label>
              <Input
                {...register('business_unit')}
                id="business_unit"
                placeholder="e.g., Engineering, Sales, Marketing"
                className="bg-slate-700 border-slate-600 text-slate-50"
                onChange={(e) => {
                  register('business_unit').onChange(e);
                  handleInputChange();
                }}
                data-testid="business-unit-input"
              />
              {errors.business_unit && (
                <p className="text-sm text-red-400">{errors.business_unit.message}</p>
              )}
            </div>
          </div>

          {/* URLs */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-slate-50 border-b border-slate-700 pb-2">
              URLs & Links
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="repository_url" className="text-slate-300 flex items-center">
                  <Code className="h-4 w-4 mr-1" />
                  Repository URL
                </Label>
                <Input
                  {...register('repository_url')}
                  id="repository_url"
                  type="url"
                  placeholder="https://github.com/company/app"
                  className="bg-slate-700 border-slate-600 text-slate-50"
                  onChange={(e) => {
                    register('repository_url').onChange(e);
                    handleInputChange();
                  }}
                  data-testid="repository-url-input"
                />
                {errors.repository_url && (
                  <p className="text-sm text-red-400">{errors.repository_url.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="deployment_url" className="text-slate-300 flex items-center">
                  <Globe className="h-4 w-4 mr-1" />
                  Deployment URL
                </Label>
                <Input
                  {...register('deployment_url')}
                  id="deployment_url"
                  type="url"
                  placeholder="https://app.company.com"
                  className="bg-slate-700 border-slate-600 text-slate-50"
                  onChange={(e) => {
                    register('deployment_url').onChange(e);
                    handleInputChange();
                  }}
                  data-testid="deployment-url-input"
                />
                {errors.deployment_url && (
                  <p className="text-sm text-red-400">{errors.deployment_url.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Technologies */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-slate-50 border-b border-slate-700 pb-2">
              Technologies & Stack *
            </h3>

            <div className="space-y-3">
              <div className="flex space-x-2">
                <Input
                  value={newTechnology}
                  onChange={(e) => setNewTechnology(e.target.value)}
                  placeholder="Add technology (e.g., React, Node.js)"
                  className="bg-slate-700 border-slate-600 text-slate-50"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addTechnology();
                    }
                  }}
                />
                <Button
                  type="button"
                  onClick={addTechnology}
                  variant="outline"
                  className="border-slate-600 text-slate-300 hover:bg-slate-700"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex flex-wrap gap-2">
                {predefinedTechnologies.map((tech) => (
                  <Button
                    key={tech}
                    type="button"
                    variant={technologies.includes(tech) ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                      if (technologies.includes(tech)) {
                        removeTechnology(tech);
                      } else {
                        setTechnologies([...technologies, tech]);
                      }
                    }}
                    className={technologies.includes(tech)
                      ? "bg-cyan-600 hover:bg-cyan-700 text-white"
                      : "border-slate-600 text-slate-300 hover:bg-slate-700"
                    }
                  >
                    {tech}
                  </Button>
                ))}
              </div>

              {technologies.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-slate-300">Selected Technologies:</Label>
                  <div className="flex flex-wrap gap-2">
                    {technologies.map((tech) => (
                      <Badge
                        key={tech}
                        variant="secondary"
                        className="bg-slate-600 text-slate-300 flex items-center space-x-1"
                      >
                        <span>{tech}</span>
                        <button
                          type="button"
                          onClick={() => removeTechnology(tech)}
                          className="ml-1 hover:text-red-400"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {errors.technologies && (
                <p className="text-sm text-red-400">{errors.technologies.message}</p>
              )}
            </div>
          </div>

          <div className="flex gap-4 pt-4 border-t border-slate-700">
            <Button
              type="submit"
              loading={loading}
              disabled={!isValid || loading || technologies.length === 0}
              data-testid="submit-button"
              className="flex-1 bg-cyan-600 hover:bg-cyan-700"
            >
              {loading 
                ? (mode === 'create' ? 'Creating...' : 'Updating...') 
                : (mode === 'create' ? 'Create Application' : 'Update Application')
              }
            </Button>
            
            {mode === 'create' && (
              <Button
                type="button"
                variant="outline"
                onClick={() => reset()}
                disabled={loading}
                data-testid="reset-button"
              >
                Reset
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
