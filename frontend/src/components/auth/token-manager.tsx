'use client';

import { useEffect, useRef } from 'react';
import { useAuthStore } from '@/stores/auth-store';

interface TokenManagerProps {
  children: React.ReactNode;
}

const TOKEN_REFRESH_INTERVAL = 15 * 60 * 1000; // 15 minutes
const TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes before expiry

export function TokenManager({ children }: TokenManagerProps) {
  const { 
    isAuthenticated, 
    token, 
    lastActivity, 
    refreshToken, 
    logout, 
    updateLastActivity,
    checkSession 
  } = useAuthStore();
  
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const activityTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Setup automatic token refresh
  useEffect(() => {
    if (!isAuthenticated || !token) {
      // Clear any existing intervals
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
      return;
    }

    // Set up periodic token refresh
    refreshIntervalRef.current = setInterval(async () => {
      try {
        await refreshToken();
      } catch (error) {
        console.error('Token refresh failed:', error);
        // The auth store will handle logout on refresh failure
      }
    }, TOKEN_REFRESH_INTERVAL);

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [isAuthenticated, token, refreshToken]);

  // Setup session timeout checking
  useEffect(() => {
    if (!isAuthenticated) {
      if (activityTimeoutRef.current) {
        clearTimeout(activityTimeoutRef.current);
        activityTimeoutRef.current = null;
      }
      return;
    }

    // Check session validity periodically
    const checkSessionValidity = () => {
      checkSession();
      
      // Schedule next check
      activityTimeoutRef.current = setTimeout(checkSessionValidity, 60000); // Check every minute
    };

    checkSessionValidity();

    return () => {
      if (activityTimeoutRef.current) {
        clearTimeout(activityTimeoutRef.current);
      }
    };
  }, [isAuthenticated, checkSession]);

  // Track user activity for session management
  useEffect(() => {
    if (!isAuthenticated) return;

    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const handleActivity = () => {
      updateLastActivity();
    };

    // Add event listeners for user activity
    activityEvents.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      // Remove event listeners
      activityEvents.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [isAuthenticated, updateLastActivity]);

  // Handle page visibility changes
  useEffect(() => {
    if (!isAuthenticated) return;

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // Page became visible, check session and refresh token if needed
        checkSession();
        
        if (lastActivity) {
          const timeSinceLastActivity = Date.now() - lastActivity;
          
          // If it's been more than the refresh threshold since last activity, refresh token
          if (timeSinceLastActivity > TOKEN_REFRESH_THRESHOLD) {
            refreshToken().catch(error => {
              console.error('Token refresh on visibility change failed:', error);
            });
          }
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isAuthenticated, lastActivity, checkSession, refreshToken]);

  // Handle beforeunload to update last activity
  useEffect(() => {
    if (!isAuthenticated) return;

    const handleBeforeUnload = () => {
      updateLastActivity();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [isAuthenticated, updateLastActivity]);

  return <>{children}</>;
}

// Hook for manual token refresh
export function useTokenRefresh() {
  const { refreshToken, isAuthenticated } = useAuthStore();

  const manualRefresh = async () => {
    if (!isAuthenticated) {
      throw new Error('User not authenticated');
    }

    try {
      await refreshToken();
      return true;
    } catch (error) {
      console.error('Manual token refresh failed:', error);
      return false;
    }
  };

  return { refreshToken: manualRefresh };
}

// Hook for checking if token needs refresh
export function useTokenStatus() {
  const { token, lastActivity, isAuthenticated } = useAuthStore();

  const getTokenStatus = () => {
    if (!isAuthenticated || !token || !lastActivity) {
      return { needsRefresh: false, isExpired: false, timeUntilRefresh: null };
    }

    const timeSinceLastActivity = Date.now() - lastActivity;
    const needsRefresh = timeSinceLastActivity > TOKEN_REFRESH_THRESHOLD;
    const isExpired = timeSinceLastActivity > (24 * 60 * 60 * 1000); // 24 hours
    const timeUntilRefresh = Math.max(0, TOKEN_REFRESH_THRESHOLD - timeSinceLastActivity);

    return {
      needsRefresh,
      isExpired,
      timeUntilRefresh,
      lastActivity: new Date(lastActivity),
    };
  };

  return getTokenStatus();
}
