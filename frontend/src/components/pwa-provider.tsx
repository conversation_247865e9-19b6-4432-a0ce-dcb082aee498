'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  X, 
  Smartphone,
  Monitor,
  Bell
} from 'lucide-react';
import { pwaManager, getPWACapabilities } from '@/lib/pwa';
import { monitoring } from '@/lib/monitoring';

interface PWAProviderProps {
  children: React.ReactNode;
}

export function PWAProvider({ children }: PWAProviderProps) {
  const [capabilities, setCapabilities] = useState(getPWACapabilities());
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false);
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    // Initialize monitoring
    monitoring;

    // Update capabilities
    const updateCapabilities = () => {
      setCapabilities(getPWACapabilities());
    };

    // Listen for PWA events
    const handleInstallAvailable = () => {
      setShowInstallPrompt(true);
      monitoring.trackCustomEvent('pwa_install_available', {});
    };

    const handleUpdateAvailable = () => {
      setShowUpdatePrompt(true);
      monitoring.trackCustomEvent('pwa_update_available', {});
    };

    const handleAppInstalled = () => {
      setShowInstallPrompt(false);
      monitoring.trackCustomEvent('pwa_app_installed', {});
      updateCapabilities();
    };

    // Listen for online/offline events
    const handleOnline = () => {
      setIsOnline(true);
      monitoring.trackCustomEvent('network_online', {});
    };

    const handleOffline = () => {
      setIsOnline(false);
      monitoring.trackCustomEvent('network_offline', {});
    };

    // Add event listeners
    window.addEventListener('pwa-install-available', handleInstallAvailable);
    window.addEventListener('pwa-update-available', handleUpdateAvailable);
    window.addEventListener('pwa-app-installed', handleAppInstalled);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Initial online status
    setIsOnline(navigator.onLine);

    // Cleanup
    return () => {
      window.removeEventListener('pwa-install-available', handleInstallAvailable);
      window.removeEventListener('pwa-update-available', handleUpdateAvailable);
      window.removeEventListener('pwa-app-installed', handleAppInstalled);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleInstallApp = async () => {
    try {
      const success = await pwaManager.installApp();
      if (success) {
        setShowInstallPrompt(false);
        monitoring.trackCustomEvent('pwa_install_success', {});
      } else {
        monitoring.trackCustomEvent('pwa_install_declined', {});
      }
    } catch (error) {
      monitoring.trackError('PWA install failed', 'medium', { error: String(error) });
    }
  };

  const handleUpdateApp = async () => {
    try {
      await pwaManager.updateApp();
      monitoring.trackCustomEvent('pwa_update_success', {});
    } catch (error) {
      monitoring.trackError('PWA update failed', 'medium', { error: String(error) });
    }
  };

  const dismissInstallPrompt = () => {
    setShowInstallPrompt(false);
    monitoring.trackCustomEvent('pwa_install_dismissed', {});
  };

  const dismissUpdatePrompt = () => {
    setShowUpdatePrompt(false);
    monitoring.trackCustomEvent('pwa_update_dismissed', {});
  };

  return (
    <>
      {children}
      
      {/* Offline Indicator */}
      {!isOnline && (
        <div className="fixed top-0 left-0 right-0 z-50 bg-orange-600 text-white px-4 py-2 text-center text-sm">
          <div className="flex items-center justify-center space-x-2">
            <WifiOff className="h-4 w-4" />
            <span>You are currently offline. Some features may be limited.</span>
          </div>
        </div>
      )}

      {/* Online Indicator (brief) */}
      {isOnline && !navigator.onLine && (
        <div className="fixed top-0 left-0 right-0 z-50 bg-green-600 text-white px-4 py-2 text-center text-sm animate-in slide-in-from-top duration-300">
          <div className="flex items-center justify-center space-x-2">
            <Wifi className="h-4 w-4" />
            <span>Connection restored</span>
          </div>
        </div>
      )}

      {/* Install App Prompt */}
      {showInstallPrompt && capabilities.canInstall && (
        <div className="fixed bottom-4 right-4 z-50 max-w-sm">
          <Card className="bg-slate-800 border-slate-700 shadow-lg">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  <Smartphone className="h-5 w-5 text-cyan-500" />
                  <CardTitle className="text-slate-50 text-sm">Install App</CardTitle>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={dismissInstallPrompt}
                  className="text-slate-400 hover:text-slate-50 h-6 w-6 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <CardDescription className="text-slate-400 text-xs">
                Install CVE Feed Service for a better experience
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex space-x-2">
                <Button
                  onClick={handleInstallApp}
                  size="sm"
                  className="bg-cyan-600 hover:bg-cyan-700 text-xs"
                >
                  <Download className="h-3 w-3 mr-1" />
                  Install
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={dismissInstallPrompt}
                  className="border-slate-600 text-slate-300 hover:bg-slate-700 text-xs"
                >
                  Later
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Update Available Prompt */}
      {showUpdatePrompt && (
        <div className="fixed bottom-4 right-4 z-50 max-w-sm">
          <Card className="bg-slate-800 border-slate-700 shadow-lg">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  <RefreshCw className="h-5 w-5 text-green-500" />
                  <CardTitle className="text-slate-50 text-sm">Update Available</CardTitle>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={dismissUpdatePrompt}
                  className="text-slate-400 hover:text-slate-50 h-6 w-6 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <CardDescription className="text-slate-400 text-xs">
                A new version of the app is available
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex space-x-2">
                <Button
                  onClick={handleUpdateApp}
                  size="sm"
                  className="bg-green-600 hover:bg-green-700 text-xs"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Update
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={dismissUpdatePrompt}
                  className="border-slate-600 text-slate-300 hover:bg-slate-700 text-xs"
                >
                  Later
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* PWA Status Indicator (Development) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 z-40">
          <Card className="bg-slate-800 border-slate-700 shadow-lg">
            <CardHeader className="pb-2">
              <CardTitle className="text-slate-50 text-xs flex items-center space-x-2">
                <Monitor className="h-4 w-4" />
                <span>PWA Status</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span className="text-slate-400">Supported:</span>
                <Badge variant={capabilities.isSupported ? "default" : "secondary"} className="text-xs">
                  {capabilities.isSupported ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-slate-400">Installed:</span>
                <Badge variant={capabilities.isInstalled ? "default" : "secondary"} className="text-xs">
                  {capabilities.isInstalled ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-slate-400">Can Install:</span>
                <Badge variant={capabilities.canInstall ? "default" : "secondary"} className="text-xs">
                  {capabilities.canInstall ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-slate-400">Notifications:</span>
                <Badge variant={capabilities.hasNotifications ? "default" : "secondary"} className="text-xs">
                  {capabilities.hasNotifications ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-slate-400">Background Sync:</span>
                <Badge variant={capabilities.hasBackgroundSync ? "default" : "secondary"} className="text-xs">
                  {capabilities.hasBackgroundSync ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-slate-400">Online:</span>
                <Badge variant={isOnline ? "default" : "destructive"} className="text-xs">
                  {isOnline ? 'Yes' : 'No'}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
}
