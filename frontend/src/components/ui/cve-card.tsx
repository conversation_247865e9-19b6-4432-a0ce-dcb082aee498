'use client';

import * as React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  AlertTriangle, 
  Shield, 
  Info, 
  ExternalLink, 
  Calendar,
  TrendingUp
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { CVE } from '@/types';

interface CVECardProps {
  cve: CVE;
  onViewDetails?: (cve: CVE) => void;
  onSelect?: (cve: CVE) => void;
  selected?: boolean;
  className?: string;
}

const getSeverityColor = (severity?: string) => {
  switch (severity?.toUpperCase()) {
    case 'CRITICAL':
      return 'bg-red-500 text-white';
    case 'HIGH':
      return 'bg-orange-500 text-white';
    case 'MEDIUM':
      return 'bg-yellow-500 text-black';
    case 'LOW':
      return 'bg-blue-500 text-white';
    default:
      return 'bg-gray-500 text-white';
  }
};

const getSeverityIcon = (severity?: string) => {
  switch (severity?.toUpperCase()) {
    case 'CRITICAL':
      return <AlertTriangle className="h-4 w-4" />;
    case 'HIGH':
      return <Shield className="h-4 w-4" />;
    case 'MEDIUM':
      return <TrendingUp className="h-4 w-4" />;
    case 'LOW':
      return <Info className="h-4 w-4" />;
    default:
      return <Info className="h-4 w-4" />;
  }
};

export function CVECard({ 
  cve, 
  onViewDetails, 
  onSelect, 
  selected = false, 
  className 
}: CVECardProps) {
  const handleCardClick = () => {
    if (onSelect) {
      onSelect(cve);
    }
  };

  const handleViewDetails = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onViewDetails) {
      onViewDetails(cve);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <Card 
      className={cn(
        'cursor-pointer transition-all duration-200 hover:shadow-md',
        selected && 'ring-2 ring-primary ring-offset-2',
        className
      )}
      onClick={handleCardClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold">
              {cve.cve_id}
            </CardTitle>
            <div className="flex items-center gap-2 mt-2">
              {cve.cvss_v3_severity && (
                <Badge 
                  className={cn(
                    'flex items-center gap-1',
                    getSeverityColor(cve.cvss_v3_severity)
                  )}
                >
                  {getSeverityIcon(cve.cvss_v3_severity)}
                  {cve.cvss_v3_severity}
                </Badge>
              )}
              {cve.cvss_v3_score && (
                <Badge variant="outline">
                  CVSS: {cve.cvss_v3_score}
                </Badge>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleViewDetails}
              className="h-8 w-8 p-0"
            >
              <ExternalLink className="h-4 w-4" />
              <span className="sr-only">View details</span>
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <CardDescription className="text-sm text-muted-foreground line-clamp-3 mb-4">
          {cve.description}
        </CardDescription>
        
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>Published: {formatDate(cve.published_date)}</span>
          </div>
          
          <div className="flex items-center gap-1">
            <span>Source: {cve.source}</span>
          </div>
        </div>
        
        {cve.last_modified_date !== cve.published_date && (
          <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
            <TrendingUp className="h-3 w-3" />
            <span>Modified: {formatDate(cve.last_modified_date)}</span>
          </div>
        )}
        
        {cve.references && cve.references.length > 0 && (
          <div className="mt-3">
            <p className="text-xs text-muted-foreground mb-1">
              References ({cve.references.length})
            </p>
            <div className="flex flex-wrap gap-1">
              {cve.references.slice(0, 3).map((ref, index) => (
                <Badge 
                  key={index} 
                  variant="secondary" 
                  className="text-xs"
                >
                  {new URL(ref).hostname}
                </Badge>
              ))}
              {cve.references.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{cve.references.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// CVE List component for displaying multiple CVEs
interface CVEListProps {
  cves: CVE[];
  loading?: boolean;
  onViewDetails?: (cve: CVE) => void;
  onSelect?: (cve: CVE) => void;
  selectedCVEs?: string[];
  className?: string;
}

export function CVEList({ 
  cves, 
  loading = false, 
  onViewDetails, 
  onSelect, 
  selectedCVEs = [], 
  className 
}: CVEListProps) {
  if (loading) {
    return (
      <div className={cn('space-y-4', className)}>
        {Array.from({ length: 5 }).map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-muted rounded w-1/3"></div>
              <div className="h-3 bg-muted rounded w-1/4 mt-2"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-3 bg-muted rounded"></div>
                <div className="h-3 bg-muted rounded w-5/6"></div>
                <div className="h-3 bg-muted rounded w-4/6"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (cves.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Shield className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No CVEs found</h3>
          <p className="text-muted-foreground text-center">
            No vulnerabilities match your current filters.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {cves.map((cve) => (
        <CVECard
          key={cve.id}
          cve={cve}
          onViewDetails={onViewDetails}
          onSelect={onSelect}
          selected={selectedCVEs.includes(cve.id)}
        />
      ))}
    </div>
  );
}
