import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CVE<PERSON>ard, CVEList } from '../cve-card';
import type { CVE } from '@/types';

// Mock the UI components
jest.mock('@/components/ui/card', () => ({
  Card: ({ children, ...props }: any) => <div data-testid="card" {...props}>{children}</div>,
  CardContent: ({ children, ...props }: any) => <div data-testid="card-content" {...props}>{children}</div>,
  CardDescription: ({ children, ...props }: any) => <div data-testid="card-description" {...props}>{children}</div>,
  CardHeader: ({ children, ...props }: any) => <div data-testid="card-header" {...props}>{children}</div>,
  CardTitle: ({ children, ...props }: any) => <h3 data-testid="card-title" {...props}>{children}</h3>,
}));

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, className, ...props }: any) => (
    <span data-testid="badge" className={className} {...props}>{children}</span>
  ),
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
}));

const mockCVE: CVE = {
  id: '1',
  cve_id: 'CVE-2023-0001',
  description: 'A critical vulnerability in the authentication system that allows remote code execution.',
  published_date: '2023-01-15T10:00:00Z',
  last_modified_date: '2023-01-16T14:30:00Z',
  cvss_v3_score: 9.8,
  cvss_v3_vector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',
  cvss_v3_severity: 'CRITICAL',
  source: 'NVD',
  references: [
    'https://nvd.nist.gov/vuln/detail/CVE-2023-0001',
    'https://example.com/security-advisory',
    'https://github.com/vendor/repo/security/advisories',
    'https://vendor.com/security-bulletin',
  ],
  cpe_configurations: ['cpe:2.3:a:vendor:product:*:*:*:*:*:*:*:*'],
  created_at: '2023-01-15T10:00:00Z',
  updated_at: '2023-01-16T14:30:00Z',
};

describe('CVECard', () => {
  const mockOnViewDetails = jest.fn();
  const mockOnSelect = jest.fn();
  const user = userEvent.setup();

  beforeEach(() => {
    mockOnViewDetails.mockClear();
    mockOnSelect.mockClear();
  });

  it('renders CVE information correctly', () => {
    render(<CVECard cve={mockCVE} />);

    expect(screen.getByText('CVE-2023-0001')).toBeInTheDocument();
    expect(screen.getByText(/A critical vulnerability in the authentication system/)).toBeInTheDocument();
    expect(screen.getByText('CRITICAL')).toBeInTheDocument();
    expect(screen.getByText('CVSS: 9.8')).toBeInTheDocument();
    expect(screen.getByText('Published: Jan 15, 2023')).toBeInTheDocument();
    expect(screen.getByText('Modified: Jan 16, 2023')).toBeInTheDocument();
    expect(screen.getByText('Source: NVD')).toBeInTheDocument();
  });

  it('displays severity badge with correct styling for critical CVE', () => {
    render(<CVECard cve={mockCVE} />);

    const severityBadge = screen.getByText('CRITICAL');
    expect(severityBadge).toHaveClass('bg-red-500', 'text-white');
  });

  it('displays severity badge with correct styling for high CVE', () => {
    const highCVE = { ...mockCVE, cvss_v3_severity: 'HIGH' };
    render(<CVECard cve={highCVE} />);

    const severityBadge = screen.getByText('HIGH');
    expect(severityBadge).toHaveClass('bg-orange-500', 'text-white');
  });

  it('displays severity badge with correct styling for medium CVE', () => {
    const mediumCVE = { ...mockCVE, cvss_v3_severity: 'MEDIUM' };
    render(<CVECard cve={mediumCVE} />);

    const severityBadge = screen.getByText('MEDIUM');
    expect(severityBadge).toHaveClass('bg-yellow-500', 'text-black');
  });

  it('displays severity badge with correct styling for low CVE', () => {
    const lowCVE = { ...mockCVE, cvss_v3_severity: 'LOW' };
    render(<CVECard cve={lowCVE} />);

    const severityBadge = screen.getByText('LOW');
    expect(severityBadge).toHaveClass('bg-blue-500', 'text-white');
  });

  it('calls onSelect when card is clicked', async () => {
    render(<CVECard cve={mockCVE} onSelect={mockOnSelect} />);

    const card = screen.getByTestId('card');
    await user.click(card);

    expect(mockOnSelect).toHaveBeenCalledWith(mockCVE);
  });

  it('calls onViewDetails when view details button is clicked', async () => {
    render(<CVECard cve={mockCVE} onViewDetails={mockOnViewDetails} />);

    const viewDetailsButton = screen.getByRole('button', { name: /view details/i });
    await user.click(viewDetailsButton);

    expect(mockOnViewDetails).toHaveBeenCalledWith(mockCVE);
    expect(mockOnSelect).not.toHaveBeenCalled();
  });

  it('shows selected state when selected prop is true', () => {
    render(<CVECard cve={mockCVE} selected={true} />);

    const card = screen.getByTestId('card');
    expect(card).toHaveClass('ring-2', 'ring-primary', 'ring-offset-2');
  });

  it('displays references with hostname truncation', () => {
    render(<CVECard cve={mockCVE} />);

    expect(screen.getByText('References (4)')).toBeInTheDocument();
    expect(screen.getByText('nvd.nist.gov')).toBeInTheDocument();
    expect(screen.getByText('example.com')).toBeInTheDocument();
    expect(screen.getByText('github.com')).toBeInTheDocument();
    expect(screen.getByText('+1 more')).toBeInTheDocument();
  });

  it('handles CVE without references', () => {
    const cveWithoutRefs = { ...mockCVE, references: [] };
    render(<CVECard cve={cveWithoutRefs} />);

    expect(screen.queryByText('References')).not.toBeInTheDocument();
  });

  it('handles CVE without CVSS score', () => {
    const cveWithoutScore = { 
      ...mockCVE, 
      cvss_v3_score: undefined,
      cvss_v3_severity: undefined 
    };
    render(<CVECard cve={cveWithoutScore} />);

    expect(screen.queryByText(/CVSS:/)).not.toBeInTheDocument();
    expect(screen.queryByText('CRITICAL')).not.toBeInTheDocument();
  });

  it('does not show modified date when same as published date', () => {
    const cveWithSameDate = { 
      ...mockCVE, 
      last_modified_date: mockCVE.published_date 
    };
    render(<CVECard cve={cveWithSameDate} />);

    expect(screen.getByText('Published: Jan 15, 2023')).toBeInTheDocument();
    expect(screen.queryByText(/Modified:/)).not.toBeInTheDocument();
  });
});

describe('CVEList', () => {
  const mockCVEs: CVE[] = [
    mockCVE,
    {
      ...mockCVE,
      id: '2',
      cve_id: 'CVE-2023-0002',
      cvss_v3_severity: 'HIGH',
      cvss_v3_score: 7.5,
    },
  ];

  it('renders list of CVEs', () => {
    render(<CVEList cves={mockCVEs} />);

    expect(screen.getByText('CVE-2023-0001')).toBeInTheDocument();
    expect(screen.getByText('CVE-2023-0002')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    render(<CVEList cves={[]} loading={true} />);

    const loadingCards = screen.getAllByTestId('card');
    expect(loadingCards).toHaveLength(5); // Should show 5 skeleton cards
    expect(loadingCards[0]).toHaveClass('animate-pulse');
  });

  it('shows empty state when no CVEs', () => {
    render(<CVEList cves={[]} />);

    expect(screen.getByText('No CVEs found')).toBeInTheDocument();
    expect(screen.getByText('No vulnerabilities match your current filters.')).toBeInTheDocument();
  });

  it('passes callbacks to individual CVE cards', () => {
    const mockOnViewDetails = jest.fn();
    const mockOnSelect = jest.fn();

    render(
      <CVEList 
        cves={mockCVEs} 
        onViewDetails={mockOnViewDetails}
        onSelect={mockOnSelect}
      />
    );

    const cards = screen.getAllByTestId('card');
    expect(cards).toHaveLength(2);
  });

  it('shows selected state for selected CVEs', () => {
    render(
      <CVEList 
        cves={mockCVEs} 
        selectedCVEs={['1']}
      />
    );

    const cards = screen.getAllByTestId('card');
    expect(cards[0]).toHaveClass('ring-2', 'ring-primary', 'ring-offset-2');
    expect(cards[1]).not.toHaveClass('ring-2');
  });
});
