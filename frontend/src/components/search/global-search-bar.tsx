'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { Search, X, Clock, Shield, Database, FileText, Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface SearchResult {
  id: string;
  type: 'cve' | 'application' | 'component' | 'user';
  title: string;
  subtitle?: string;
  description?: string;
  severity?: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  url: string;
  metadata?: Record<string, any>;
}

interface SearchHistory {
  id: string;
  query: string;
  timestamp: string;
  resultCount: number;
}

interface GlobalSearchBarProps {
  placeholder?: string;
  className?: string;
  onSearch?: (query: string, results: SearchResult[]) => void;
  showHistory?: boolean;
  maxResults?: number;
}

export function GlobalSearchBar({
  placeholder = "Search CVEs, applications, components...",
  className,
  onSearch,
  showHistory = true,
  maxResults = 8
}: GlobalSearchBarProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [history, setHistory] = useState<SearchHistory[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const router = useRouter();
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceRef = useRef<NodeJS.Timeout>();

  // Mock search data - in real app, this would come from API
  const mockSearchData: SearchResult[] = [
    {
      id: '1',
      type: 'cve',
      title: 'CVE-2024-0001',
      subtitle: 'Remote Code Execution',
      description: 'Critical vulnerability in web framework allowing remote code execution',
      severity: 'CRITICAL',
      url: '/cves/CVE-2024-0001',
      metadata: { cvss: 9.8, published: '2024-01-15' }
    },
    {
      id: '2',
      type: 'cve',
      title: 'CVE-2024-0002',
      subtitle: 'SQL Injection',
      description: 'High severity SQL injection vulnerability in database connector',
      severity: 'HIGH',
      url: '/cves/CVE-2024-0002',
      metadata: { cvss: 8.1, published: '2024-01-14' }
    },
    {
      id: '3',
      type: 'application',
      title: 'E-commerce Platform',
      subtitle: 'Production Environment',
      description: 'Main customer-facing e-commerce application',
      url: '/applications/ecommerce-platform',
      metadata: { environment: 'production', criticality: 'high' }
    },
    {
      id: '4',
      type: 'application',
      title: 'User Management API',
      subtitle: 'Staging Environment',
      description: 'Authentication and user management microservice',
      url: '/applications/user-management-api',
      metadata: { environment: 'staging', criticality: 'medium' }
    },
    {
      id: '5',
      type: 'component',
      title: 'React v18.2.0',
      subtitle: 'Frontend Framework',
      description: 'JavaScript library for building user interfaces',
      url: '/components/react',
      metadata: { version: '18.2.0', type: 'frontend' }
    }
  ];

  // Load search history from localStorage
  useEffect(() => {
    const savedHistory = localStorage.getItem('search-history');
    if (savedHistory) {
      try {
        setHistory(JSON.parse(savedHistory));
      } catch (error) {
        console.error('Failed to parse search history:', error);
      }
    }
  }, []);

  // Debounced search function
  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Filter mock data based on query
    const filteredResults = mockSearchData.filter(item =>
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.subtitle?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchQuery.toLowerCase())
    ).slice(0, maxResults);

    setResults(filteredResults);
    setIsLoading(false);
    onSearch?.(searchQuery, filteredResults);
  };

  // Handle input change with debouncing
  const handleInputChange = (value: string) => {
    setQuery(value);
    setSelectedIndex(-1);

    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      performSearch(value);
    }, 300);
  };

  // Handle search submission
  const handleSearch = (searchQuery?: string) => {
    const finalQuery = searchQuery || query;
    if (!finalQuery.trim()) return;

    // Add to search history
    const newHistoryItem: SearchHistory = {
      id: Date.now().toString(),
      query: finalQuery,
      timestamp: new Date().toISOString(),
      resultCount: results.length
    };

    const updatedHistory = [newHistoryItem, ...history.slice(0, 9)]; // Keep last 10 searches
    setHistory(updatedHistory);
    localStorage.setItem('search-history', JSON.stringify(updatedHistory));

    // Navigate to search results page
    router.push(`/search?q=${encodeURIComponent(finalQuery)}`);
    setIsOpen(false);
    setQuery('');
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < results.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && results[selectedIndex]) {
          router.push(results[selectedIndex].url);
          setIsOpen(false);
          setQuery('');
        } else {
          handleSearch();
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'cve':
        return <Shield className="h-4 w-4 text-red-400" />;
      case 'application':
        return <Database className="h-4 w-4 text-blue-400" />;
      case 'component':
        return <FileText className="h-4 w-4 text-green-400" />;
      default:
        return <Search className="h-4 w-4 text-slate-400" />;
    }
  };

  const getSeverityColor = (severity?: string) => {
    switch (severity) {
      case 'CRITICAL':
        return 'bg-red-600 text-white';
      case 'HIGH':
        return 'bg-orange-600 text-white';
      case 'MEDIUM':
        return 'bg-yellow-600 text-white';
      case 'LOW':
        return 'bg-green-600 text-white';
      default:
        return 'bg-slate-600 text-white';
    }
  };

  return (
    <div ref={searchRef} className={cn("relative w-full max-w-md", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-slate-400" />
        <Input
          ref={inputRef}
          type="search"
          placeholder={placeholder}
          value={query}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onKeyDown={handleKeyDown}
          className="pl-10 pr-10 bg-slate-800 border-slate-700 text-slate-50 placeholder:text-slate-400 focus:border-cyan-400 focus:ring-cyan-400"
        />
        {query && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setQuery('');
              setResults([]);
              setIsOpen(false);
            }}
            className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0 text-slate-400 hover:text-slate-300"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Search Results Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-slate-800 border border-slate-700 rounded-md shadow-lg z-50 max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="h-4 w-4 animate-spin text-slate-400" />
              <span className="ml-2 text-sm text-slate-400">Searching...</span>
            </div>
          ) : results.length > 0 ? (
            <div className="py-2">
              {results.map((result, index) => (
                <button
                  key={result.id}
                  onClick={() => {
                    router.push(result.url);
                    setIsOpen(false);
                    setQuery('');
                  }}
                  className={cn(
                    "w-full px-4 py-3 text-left hover:bg-slate-700 focus:bg-slate-700 focus:outline-none transition-colors",
                    selectedIndex === index && "bg-slate-700"
                  )}
                >
                  <div className="flex items-start space-x-3">
                    {getTypeIcon(result.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-sm font-medium text-slate-50 truncate">
                          {result.title}
                        </span>
                        {result.severity && (
                          <Badge className={cn("text-xs", getSeverityColor(result.severity))}>
                            {result.severity}
                          </Badge>
                        )}
                      </div>
                      {result.subtitle && (
                        <p className="text-xs text-slate-400 mb-1">{result.subtitle}</p>
                      )}
                      {result.description && (
                        <p className="text-xs text-slate-500 line-clamp-2">{result.description}</p>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          ) : query ? (
            <div className="p-4 text-center">
              <p className="text-sm text-slate-400">No results found for "{query}"</p>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleSearch()}
                className="mt-2 text-cyan-400 hover:text-cyan-300"
              >
                Search all results
              </Button>
            </div>
          ) : showHistory && history.length > 0 ? (
            <div className="py-2">
              <div className="px-4 py-2 text-xs font-medium text-slate-400 uppercase tracking-wide">
                Recent Searches
              </div>
              {history.slice(0, 5).map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleSearch(item.query)}
                  className="w-full px-4 py-2 text-left hover:bg-slate-700 focus:bg-slate-700 focus:outline-none transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <Clock className="h-3 w-3 text-slate-500" />
                    <span className="text-sm text-slate-300">{item.query}</span>
                    <span className="text-xs text-slate-500 ml-auto">
                      {item.resultCount} results
                    </span>
                  </div>
                </button>
              ))}
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
}
