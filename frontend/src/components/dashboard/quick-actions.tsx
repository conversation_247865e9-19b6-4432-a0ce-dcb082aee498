'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Shield,
  Database,
  FileText,
  Settings,
  Search,
  Plus,
  AlertTriangle,
  TrendingUp,
  Users,
  Bell,
  Download,
  RefreshCw,
  Eye,
  Filter,
  BarChart3,
  Zap,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  href?: string;
  onClick?: () => void;
  badge?: string | number;
  color: 'default' | 'primary' | 'secondary' | 'warning' | 'danger';
  category: 'primary' | 'secondary' | 'management' | 'reports';
}

interface QuickActionsProps {
  onActionClick?: (actionId: string) => void;
  className?: string;
}

export function QuickActions({ onActionClick, className }: QuickActionsProps) {
  const router = useRouter();
  const [recentActions, setRecentActions] = useState<string[]>([]);

  const quickActions: QuickAction[] = [
    // Primary Actions
    {
      id: 'view-critical-cves',
      title: 'Critical CVEs',
      description: 'View all critical vulnerabilities requiring immediate attention',
      icon: AlertTriangle,
      href: '/cves?severity=CRITICAL',
      badge: '23',
      color: 'danger',
      category: 'primary',
    },
    {
      id: 'scan-applications',
      title: 'Scan Applications',
      description: 'Run vulnerability scans on registered applications',
      icon: Shield,
      onClick: () => console.log('Starting application scan...'),
      color: 'primary',
      category: 'primary',
    },
    {
      id: 'add-application',
      title: 'Add Application',
      description: 'Register a new application for monitoring',
      icon: Plus,
      href: '/applications/new',
      color: 'primary',
      category: 'primary',
    },
    {
      id: 'global-search',
      title: 'Advanced Search',
      description: 'Search across CVEs, applications, and components',
      icon: Search,
      href: '/search',
      color: 'secondary',
      category: 'primary',
    },

    // Secondary Actions
    {
      id: 'view-all-cves',
      title: 'All CVEs',
      description: 'Browse complete vulnerability database',
      icon: Database,
      href: '/cves',
      badge: '1,247',
      color: 'default',
      category: 'secondary',
    },
    {
      id: 'manage-applications',
      title: 'Applications',
      description: 'Manage registered applications and environments',
      icon: Database,
      href: '/applications',
      badge: '45',
      color: 'default',
      category: 'secondary',
    },
    {
      id: 'view-notifications',
      title: 'Notifications',
      description: 'Check security alerts and system notifications',
      icon: Bell,
      href: '/notifications',
      badge: '12',
      color: 'warning',
      category: 'secondary',
    },
    {
      id: 'watchlist',
      title: 'Watchlist',
      description: 'Monitor tracked CVEs and applications',
      icon: Eye,
      href: '/watchlist',
      badge: '8',
      color: 'secondary',
      category: 'secondary',
    },

    // Management Actions
    {
      id: 'user-management',
      title: 'User Management',
      description: 'Manage user accounts and permissions',
      icon: Users,
      href: '/admin/users',
      color: 'default',
      category: 'management',
    },
    {
      id: 'system-settings',
      title: 'Settings',
      description: 'Configure system preferences and integrations',
      icon: Settings,
      href: '/settings',
      color: 'default',
      category: 'management',
    },
    {
      id: 'refresh-feeds',
      title: 'Refresh Feeds',
      description: 'Update CVE feeds and vulnerability data',
      icon: RefreshCw,
      onClick: () => console.log('Refreshing CVE feeds...'),
      color: 'secondary',
      category: 'management',
    },
    {
      id: 'filter-management',
      title: 'Filter Presets',
      description: 'Create and manage custom filter presets',
      icon: Filter,
      href: '/settings/filters',
      color: 'default',
      category: 'management',
    },

    // Reports Actions
    {
      id: 'security-report',
      title: 'Security Report',
      description: 'Generate comprehensive security assessment',
      icon: FileText,
      href: '/reports/security',
      color: 'primary',
      category: 'reports',
    },
    {
      id: 'compliance-report',
      title: 'Compliance Report',
      description: 'Generate compliance and audit reports',
      icon: BarChart3,
      href: '/reports/compliance',
      color: 'default',
      category: 'reports',
    },
    {
      id: 'trend-analysis',
      title: 'Trend Analysis',
      description: 'Analyze vulnerability trends and patterns',
      icon: TrendingUp,
      href: '/reports/trends',
      color: 'secondary',
      category: 'reports',
    },
    {
      id: 'export-data',
      title: 'Export Data',
      description: 'Export CVE data in various formats',
      icon: Download,
      onClick: () => console.log('Opening export dialog...'),
      color: 'default',
      category: 'reports',
    },
  ];

  const handleActionClick = (action: QuickAction) => {
    // Track recent actions
    setRecentActions(prev => {
      const updated = [action.id, ...prev.filter(id => id !== action.id)].slice(0, 5);
      return updated;
    });

    // Execute action
    if (action.onClick) {
      action.onClick();
    } else if (action.href) {
      router.push(action.href);
    }

    onActionClick?.(action.id);
  };

  const getColorClasses = (color: QuickAction['color']) => {
    switch (color) {
      case 'primary':
        return 'bg-cyan-600 hover:bg-cyan-700 text-white border-cyan-600';
      case 'danger':
        return 'bg-red-600 hover:bg-red-700 text-white border-red-600';
      case 'warning':
        return 'bg-orange-600 hover:bg-orange-700 text-white border-orange-600';
      case 'secondary':
        return 'bg-slate-600 hover:bg-slate-700 text-white border-slate-600';
      default:
        return 'bg-slate-700 hover:bg-slate-600 text-slate-50 border-slate-600';
    }
  };

  const groupedActions = quickActions.reduce((acc, action) => {
    if (!acc[action.category]) {
      acc[action.category] = [];
    }
    acc[action.category].push(action);
    return acc;
  }, {} as Record<string, QuickAction[]>);

  const categoryTitles = {
    primary: 'Primary Actions',
    secondary: 'Quick Access',
    management: 'Management',
    reports: 'Reports & Analytics',
  };

  return (
    <div className={cn("space-y-6", className)}>
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-slate-50 flex items-center">
            <Zap className="h-5 w-5 mr-2" />
            Quick Actions
          </CardTitle>
          <CardDescription className="text-slate-400">
            Common tasks and shortcuts for vulnerability management
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {Object.entries(groupedActions).map(([category, actions]) => (
            <div key={category} className="space-y-3">
              <h3 className="text-sm font-medium text-slate-300 uppercase tracking-wide">
                {categoryTitles[category as keyof typeof categoryTitles]}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                {actions.map((action) => {
                  const Icon = action.icon;
                  const isRecent = recentActions.includes(action.id);
                  
                  return (
                    <Button
                      key={action.id}
                      onClick={() => handleActionClick(action)}
                      variant="outline"
                      className={cn(
                        "h-auto p-4 flex flex-col items-start space-y-2 text-left relative transition-all duration-200",
                        "border-slate-600 hover:border-slate-500 hover:bg-slate-700/50",
                        isRecent && "ring-2 ring-cyan-400/50"
                      )}
                    >
                      <div className="flex items-center justify-between w-full">
                        <Icon className="h-5 w-5 text-slate-400" />
                        {action.badge && (
                          <Badge 
                            variant="secondary" 
                            className={cn(
                              "text-xs",
                              action.color === 'danger' ? 'bg-red-600 text-white' :
                              action.color === 'warning' ? 'bg-orange-600 text-white' :
                              action.color === 'primary' ? 'bg-cyan-600 text-white' :
                              'bg-slate-600 text-slate-300'
                            )}
                          >
                            {action.badge}
                          </Badge>
                        )}
                      </div>
                      <div className="space-y-1">
                        <p className="font-medium text-slate-50 text-sm">{action.title}</p>
                        <p className="text-xs text-slate-400 line-clamp-2">{action.description}</p>
                      </div>
                      {isRecent && (
                        <div className="absolute top-2 right-2">
                          <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse" />
                        </div>
                      )}
                    </Button>
                  );
                })}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Recent Actions Summary */}
      {recentActions.length > 0 && (
        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-cyan-400 rounded-full" />
                <span className="text-sm text-slate-300">Recent Actions</span>
              </div>
              <div className="flex items-center space-x-1">
                {recentActions.slice(0, 3).map((actionId) => {
                  const action = quickActions.find(a => a.id === actionId);
                  if (!action) return null;
                  const Icon = action.icon;
                  return (
                    <div
                      key={actionId}
                      className="p-1 rounded bg-slate-700 text-slate-400"
                      title={action.title}
                    >
                      <Icon className="h-3 w-3" />
                    </div>
                  );
                })}
                {recentActions.length > 3 && (
                  <Badge variant="secondary" className="text-xs bg-slate-700 text-slate-400">
                    +{recentActions.length - 3}
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
