'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Shield,
  AlertTriangle,
  TrendingUp,
  Database,
  Activity,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  RefreshCw,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MetricData {
  current: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  percentage?: number;
  target?: number;
}

interface DashboardMetrics {
  totalCVEs: MetricData;
  criticalCVEs: MetricData;
  highCVEs: MetricData;
  applications: MetricData;
  componentsScanned: MetricData;
  lastUpdate: string;
  isLoading: boolean;
}

interface MetricsCardsProps {
  onRefresh?: () => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function MetricsCards({ 
  onRefresh, 
  autoRefresh = true, 
  refreshInterval = 30000 
}: MetricsCardsProps) {
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    totalCVEs: { current: 1247, change: 23, trend: 'up', percentage: 75 },
    criticalCVEs: { current: 23, change: 12, trend: 'up', target: 10 },
    highCVEs: { current: 156, change: -5, trend: 'down', target: 100 },
    applications: { current: 45, change: 3, trend: 'up', percentage: 90 },
    componentsScanned: { current: 1234, change: 89, trend: 'up', percentage: 85 },
    lastUpdate: new Date().toISOString(),
    isLoading: false,
  });

  const [isRefreshing, setIsRefreshing] = useState(false);

  // Simulate real-time data fetching
  const fetchMetrics = async () => {
    setIsRefreshing(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate data updates with small random changes
      setMetrics(prev => ({
        ...prev,
        totalCVEs: {
          ...prev.totalCVEs,
          current: prev.totalCVEs.current + Math.floor(Math.random() * 5) - 2,
          change: Math.floor(Math.random() * 10) - 5,
        },
        criticalCVEs: {
          ...prev.criticalCVEs,
          current: Math.max(0, prev.criticalCVEs.current + Math.floor(Math.random() * 3) - 1),
          change: Math.floor(Math.random() * 6) - 3,
        },
        highCVEs: {
          ...prev.highCVEs,
          current: Math.max(0, prev.highCVEs.current + Math.floor(Math.random() * 8) - 4),
          change: Math.floor(Math.random() * 10) - 5,
        },
        lastUpdate: new Date().toISOString(),
      }));
      
      onRefresh?.();
    } catch (error) {
      console.error('Failed to fetch metrics:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchMetrics, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  const getTrendIcon = (trend: 'up' | 'down' | 'stable', isPositive = false) => {
    const colorClass = trend === 'up' 
      ? (isPositive ? 'text-green-500' : 'text-red-500')
      : trend === 'down' 
      ? (isPositive ? 'text-red-500' : 'text-green-500')
      : 'text-slate-500';

    switch (trend) {
      case 'up':
        return <ArrowUpRight className={cn("h-3 w-3", colorClass)} />;
      case 'down':
        return <ArrowDownRight className={cn("h-3 w-3", colorClass)} />;
      case 'stable':
        return <Minus className={cn("h-3 w-3", colorClass)} />;
    }
  };

  const formatLastUpdate = (timestamp: string) => {
    const now = new Date();
    const updated = new Date(timestamp);
    const diffMs = now.getTime() - updated.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    return updated.toLocaleDateString();
  };

  const MetricCard = ({ 
    title, 
    icon: Icon, 
    metric, 
    color = 'slate',
    showProgress = false,
    showTarget = false,
    isPositiveTrend = false 
  }: {
    title: string;
    icon: React.ComponentType<{ className?: string }>;
    metric: MetricData;
    color?: 'slate' | 'red' | 'orange' | 'green' | 'blue';
    showProgress?: boolean;
    showTarget?: boolean;
    isPositiveTrend?: boolean;
  }) => {
    const colorClasses = {
      slate: 'text-slate-400',
      red: 'text-red-400',
      orange: 'text-orange-400',
      green: 'text-green-400',
      blue: 'text-blue-400',
    };

    return (
      <Card className="bg-slate-800 border-slate-700 hover:bg-slate-800/80 transition-colors">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-slate-300">{title}</CardTitle>
          <Icon className={cn("h-4 w-4", colorClasses[color])} />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-slate-50 mb-1">
            {metric.current.toLocaleString()}
          </div>
          
          <div className="flex items-center space-x-2 text-xs text-slate-400 mb-2">
            {getTrendIcon(metric.trend, isPositiveTrend)}
            <span>
              {Math.abs(metric.change)} {metric.trend === 'stable' ? 'no change' : 'from last week'}
            </span>
          </div>

          {showProgress && metric.percentage && (
            <div className="space-y-1">
              <Progress value={metric.percentage} className="h-1" />
              <div className="text-xs text-slate-500">
                {metric.percentage}% of capacity
              </div>
            </div>
          )}

          {showTarget && metric.target && (
            <div className="text-xs">
              <span className={cn(
                "font-medium",
                metric.current > metric.target ? "text-red-400" : "text-green-400"
              )}>
                Target: {metric.target}
              </span>
              {metric.current > metric.target && (
                <span className="text-red-400 ml-2">
                  ({metric.current - metric.target} over)
                </span>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-4">
      {/* Header with refresh controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-slate-50">Security Metrics</h2>
          <p className="text-sm text-slate-400">
            Last updated: {formatLastUpdate(metrics.lastUpdate)}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge 
            variant={autoRefresh ? "default" : "secondary"} 
            className="text-xs"
          >
            {autoRefresh ? 'Auto-refresh ON' : 'Auto-refresh OFF'}
          </Badge>
          <button
            onClick={fetchMetrics}
            disabled={isRefreshing}
            className={cn(
              "p-2 rounded-md border border-slate-600 text-slate-400 hover:text-slate-300 hover:bg-slate-700 transition-colors",
              isRefreshing && "animate-spin"
            )}
          >
            <RefreshCw className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total CVEs"
          icon={Shield}
          metric={metrics.totalCVEs}
          color="slate"
          showProgress
        />
        
        <MetricCard
          title="Critical CVEs"
          icon={AlertTriangle}
          metric={metrics.criticalCVEs}
          color="red"
          showTarget
          isPositiveTrend={false}
        />
        
        <MetricCard
          title="High Severity"
          icon={TrendingUp}
          metric={metrics.highCVEs}
          color="orange"
          showTarget
          isPositiveTrend={false}
        />
        
        <MetricCard
          title="Applications"
          icon={Database}
          metric={metrics.applications}
          color="blue"
          showProgress
          isPositiveTrend
        />
      </div>

      {/* Additional metrics row */}
      <div className="grid gap-4 md:grid-cols-3">
        <MetricCard
          title="Components Scanned"
          icon={Activity}
          metric={metrics.componentsScanned}
          color="green"
          showProgress
          isPositiveTrend
        />
        
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-300 flex items-center">
              <Clock className="h-4 w-4 mr-2 text-slate-400" />
              System Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs text-slate-400">API Response</span>
                <Badge variant="secondary" className="text-xs bg-green-900 text-green-300">
                  98ms
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-slate-400">Database</span>
                <Badge variant="secondary" className="text-xs bg-green-900 text-green-300">
                  Healthy
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-slate-400">Feed Status</span>
                <Badge variant="secondary" className="text-xs bg-green-900 text-green-300">
                  Active
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-300">
              Quick Stats
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-xs">
              <div className="flex justify-between">
                <span className="text-slate-400">New today:</span>
                <span className="text-slate-50 font-medium">12 CVEs</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">Resolved:</span>
                <span className="text-green-400 font-medium">8 CVEs</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">In progress:</span>
                <span className="text-yellow-400 font-medium">15 CVEs</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
