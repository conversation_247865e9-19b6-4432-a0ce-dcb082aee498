'use client';

import { <PERSON>, <PERSON>, User, Moon, <PERSON>, LogOut, Setting<PERSON>, Shield } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { GlobalSearchBar } from '@/components/search/global-search-bar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAuthStore } from '@/stores/auth-store';
import { useState } from 'react';
import { useRouter } from 'next/navigation';

interface HeaderProps {
  user?: {
    id: string;
    username: string;
    email: string;
    full_name: string;
    role: string;
  } | null;
  onLogout?: () => void;
  onToggleTheme?: () => void;
  notifications?: Array<{
    id: string;
    title: string;
    type: 'info' | 'warning' | 'error';
    unread: boolean;
  }>;
}

export function Header({ user, onLogout, onToggleTheme, notifications = [] }: HeaderProps) {
  const { logout } = useAuthStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [theme, setTheme] = useState('dark');
  const router = useRouter();

  const unreadCount = notifications.filter(n => n.unread).length;

  const handleLogout = () => {
    logout();
    onLogout?.();
    router.push('/login');
  };

  const handleThemeToggle = () => {
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
    onToggleTheme?.();
  };

  const handleGlobalSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery)}`);
    }
  };

  return (
    <header 
      className="border-b border-slate-700 bg-slate-900/95 backdrop-blur supports-[backdrop-filter]:bg-slate-900/60"
      data-testid="app-header"
    >
      <div className="container flex h-16 items-center px-4">
        {/* Logo and Brand */}
        <div className="mr-6 flex items-center space-x-2">
          <Shield className="h-6 w-6 text-cyan-400" />
          <span className="hidden font-bold text-slate-50 sm:inline-block">
            CVE Feed Service
          </span>
        </div>

        {/* Global Search */}
        <div className="flex flex-1 items-center justify-between space-x-4">
          <GlobalSearchBar
            placeholder="Search CVEs, applications, components..."
            onSearch={(query, results) => {
              console.log('Search performed:', query, results);
            }}
          />

          {/* Right Side Actions */}
          <nav className="flex items-center space-x-2">
            {/* Theme Toggle */}
            <Button 
              variant="ghost" 
              size="sm"
              onClick={handleThemeToggle}
              className="text-slate-400 hover:text-slate-50 hover:bg-slate-800"
              data-testid="theme-toggle"
            >
              {theme === 'dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
            </Button>

            {/* Notifications */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="relative text-slate-400 hover:text-slate-50 hover:bg-slate-800"
                  data-testid="notifications-trigger"
                >
                  <Bell className="h-4 w-4" />
                  {unreadCount > 0 && (
                    <Badge 
                      variant="destructive" 
                      className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs"
                    >
                      {unreadCount}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-80 bg-slate-800 border-slate-700">
                <DropdownMenuLabel className="text-slate-50">Notifications</DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-slate-700" />
                {notifications.length > 0 ? (
                  notifications.slice(0, 5).map((notification) => (
                    <DropdownMenuItem 
                      key={notification.id}
                      className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700"
                    >
                      <div className="flex flex-col space-y-1">
                        <span className="text-sm font-medium">{notification.title}</span>
                        <Badge 
                          variant={notification.type === 'error' ? 'destructive' : 'secondary'}
                          className="w-fit"
                        >
                          {notification.type}
                        </Badge>
                      </div>
                    </DropdownMenuItem>
                  ))
                ) : (
                  <DropdownMenuItem className="text-slate-400">
                    No new notifications
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="text-slate-400 hover:text-slate-50 hover:bg-slate-800"
                  data-testid="user-menu-trigger"
                >
                  <User className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56 bg-slate-800 border-slate-700">
                <DropdownMenuLabel className="text-slate-50">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium">{user?.full_name || 'User'}</p>
                    <p className="text-xs text-slate-400">{user?.email}</p>
                    <Badge variant="outline" className="w-fit text-xs">
                      {user?.role || 'user'}
                    </Badge>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-slate-700" />
                <DropdownMenuItem className="text-slate-300 hover:bg-slate-700 focus:bg-slate-700">
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </DropdownMenuItem>
                <DropdownMenuSeparator className="bg-slate-700" />
                <DropdownMenuItem 
                  onClick={handleLogout}
                  className="text-red-400 hover:bg-slate-700 focus:bg-slate-700"
                  data-testid="logout-button"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </nav>
        </div>
      </div>
    </header>
  );
}
