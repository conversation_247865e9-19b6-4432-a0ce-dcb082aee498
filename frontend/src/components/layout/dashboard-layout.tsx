'use client';

import { ReactNode } from 'react';
import { Head<PERSON> } from './header';
import { Sidebar } from './sidebar';
import { useAuthStore } from '@/stores/auth-store';

interface DashboardLayoutProps {
  children: ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const { user, isAuthenticated } = useAuthStore();

  // Mock notifications for demo
  const notifications = [
    {
      id: '1',
      title: 'New critical CVE detected: CVE-2024-0001',
      type: 'error' as const,
      unread: true,
    },
    {
      id: '2',
      title: 'Application scan completed',
      type: 'info' as const,
      unread: true,
    },
    {
      id: '3',
      title: 'Weekly security report available',
      type: 'info' as const,
      unread: false,
    },
  ];

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-slate-50">Please log in to access the dashboard.</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900" data-testid="dashboard-layout">
      {/* Header */}
      <Header
        user={user}
        notifications={notifications}
      />

      <div className="flex h-[calc(100vh-4rem)]">
        {/* Sidebar */}
        <aside className="w-64 border-r border-slate-700 bg-slate-900 overflow-y-auto">
          <Sidebar user={user} />
        </aside>

        {/* Main content */}
        <main className="flex-1 overflow-auto bg-slate-900">
          <div className="container mx-auto p-6 max-w-7xl">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
