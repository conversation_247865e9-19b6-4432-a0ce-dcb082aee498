'use client';

import { useState } from 'react';
import { Download, FileText, FileSpreadsheet, Code, Database, Calendar, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface ExportOptions {
  format: 'csv' | 'json' | 'xml' | 'pdf';
  fields: string[];
  filters: {
    severity: string[];
    dateRange: string;
    includeReferences: boolean;
    includeAffectedProducts: boolean;
  };
  limit?: number;
}

interface ExportDialogProps {
  totalCVEs: number;
  filteredCVEs: number;
  currentFilters?: any;
  onExport: (options: ExportOptions) => Promise<void>;
}

export function ExportDialog({ totalCVEs, filteredCVEs, currentFilters, onExport }: ExportDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'csv',
    fields: ['cve_id', 'severity', 'cvss_score', 'description', 'published_date'],
    filters: {
      severity: [],
      dateRange: 'all',
      includeReferences: true,
      includeAffectedProducts: true,
    },
  });

  const availableFields = [
    { id: 'cve_id', label: 'CVE ID', required: true },
    { id: 'severity', label: 'Severity', required: false },
    { id: 'cvss_score', label: 'CVSS Score', required: false },
    { id: 'description', label: 'Description', required: false },
    { id: 'published_date', label: 'Published Date', required: false },
    { id: 'modified_date', label: 'Modified Date', required: false },
    { id: 'cwe_id', label: 'CWE ID', required: false },
    { id: 'attack_vector', label: 'Attack Vector', required: false },
    { id: 'exploitability_score', label: 'Exploitability Score', required: false },
    { id: 'impact_score', label: 'Impact Score', required: false },
  ];

  const formatOptions = [
    {
      value: 'csv',
      label: 'CSV (Comma Separated)',
      icon: FileSpreadsheet,
      description: 'Best for spreadsheet applications',
      extension: '.csv'
    },
    {
      value: 'json',
      label: 'JSON (JavaScript Object)',
      icon: Code,
      description: 'Best for programmatic access',
      extension: '.json'
    },
    {
      value: 'xml',
      label: 'XML (Extensible Markup)',
      icon: Database,
      description: 'Best for system integration',
      extension: '.xml'
    },
    {
      value: 'pdf',
      label: 'PDF (Portable Document)',
      icon: FileText,
      description: 'Best for reports and documentation',
      extension: '.pdf'
    },
  ];

  const handleFieldToggle = (fieldId: string) => {
    const field = availableFields.find(f => f.id === fieldId);
    if (field?.required) return; // Don't allow toggling required fields

    setExportOptions(prev => ({
      ...prev,
      fields: prev.fields.includes(fieldId)
        ? prev.fields.filter(f => f !== fieldId)
        : [...prev.fields, fieldId]
    }));
  };

  const handleSeverityToggle = (severity: string) => {
    setExportOptions(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        severity: prev.filters.severity.includes(severity)
          ? prev.filters.severity.filter(s => s !== severity)
          : [...prev.filters.severity, severity]
      }
    }));
  };

  const handleExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      await onExport(exportOptions);
      
      clearInterval(progressInterval);
      setExportProgress(100);
      
      // Close dialog after successful export
      setTimeout(() => {
        setIsOpen(false);
        setIsExporting(false);
        setExportProgress(0);
      }, 1000);
    } catch (error) {
      console.error('Export failed:', error);
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  const getEstimatedCount = () => {
    if (exportOptions.filters.severity.length === 0) {
      return filteredCVEs;
    }
    // This would be calculated based on actual filters in a real app
    return Math.floor(filteredCVEs * 0.7);
  };

  const selectedFormat = formatOptions.find(f => f.value === exportOptions.format);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-700">
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-slate-800 border-slate-700 text-slate-50 max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Download className="h-5 w-5 mr-2" />
            Export CVE Data
          </DialogTitle>
          <DialogDescription className="text-slate-400">
            Configure your export settings and download CVE data in your preferred format.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Export Format */}
          <div className="space-y-3">
            <Label className="text-slate-300 font-medium">Export Format</Label>
            <div className="grid grid-cols-2 gap-3">
              {formatOptions.map((format) => {
                const Icon = format.icon;
                return (
                  <button
                    key={format.value}
                    onClick={() => setExportOptions(prev => ({ ...prev, format: format.value as any }))}
                    className={`p-3 rounded-lg border text-left transition-colors ${
                      exportOptions.format === format.value
                        ? 'border-cyan-400 bg-cyan-400/10'
                        : 'border-slate-600 hover:border-slate-500'
                    }`}
                  >
                    <div className="flex items-center space-x-2 mb-1">
                      <Icon className="h-4 w-4" />
                      <span className="font-medium text-sm">{format.label}</span>
                    </div>
                    <p className="text-xs text-slate-400">{format.description}</p>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Fields Selection */}
          <div className="space-y-3">
            <Label className="text-slate-300 font-medium">Fields to Include</Label>
            <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
              {availableFields.map((field) => (
                <div key={field.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={field.id}
                    checked={exportOptions.fields.includes(field.id)}
                    onCheckedChange={() => handleFieldToggle(field.id)}
                    disabled={field.required}
                    className="border-slate-600 data-[state=checked]:bg-cyan-500 data-[state=checked]:border-cyan-500"
                  />
                  <Label 
                    htmlFor={field.id} 
                    className={`text-sm ${field.required ? 'text-slate-400' : 'text-slate-300'}`}
                  >
                    {field.label}
                    {field.required && <span className="text-cyan-400 ml-1">*</span>}
                  </Label>
                </div>
              ))}
            </div>
            <p className="text-xs text-slate-500">* Required fields cannot be deselected</p>
          </div>

          {/* Additional Options */}
          <div className="space-y-3">
            <Label className="text-slate-300 font-medium">Additional Options</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeReferences"
                  checked={exportOptions.filters.includeReferences}
                  onCheckedChange={(checked) => 
                    setExportOptions(prev => ({
                      ...prev,
                      filters: { ...prev.filters, includeReferences: !!checked }
                    }))
                  }
                  className="border-slate-600 data-[state=checked]:bg-cyan-500 data-[state=checked]:border-cyan-500"
                />
                <Label htmlFor="includeReferences" className="text-sm text-slate-300">
                  Include reference URLs
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeAffectedProducts"
                  checked={exportOptions.filters.includeAffectedProducts}
                  onCheckedChange={(checked) => 
                    setExportOptions(prev => ({
                      ...prev,
                      filters: { ...prev.filters, includeAffectedProducts: !!checked }
                    }))
                  }
                  className="border-slate-600 data-[state=checked]:bg-cyan-500 data-[state=checked]:border-cyan-500"
                />
                <Label htmlFor="includeAffectedProducts" className="text-sm text-slate-300">
                  Include affected products list
                </Label>
              </div>
            </div>
          </div>

          {/* Severity Filter */}
          <div className="space-y-3">
            <Label className="text-slate-300 font-medium">Filter by Severity (Optional)</Label>
            <div className="flex flex-wrap gap-2">
              {['CRITICAL', 'HIGH', 'MEDIUM', 'LOW'].map((severity) => (
                <Button
                  key={severity}
                  variant={exportOptions.filters.severity.includes(severity) ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleSeverityToggle(severity)}
                  className={`text-xs ${
                    exportOptions.filters.severity.includes(severity)
                      ? severity === 'CRITICAL' ? 'bg-red-600 hover:bg-red-700' :
                        severity === 'HIGH' ? 'bg-orange-600 hover:bg-orange-700' :
                        severity === 'MEDIUM' ? 'bg-yellow-600 hover:bg-yellow-700' :
                        'bg-green-600 hover:bg-green-700'
                      : 'border-slate-600 text-slate-300 hover:bg-slate-700'
                  }`}
                >
                  {severity}
                </Button>
              ))}
            </div>
          </div>

          {/* Export Summary */}
          <div className="bg-slate-700/50 rounded-lg p-4 space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-300">Estimated records:</span>
              <Badge variant="secondary" className="bg-slate-600 text-slate-300">
                {getEstimatedCount().toLocaleString()}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-300">File format:</span>
              <Badge variant="secondary" className="bg-slate-600 text-slate-300">
                {selectedFormat?.extension}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-300">Selected fields:</span>
              <Badge variant="secondary" className="bg-slate-600 text-slate-300">
                {exportOptions.fields.length}
              </Badge>
            </div>
          </div>

          {/* Export Progress */}
          {isExporting && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-300">Export Progress</span>
                <span className="text-sm text-slate-400">{exportProgress}%</span>
              </div>
              <Progress value={exportProgress} className="h-2" />
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-end space-x-2 pt-4 border-t border-slate-700">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isExporting}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              Cancel
            </Button>
            <Button
              onClick={handleExport}
              disabled={isExporting || exportOptions.fields.length === 0}
              className="bg-cyan-600 hover:bg-cyan-700"
            >
              {isExporting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export Data
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
