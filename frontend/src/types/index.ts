// Authentication-related type definitions
export interface User {
  id: string;
  username: string;
  email: string;
  full_name: string;
  role: User<PERSON><PERSON>;
  is_active: boolean;
  is_superuser: boolean;
  created_at: string;
  updated_at?: string;
}

export type UserRole =
  | 'security_analyst'
  | 'it_admin'
  | 'application_owner'
  | 'read_only';

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  user: User;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  full_name: string;
  role: UserRole;
}

// CVE-related type definitions
export interface CVE {
  id: string;
  cve_id: string;
  description: string;
  published_date: string;
  last_modified_date: string;
  cvss_v3_score?: number;
  cvss_v3_vector?: string;
  cvss_v3_severity?: string;
  source: string;
  references?: string[];
  cpe_configurations?: string[];
  created_at: string;
  updated_at?: string;
}

export interface CVEListResponse {
  cves: CVE[];
  total: number;
  skip: number;
  limit: number;
}

// Application-related type definitions
export interface Application {
  id: string;
  name: string;
  description?: string;
  environment: string;
  criticality: string;
  owner?: string;
  created_at: string;
  updated_at?: string;
}

export interface ApplicationCreateRequest {
  name: string;
  description?: string;
  environment: string;
  criticality: string;
  owner?: string;
}

// API Response wrapper
export interface ApiResponse<T> {
  data?: T;
  message?: string;
  detail?: string;
  success: boolean;
}

// Error types
export interface ApiError {
  detail: string;
  status_code: number;
}

// Dashboard types
export interface DashboardStats {
  total_cves: number;
  critical_cves: number;
  high_cves: number;
  total_applications: number;
  recent_cves: CVE[];
}

// Notification types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

// Form types for validation
export interface LoginFormData {
  username: string;
  password: string;
  rememberMe?: boolean;
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Navigation types
export interface NavigationItem {
  name: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  badge?: string | number;
  children?: NavigationItem[];
}
