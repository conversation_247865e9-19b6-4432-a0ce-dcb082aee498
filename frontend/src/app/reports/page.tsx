'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText,
  Download,
  Calendar,
  Filter,
  BarChart3,
  PieChart,
  TrendingUp,
  Clock,
  Users,
  Shield,
  AlertTriangle,
  CheckCircle,
  Settings,
  Plus,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';

interface Report {
  id: string;
  name: string;
  description: string;
  type: 'vulnerability' | 'compliance' | 'application' | 'custom';
  format: 'pdf' | 'excel' | 'csv' | 'json';
  schedule: 'manual' | 'daily' | 'weekly' | 'monthly';
  created_by: string;
  created_at: string;
  last_run: string | null;
  status: 'active' | 'inactive' | 'running';
  parameters: Record<string, any>;
}

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: 'vulnerability' | 'compliance' | 'application' | 'custom';
  fields: string[];
  filters: Record<string, any>;
}

export default function ReportsPage() {
  const [reports, setReports] = useState<Report[]>([]);
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('reports');
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  // Mock data
  const mockReports: Report[] = [
    {
      id: '1',
      name: 'Weekly Vulnerability Summary',
      description: 'Comprehensive weekly report of all critical and high severity vulnerabilities',
      type: 'vulnerability',
      format: 'pdf',
      schedule: 'weekly',
      created_by: '<EMAIL>',
      created_at: '2024-01-15T10:30:00Z',
      last_run: '2024-01-22T09:00:00Z',
      status: 'active',
      parameters: {
        severity: ['CRITICAL', 'HIGH'],
        date_range: '7_days',
        include_charts: true
      }
    },
    {
      id: '2',
      name: 'Application Security Dashboard',
      description: 'Monthly security posture report for all applications in portfolio',
      type: 'application',
      format: 'excel',
      schedule: 'monthly',
      created_by: '<EMAIL>',
      created_at: '2024-01-10T14:20:00Z',
      last_run: '2024-01-01T08:00:00Z',
      status: 'active',
      parameters: {
        environment: ['production', 'staging'],
        criticality: ['critical', 'high'],
        include_trends: true
      }
    },
    {
      id: '3',
      name: 'Compliance Audit Report',
      description: 'Quarterly compliance status report for SOX and PCI-DSS requirements',
      type: 'compliance',
      format: 'pdf',
      schedule: 'manual',
      created_by: '<EMAIL>',
      created_at: '2024-01-05T16:45:00Z',
      last_run: null,
      status: 'inactive',
      parameters: {
        frameworks: ['SOX', 'PCI-DSS'],
        include_remediation: true,
        detailed_findings: true
      }
    }
  ];

  const mockTemplates: ReportTemplate[] = [
    {
      id: '1',
      name: 'Executive Security Summary',
      description: 'High-level security metrics for executive leadership',
      type: 'vulnerability',
      fields: ['total_cves', 'critical_count', 'trend_analysis', 'top_risks'],
      filters: { severity: ['CRITICAL', 'HIGH'], time_period: '30_days' }
    },
    {
      id: '2',
      name: 'Technical Vulnerability Details',
      description: 'Detailed technical report for security teams',
      type: 'vulnerability',
      fields: ['cve_details', 'cvss_scores', 'affected_systems', 'remediation_steps'],
      filters: { include_technical: true, detailed_analysis: true }
    },
    {
      id: '3',
      name: 'Application Risk Assessment',
      description: 'Risk assessment report for application portfolio',
      type: 'application',
      fields: ['app_inventory', 'risk_scores', 'vulnerability_mapping', 'compliance_status'],
      filters: { environment: 'all', include_dependencies: true }
    }
  ];

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 800));
      setReports(mockReports);
      setTemplates(mockTemplates);
      setLoading(false);
    };

    loadData();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'running': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'vulnerability': return 'bg-red-100 text-red-800 border-red-200';
      case 'compliance': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'application': return 'bg-green-100 text-green-800 border-green-200';
      case 'custom': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf': return <FileText className="h-4 w-4" />;
      case 'excel': return <BarChart3 className="h-4 w-4" />;
      case 'csv': return <FileText className="h-4 w-4" />;
      case 'json': return <FileText className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const handleRunReport = async (reportId: string) => {
    // Simulate report generation
    setReports(reports.map(report => 
      report.id === reportId 
        ? { ...report, status: 'running' as const }
        : report
    ));

    setTimeout(() => {
      setReports(reports.map(report => 
        report.id === reportId 
          ? { 
              ...report, 
              status: 'active' as const, 
              last_run: new Date().toISOString() 
            }
          : report
      ));
    }, 3000);
  };

  const handleDeleteReport = (reportId: string) => {
    if (confirm('Are you sure you want to delete this report?')) {
      setReports(reports.filter(report => report.id !== reportId));
    }
  };

  return (
    <div className="space-y-6" data-testid="reports-page">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-slate-50">Reports & Analytics</h1>
          <p className="text-slate-400 mt-1">
            Generate and manage security reports and compliance documentation
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
          <Button 
            onClick={() => setShowCreateDialog(true)}
            className="bg-cyan-600 hover:bg-cyan-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Report
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-slate-300">Total Reports</p>
                <p className="text-2xl font-bold text-slate-50">{reports.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-slate-300">Active</p>
                <p className="text-2xl font-bold text-green-400">
                  {reports.filter(r => r.status === 'active').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-slate-300">Scheduled</p>
                <p className="text-2xl font-bold text-orange-400">
                  {reports.filter(r => r.schedule !== 'manual').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-cyan-500" />
              <div>
                <p className="text-sm font-medium text-slate-300">This Month</p>
                <p className="text-2xl font-bold text-cyan-400">
                  {reports.filter(r => r.last_run && 
                    new Date(r.last_run) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                  ).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-slate-800 border-slate-700">
          <TabsTrigger value="reports" className="data-[state=active]:bg-slate-700">
            My Reports
          </TabsTrigger>
          <TabsTrigger value="templates" className="data-[state=active]:bg-slate-700">
            Templates
          </TabsTrigger>
          <TabsTrigger value="analytics" className="data-[state=active]:bg-slate-700">
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* Reports Tab */}
        <TabsContent value="reports" className="mt-6">
          {loading ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {Array.from({ length: 6 }).map((_, i) => (
                <Card key={i} className="bg-slate-800 border-slate-700 animate-pulse">
                  <CardContent className="p-6">
                    <div className="space-y-3">
                      <div className="h-4 bg-slate-700 rounded w-3/4"></div>
                      <div className="h-3 bg-slate-700 rounded w-1/2"></div>
                      <div className="h-20 bg-slate-700 rounded"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {reports.map((report) => (
                <Card key={report.id} className="bg-slate-800 border-slate-700 hover:border-slate-600 transition-colors">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg font-semibold text-slate-50 flex items-center space-x-2">
                          {getFormatIcon(report.format)}
                          <span>{report.name}</span>
                        </CardTitle>
                        <p className="text-sm text-slate-400 mt-1">{report.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 mt-2">
                      <Badge className={getTypeColor(report.type)}>
                        {report.type}
                      </Badge>
                      <Badge className={getStatusColor(report.status)}>
                        {report.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="space-y-4">
                      {/* Report Details */}
                      <div className="text-sm text-slate-300">
                        <div className="flex items-center justify-between">
                          <span>Schedule:</span>
                          <span className="capitalize">{report.schedule}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Format:</span>
                          <span className="uppercase">{report.format}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Last Run:</span>
                          <span>
                            {report.last_run 
                              ? new Date(report.last_run).toLocaleDateString()
                              : 'Never'
                            }
                          </span>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center justify-between pt-2 border-t border-slate-700">
                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRunReport(report.id)}
                            disabled={report.status === 'running'}
                            className="text-slate-400 hover:text-slate-50 hover:bg-slate-700"
                          >
                            <Download className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedReport(report)}
                            className="text-slate-400 hover:text-slate-50 hover:bg-slate-700"
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-slate-400 hover:text-slate-50 hover:bg-slate-700"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteReport(report.id)}
                          className="text-red-400 hover:text-red-300 hover:bg-slate-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Templates Tab */}
        <TabsContent value="templates" className="mt-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {templates.map((template) => (
              <Card key={template.id} className="bg-slate-800 border-slate-700 hover:border-slate-600 transition-colors">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-slate-50">{template.name}</CardTitle>
                  <CardDescription className="text-slate-400">{template.description}</CardDescription>
                  <Badge className={getTypeColor(template.type)}>
                    {template.type}
                  </Badge>
                </CardHeader>

                <CardContent>
                  <div className="space-y-4">
                    {/* Template Fields */}
                    <div>
                      <h4 className="text-sm font-medium text-slate-300 mb-2">Included Fields</h4>
                      <div className="flex flex-wrap gap-1">
                        {template.fields.slice(0, 3).map((field, index) => (
                          <Badge key={index} variant="secondary" className="text-xs bg-slate-700 text-slate-300">
                            {field.replace('_', ' ')}
                          </Badge>
                        ))}
                        {template.fields.length > 3 && (
                          <Badge variant="secondary" className="text-xs bg-slate-700 text-slate-300">
                            +{template.fields.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center justify-between pt-2 border-t border-slate-700">
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-slate-600 text-slate-300 hover:bg-slate-700"
                      >
                        Use Template
                      </Button>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-slate-400 hover:text-slate-50 hover:bg-slate-700"
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-slate-400 hover:text-slate-50 hover:bg-slate-700"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="mt-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Report Usage Analytics */}
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-slate-50 flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Report Usage Analytics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-slate-300">Most Generated Report</span>
                    <span className="text-slate-50 font-medium">Weekly Vulnerability Summary</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-slate-300">Total Reports Generated</span>
                    <span className="text-slate-50 font-medium">247</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-slate-300">Average Generation Time</span>
                    <span className="text-slate-50 font-medium">2.3 minutes</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-slate-300">Success Rate</span>
                    <span className="text-green-400 font-medium">98.7%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Report Types Distribution */}
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-slate-50 flex items-center">
                  <PieChart className="h-5 w-5 mr-2" />
                  Report Types Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <span className="text-slate-300">Vulnerability Reports</span>
                    </div>
                    <span className="text-slate-50 font-medium">45%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-slate-300">Application Reports</span>
                    </div>
                    <span className="text-slate-50 font-medium">30%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-slate-300">Compliance Reports</span>
                    </div>
                    <span className="text-slate-50 font-medium">20%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                      <span className="text-slate-300">Custom Reports</span>
                    </div>
                    <span className="text-slate-50 font-medium">5%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card className="bg-slate-800 border-slate-700 lg:col-span-2">
              <CardHeader>
                <CardTitle className="text-slate-50 flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Recent Report Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { action: 'Generated', report: 'Weekly Vulnerability Summary', user: '<EMAIL>', time: '2 hours ago' },
                    { action: 'Created', report: 'Custom Security Dashboard', user: '<EMAIL>', time: '1 day ago' },
                    { action: 'Scheduled', report: 'Monthly Compliance Report', user: '<EMAIL>', time: '2 days ago' },
                    { action: 'Modified', report: 'Application Risk Assessment', user: '<EMAIL>', time: '3 days ago' },
                  ].map((activity, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-slate-700 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={`w-2 h-2 rounded-full ${
                          activity.action === 'Generated' ? 'bg-green-500' :
                          activity.action === 'Created' ? 'bg-blue-500' :
                          activity.action === 'Scheduled' ? 'bg-yellow-500' :
                          'bg-purple-500'
                        }`}></div>
                        <div>
                          <p className="text-slate-50 font-medium">
                            {activity.action} "{activity.report}"
                          </p>
                          <p className="text-slate-400 text-sm">by {activity.user}</p>
                        </div>
                      </div>
                      <span className="text-slate-400 text-sm">{activity.time}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
