'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CVECard } from '@/components/ui/cve-card';
import { 
  Search, 
  Filter, 
  Clock,
  Shield,
  Package,
  Database,
  FileText,
  AlertTriangle,
  TrendingUp,
  Users
} from 'lucide-react';

interface SearchResult {
  type: 'cve' | 'application' | 'component';
  id: string;
  title: string;
  description: string;
  metadata: any;
  relevance: number;
}

interface SearchStats {
  total: number;
  cves: number;
  applications: number;
  components: number;
}

export default function SearchPage() {
  const searchParams = useSearchParams();
  const initialQuery = searchParams.get('q') || '';
  
  const [query, setQuery] = useState(initialQuery);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [stats, setStats] = useState<SearchStats>({ total: 0, cves: 0, applications: 0, components: 0 });
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [recentSearches, setRecentSearches] = useState<string[]>([
    'CVE-2024-0001',
    'React vulnerabilities',
    'PostgreSQL',
    'Customer Portal',
    'critical severity'
  ]);

  // Mock search results
  const mockResults: SearchResult[] = [
    {
      type: 'cve',
      id: 'CVE-2024-0001',
      title: 'CVE-2024-0001',
      description: 'Remote code execution vulnerability in popular web framework allowing attackers to execute arbitrary code',
      metadata: {
        severity: 'CRITICAL',
        cvss_score: 9.8,
        published_date: '2024-01-15T10:30:00Z',
        affected_products: ['React', 'Next.js', 'Express.js']
      },
      relevance: 95
    },
    {
      type: 'application',
      id: 'app-1',
      title: 'Customer Portal',
      description: 'Main customer-facing web application for account management and service access',
      metadata: {
        environment: 'production',
        criticality: 'critical',
        technologies: ['React', 'Node.js', 'PostgreSQL'],
        vulnerability_count: { critical: 2, high: 5, medium: 12, low: 8 }
      },
      relevance: 88
    },
    {
      type: 'component',
      id: 'comp-1',
      title: 'React v18.2.0',
      description: 'JavaScript library for building user interfaces',
      metadata: {
        version: '18.2.0',
        type: 'Frontend Framework',
        usage_count: 15,
        vulnerability_count: { critical: 1, high: 2, medium: 3, low: 1 }
      },
      relevance: 82
    },
    {
      type: 'cve',
      id: 'CVE-2024-0002',
      title: 'CVE-2024-0002',
      description: 'SQL injection vulnerability in database connector enabling unauthorized data access',
      metadata: {
        severity: 'HIGH',
        cvss_score: 8.1,
        published_date: '2024-01-14T15:45:00Z',
        affected_products: ['PostgreSQL', 'MySQL', 'MongoDB']
      },
      relevance: 75
    }
  ];

  useEffect(() => {
    if (initialQuery) {
      performSearch(initialQuery);
    }
  }, [initialQuery]);

  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) return;

    setLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // Filter mock results based on query
    const filteredResults = mockResults.filter(result => 
      result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      result.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (result.metadata.affected_products && 
       result.metadata.affected_products.some((product: string) => 
         product.toLowerCase().includes(searchQuery.toLowerCase())
       )) ||
      (result.metadata.technologies && 
       result.metadata.technologies.some((tech: string) => 
         tech.toLowerCase().includes(searchQuery.toLowerCase())
       ))
    );

    setResults(filteredResults);
    
    // Calculate stats
    const newStats = {
      total: filteredResults.length,
      cves: filteredResults.filter(r => r.type === 'cve').length,
      applications: filteredResults.filter(r => r.type === 'application').length,
      components: filteredResults.filter(r => r.type === 'component').length,
    };
    setStats(newStats);

    // Add to recent searches
    if (!recentSearches.includes(searchQuery)) {
      setRecentSearches([searchQuery, ...recentSearches.slice(0, 4)]);
    }

    setLoading(false);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    performSearch(query);
  };

  const getFilteredResults = () => {
    if (activeTab === 'all') return results;
    return results.filter(result => result.type === activeTab);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'cve': return <Shield className="h-4 w-4" />;
      case 'application': return <Package className="h-4 w-4" />;
      case 'component': return <Database className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'cve': return 'bg-red-100 text-red-800 border-red-200';
      case 'application': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'component': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-slate-100 text-slate-800 border-slate-200';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-600 text-white';
      case 'HIGH': return 'bg-orange-600 text-white';
      case 'MEDIUM': return 'bg-yellow-600 text-white';
      case 'LOW': return 'bg-green-600 text-white';
      default: return 'bg-slate-600 text-white';
    }
  };

  const filteredResults = getFilteredResults();

  return (
    <div className="space-y-6" data-testid="search-page">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight text-slate-50">Global Search</h1>
        <p className="text-slate-400 mt-1">
          Search across CVEs, applications, and components
        </p>
      </div>

      {/* Search Form */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-slate-400" />
              <Input
                type="search"
                placeholder="Search CVEs, applications, components..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="pl-12 text-lg h-12 bg-slate-700 border-slate-600 text-slate-50 placeholder:text-slate-400 focus:border-cyan-400 focus:ring-cyan-400"
              />
              <Button 
                type="submit" 
                className="absolute right-2 top-1/2 -translate-y-1/2 bg-cyan-600 hover:bg-cyan-700"
                disabled={loading}
              >
                {loading ? 'Searching...' : 'Search'}
              </Button>
            </div>
            
            {/* Recent Searches */}
            {recentSearches.length > 0 && !query && (
              <div>
                <h3 className="text-sm font-medium text-slate-300 mb-2">Recent Searches</h3>
                <div className="flex flex-wrap gap-2">
                  {recentSearches.map((search, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setQuery(search);
                        performSearch(search);
                      }}
                      className="border-slate-600 text-slate-300 hover:bg-slate-700"
                    >
                      <Clock className="h-3 w-3 mr-1" />
                      {search}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </form>
        </CardContent>
      </Card>

      {/* Search Results */}
      {(results.length > 0 || loading) && (
        <>
          {/* Stats */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-slate-400" />
                  <div>
                    <p className="text-sm font-medium text-slate-300">Total Results</p>
                    <p className="text-2xl font-bold text-slate-50">{stats.total}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Shield className="h-5 w-5 text-red-500" />
                  <div>
                    <p className="text-sm font-medium text-slate-300">CVEs</p>
                    <p className="text-2xl font-bold text-red-400">{stats.cves}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Package className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium text-slate-300">Applications</p>
                    <p className="text-2xl font-bold text-blue-400">{stats.applications}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Database className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm font-medium text-slate-300">Components</p>
                    <p className="text-2xl font-bold text-green-400">{stats.components}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Results Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-slate-800 border-slate-700">
              <TabsTrigger value="all" className="data-[state=active]:bg-slate-700">
                All ({stats.total})
              </TabsTrigger>
              <TabsTrigger value="cve" className="data-[state=active]:bg-slate-700">
                CVEs ({stats.cves})
              </TabsTrigger>
              <TabsTrigger value="application" className="data-[state=active]:bg-slate-700">
                Apps ({stats.applications})
              </TabsTrigger>
              <TabsTrigger value="component" className="data-[state=active]:bg-slate-700">
                Components ({stats.components})
              </TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-6">
              {loading ? (
                <div className="space-y-4">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <Card key={i} className="bg-slate-800 border-slate-700 animate-pulse">
                      <CardContent className="p-6">
                        <div className="space-y-3">
                          <div className="h-4 bg-slate-700 rounded w-3/4"></div>
                          <div className="h-3 bg-slate-700 rounded w-1/2"></div>
                          <div className="h-16 bg-slate-700 rounded"></div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : filteredResults.length > 0 ? (
                <div className="space-y-4">
                  {filteredResults.map((result) => (
                    <Card key={`${result.type}-${result.id}`} className="bg-slate-800 border-slate-700 hover:border-slate-600 transition-colors">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <Badge className={getTypeColor(result.type)}>
                                {getTypeIcon(result.type)}
                                <span className="ml-1 capitalize">{result.type}</span>
                              </Badge>
                              <span className="text-xs text-slate-400">
                                {result.relevance}% match
                              </span>
                            </div>

                            <h3 className="text-lg font-semibold text-slate-50 mb-2">
                              {result.title}
                            </h3>

                            <p className="text-slate-300 mb-4 line-clamp-2">
                              {result.description}
                            </p>

                            {/* Type-specific metadata */}
                            {result.type === 'cve' && (
                              <div className="flex items-center space-x-4 text-sm">
                                <Badge className={getSeverityColor(result.metadata.severity)}>
                                  {result.metadata.severity}
                                </Badge>
                                <span className="text-slate-400">
                                  CVSS: {result.metadata.cvss_score}
                                </span>
                                <span className="text-slate-400">
                                  {new Date(result.metadata.published_date).toLocaleDateString()}
                                </span>
                                {result.metadata.affected_products && (
                                  <div className="flex space-x-1">
                                    {result.metadata.affected_products.slice(0, 3).map((product: string, index: number) => (
                                      <Badge key={index} variant="secondary" className="text-xs bg-slate-700 text-slate-300">
                                        {product}
                                      </Badge>
                                    ))}
                                  </div>
                                )}
                              </div>
                            )}

                            {result.type === 'application' && (
                              <div className="flex items-center space-x-4 text-sm">
                                <Badge className={result.metadata.environment === 'production' ? 'bg-red-600 text-white' : 'bg-yellow-600 text-white'}>
                                  {result.metadata.environment}
                                </Badge>
                                <Badge className={getSeverityColor(result.metadata.criticality.toUpperCase())}>
                                  {result.metadata.criticality}
                                </Badge>
                                <div className="flex items-center space-x-2 text-slate-400">
                                  <AlertTriangle className="h-3 w-3 text-red-400" />
                                  <span>{result.metadata.vulnerability_count.critical + result.metadata.vulnerability_count.high} high-risk</span>
                                </div>
                                {result.metadata.technologies && (
                                  <div className="flex space-x-1">
                                    {result.metadata.technologies.slice(0, 3).map((tech: string, index: number) => (
                                      <Badge key={index} variant="secondary" className="text-xs bg-slate-700 text-slate-300">
                                        {tech}
                                      </Badge>
                                    ))}
                                  </div>
                                )}
                              </div>
                            )}

                            {result.type === 'component' && (
                              <div className="flex items-center space-x-4 text-sm">
                                <Badge variant="secondary" className="bg-slate-700 text-slate-300">
                                  v{result.metadata.version}
                                </Badge>
                                <span className="text-slate-400">
                                  {result.metadata.type}
                                </span>
                                <div className="flex items-center space-x-2 text-slate-400">
                                  <Users className="h-3 w-3" />
                                  <span>Used in {result.metadata.usage_count} apps</span>
                                </div>
                                <div className="flex items-center space-x-2 text-slate-400">
                                  <AlertTriangle className="h-3 w-3 text-orange-400" />
                                  <span>{result.metadata.vulnerability_count.critical + result.metadata.vulnerability_count.high} vulnerabilities</span>
                                </div>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                // Navigate to detail page based on type
                                const path = result.type === 'cve' ? `/cves/${result.id}` :
                                           result.type === 'application' ? `/applications/${result.id}` :
                                           `/components/${result.id}`;
                                window.location.href = path;
                              }}
                              className="border-slate-600 text-slate-300 hover:bg-slate-700"
                            >
                              View Details
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card className="bg-slate-800 border-slate-700">
                  <CardContent className="p-12 text-center">
                    <Search className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-slate-50 mb-2">No Results Found</h3>
                    <p className="text-slate-400 mb-4">
                      No {activeTab === 'all' ? 'results' : activeTab + 's'} found for "{query}". Try different keywords or check your spelling.
                    </p>
                    <div className="space-y-2">
                      <p className="text-sm text-slate-500">Search tips:</p>
                      <ul className="text-sm text-slate-400 space-y-1">
                        <li>• Use specific CVE IDs (e.g., CVE-2024-0001)</li>
                        <li>• Search by technology names (e.g., React, PostgreSQL)</li>
                        <li>• Try application or component names</li>
                        <li>• Use severity levels (critical, high, medium, low)</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </>
      )}

      {/* No search performed yet */}
      {results.length === 0 && !loading && query === '' && (
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-12 text-center">
            <Search className="h-16 w-16 text-slate-400 mx-auto mb-6" />
            <h3 className="text-xl font-medium text-slate-50 mb-4">Start Your Security Search</h3>
            <p className="text-slate-400 mb-6 max-w-md mx-auto">
              Search across all your security data including CVEs, applications, and components to quickly find what you need.
            </p>
            <div className="grid gap-4 md:grid-cols-3 max-w-2xl mx-auto">
              <div className="text-center">
                <Shield className="h-8 w-8 text-red-400 mx-auto mb-2" />
                <h4 className="font-medium text-slate-50 mb-1">CVE Database</h4>
                <p className="text-sm text-slate-400">Search vulnerability records and security advisories</p>
              </div>
              <div className="text-center">
                <Package className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                <h4 className="font-medium text-slate-50 mb-1">Applications</h4>
                <p className="text-sm text-slate-400">Find applications and their security status</p>
              </div>
              <div className="text-center">
                <Database className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <h4 className="font-medium text-slate-50 mb-1">Components</h4>
                <p className="text-sm text-slate-400">Search software components and dependencies</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
