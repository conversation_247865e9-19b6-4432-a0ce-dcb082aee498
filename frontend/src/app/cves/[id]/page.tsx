'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft,
  Shield,
  AlertTriangle,
  Calendar,
  ExternalLink,
  Eye,
  EyeOff,
  Share2,
  Download,
  Clock,
  Target,
  Zap,
  Database,
  Server,
  Globe,
  Users,
  FileText,
  TrendingUp,
  Activity
} from 'lucide-react';

interface CVEDetails {
  id: string;
  cve_id: string;
  description: string;
  severity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  cvss_score: number;
  cvss_vector: string;
  published_date: string;
  modified_date: string;
  references: string[];
  affected_products: string[];
  cwe_id: string;
  cwe_description: string;
  exploitability_score: number;
  impact_score: number;
  attack_vector: string;
  attack_complexity: string;
  privileges_required: string;
  user_interaction: string;
  scope: string;
  confidentiality_impact: string;
  integrity_impact: string;
  availability_impact: string;
  vendor_advisories: string[];
  patches_available: boolean;
  exploit_available: boolean;
  affected_applications: Array<{
    id: string;
    name: string;
    environment: string;
    criticality: string;
    version: string;
  }>;
}

export default function CVEDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const cveId = params.id as string;
  
  const [cve, setCVE] = useState<CVEDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [isWatched, setIsWatched] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Mock CVE details data
  const mockCVEDetails: CVEDetails = {
    id: '1',
    cve_id: 'CVE-2024-0001',
    description: 'A critical remote code execution vulnerability exists in the popular web framework that allows attackers to execute arbitrary code through malicious HTTP requests. This vulnerability affects the request parsing mechanism and can be exploited without authentication. The flaw occurs when the framework processes specially crafted HTTP headers, leading to buffer overflow conditions that can be leveraged for code execution.',
    severity: 'CRITICAL',
    cvss_score: 9.8,
    cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',
    published_date: '2024-01-15T10:30:00Z',
    modified_date: '2024-01-16T14:20:00Z',
    references: [
      'https://nvd.nist.gov/vuln/detail/CVE-2024-0001',
      'https://github.com/advisories/GHSA-xxxx-yyyy-zzzz',
      'https://security.example.com/advisory/2024-001',
      'https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2024-0001'
    ],
    affected_products: ['React 18.x', 'Next.js 13.x-14.x', 'Express.js 4.x'],
    cwe_id: 'CWE-78',
    cwe_description: 'Improper Neutralization of Special Elements used in an OS Command (OS Command Injection)',
    exploitability_score: 3.9,
    impact_score: 5.9,
    attack_vector: 'Network',
    attack_complexity: 'Low',
    privileges_required: 'None',
    user_interaction: 'None',
    scope: 'Unchanged',
    confidentiality_impact: 'High',
    integrity_impact: 'High',
    availability_impact: 'High',
    vendor_advisories: [
      'https://react.dev/security/2024-001',
      'https://nextjs.org/security/2024-001'
    ],
    patches_available: true,
    exploit_available: true,
    affected_applications: [
      {
        id: '1',
        name: 'Customer Portal',
        environment: 'production',
        criticality: 'critical',
        version: '2.1.4'
      },
      {
        id: '2',
        name: 'Analytics Dashboard',
        environment: 'staging',
        criticality: 'medium',
        version: '3.0.1'
      }
    ]
  };

  useEffect(() => {
    const loadCVEDetails = async () => {
      setLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCVE(mockCVEDetails);
      setLoading(false);
    };

    loadCVEDetails();
  }, [cveId]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-600 text-white';
      case 'HIGH': return 'bg-orange-600 text-white';
      case 'MEDIUM': return 'bg-yellow-600 text-white';
      case 'LOW': return 'bg-green-600 text-white';
      default: return 'bg-slate-600 text-white';
    }
  };

  const getCVSSRating = (score: number) => {
    if (score >= 9.0) return { rating: 'Critical', color: 'text-red-500' };
    if (score >= 7.0) return { rating: 'High', color: 'text-orange-500' };
    if (score >= 4.0) return { rating: 'Medium', color: 'text-yellow-500' };
    return { rating: 'Low', color: 'text-green-500' };
  };

  const getImpactColor = (impact: string) => {
    switch (impact.toLowerCase()) {
      case 'high': return 'text-red-500';
      case 'low': return 'text-yellow-500';
      case 'none': return 'text-green-500';
      default: return 'text-slate-400';
    }
  };

  const handleWatchToggle = () => {
    setIsWatched(!isWatched);
    // TODO: Implement API call to update watch status
  };

  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href);
    // TODO: Show toast notification
  };

  if (loading) {
    return (
      <div className="space-y-6" data-testid="cve-details-loading">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-700 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-slate-700 rounded w-2/3 mb-6"></div>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="bg-slate-800 border-slate-700">
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <div className="h-4 bg-slate-700 rounded w-3/4"></div>
                    <div className="h-8 bg-slate-700 rounded"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!cve) {
    return (
      <div className="space-y-6" data-testid="cve-not-found">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-12 text-center">
            <AlertTriangle className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-50 mb-2">CVE Not Found</h3>
            <p className="text-slate-400 mb-4">
              The requested CVE could not be found or may have been removed.
            </p>
            <Button onClick={() => router.back()} className="bg-cyan-600 hover:bg-cyan-700">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const cvssRating = getCVSSRating(cve.cvss_score);

  return (
    <div className="space-y-6" data-testid="cve-details-page">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-slate-50">{cve.cve_id}</h1>
            <p className="text-slate-400 mt-1">
              Published {new Date(cve.published_date).toLocaleDateString()}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleWatchToggle}
            className={`border-slate-600 hover:bg-slate-700 ${
              isWatched ? 'text-cyan-400 border-cyan-400' : 'text-slate-300'
            }`}
          >
            {isWatched ? <Eye className="h-4 w-4 mr-2" /> : <EyeOff className="h-4 w-4 mr-2" />}
            {isWatched ? 'Watching' : 'Watch'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleShare}
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-300">Severity</p>
                <Badge className={getSeverityColor(cve.severity)}>
                  {cve.severity}
                </Badge>
              </div>
              <Shield className="h-8 w-8 text-slate-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-300">CVSS Score</p>
                <p className={`text-2xl font-bold ${cvssRating.color}`}>
                  {cve.cvss_score}
                </p>
                <p className="text-xs text-slate-400">{cvssRating.rating}</p>
              </div>
              <Target className="h-8 w-8 text-slate-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-300">Exploitability</p>
                <p className="text-2xl font-bold text-orange-400">{cve.exploitability_score}</p>
                <p className="text-xs text-slate-400">Score</p>
              </div>
              <Zap className="h-8 w-8 text-slate-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-300">Impact</p>
                <p className="text-2xl font-bold text-red-400">{cve.impact_score}</p>
                <p className="text-xs text-slate-400">Score</p>
              </div>
              <Activity className="h-8 w-8 text-slate-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Information Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5 bg-slate-800 border-slate-700">
          <TabsTrigger value="overview" className="data-[state=active]:bg-slate-700">
            Overview
          </TabsTrigger>
          <TabsTrigger value="technical" className="data-[state=active]:bg-slate-700">
            Technical
          </TabsTrigger>
          <TabsTrigger value="affected" className="data-[state=active]:bg-slate-700">
            Affected Systems
          </TabsTrigger>
          <TabsTrigger value="references" className="data-[state=active]:bg-slate-700">
            References
          </TabsTrigger>
          <TabsTrigger value="timeline" className="data-[state=active]:bg-slate-700">
            Timeline
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="mt-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-slate-50 flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  Description
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-300 leading-relaxed">{cve.description}</p>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-slate-50 flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2" />
                  Risk Assessment
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-slate-300">Patches Available</span>
                  <Badge className={cve.patches_available ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}>
                    {cve.patches_available ? 'Yes' : 'No'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-300">Exploit Available</span>
                  <Badge className={cve.exploit_available ? 'bg-red-600 text-white' : 'bg-green-600 text-white'}>
                    {cve.exploit_available ? 'Yes' : 'No'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-300">CWE Classification</span>
                  <Badge variant="secondary" className="bg-slate-700 text-slate-300">
                    {cve.cwe_id}
                  </Badge>
                </div>
                <div className="pt-2 border-t border-slate-700">
                  <p className="text-sm text-slate-400">{cve.cwe_description}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Technical Tab */}
        <TabsContent value="technical" className="mt-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-slate-50 flex items-center">
                  <Target className="h-5 w-5 mr-2" />
                  CVSS v3.1 Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-slate-300">Attack Vector</p>
                    <p className="text-slate-400">{cve.attack_vector}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-slate-300">Attack Complexity</p>
                    <p className="text-slate-400">{cve.attack_complexity}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-slate-300">Privileges Required</p>
                    <p className="text-slate-400">{cve.privileges_required}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-slate-300">User Interaction</p>
                    <p className="text-slate-400">{cve.user_interaction}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-slate-300">Scope</p>
                    <p className="text-slate-400">{cve.scope}</p>
                  </div>
                </div>
                <div className="pt-4 border-t border-slate-700">
                  <p className="text-sm font-medium text-slate-300 mb-2">CVSS Vector</p>
                  <code className="text-xs bg-slate-700 text-slate-300 p-2 rounded block">
                    {cve.cvss_vector}
                  </code>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-slate-50 flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  Impact Assessment
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-slate-300">Confidentiality</span>
                    <span className={`font-medium ${getImpactColor(cve.confidentiality_impact)}`}>
                      {cve.confidentiality_impact}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-slate-300">Integrity</span>
                    <span className={`font-medium ${getImpactColor(cve.integrity_impact)}`}>
                      {cve.integrity_impact}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-slate-300">Availability</span>
                    <span className={`font-medium ${getImpactColor(cve.availability_impact)}`}>
                      {cve.availability_impact}
                    </span>
                  </div>
                </div>
                <div className="pt-4 border-t border-slate-700">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-slate-300">Exploitability Score</p>
                      <p className="text-xl font-bold text-orange-400">{cve.exploitability_score}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-slate-300">Impact Score</p>
                      <p className="text-xl font-bold text-red-400">{cve.impact_score}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Affected Systems Tab */}
        <TabsContent value="affected" className="mt-6">
          <div className="space-y-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-slate-50 flex items-center">
                  <Database className="h-5 w-5 mr-2" />
                  Affected Products
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {cve.affected_products.map((product, index) => (
                    <Badge key={index} variant="secondary" className="bg-slate-700 text-slate-300">
                      {product}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-slate-50 flex items-center">
                  <Server className="h-5 w-5 mr-2" />
                  Affected Applications
                </CardTitle>
              </CardHeader>
              <CardContent>
                {cve.affected_applications.length > 0 ? (
                  <div className="space-y-4">
                    {cve.affected_applications.map((app) => (
                      <div key={app.id} className="flex items-center justify-between p-4 bg-slate-700 rounded-lg">
                        <div className="flex-1">
                          <h4 className="font-medium text-slate-50">{app.name}</h4>
                          <p className="text-sm text-slate-400">Version {app.version}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={app.environment === 'production' ? 'bg-red-600 text-white' : 'bg-yellow-600 text-white'}>
                            {app.environment}
                          </Badge>
                          <Badge className={
                            app.criticality === 'critical' ? 'bg-red-600 text-white' :
                            app.criticality === 'high' ? 'bg-orange-600 text-white' :
                            app.criticality === 'medium' ? 'bg-yellow-600 text-white' :
                            'bg-green-600 text-white'
                          }>
                            {app.criticality}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-slate-400 text-center py-8">
                    No applications in your portfolio are affected by this CVE.
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* References Tab */}
        <TabsContent value="references" className="mt-6">
          <div className="grid gap-6 lg:grid-cols-2">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-slate-50 flex items-center">
                  <ExternalLink className="h-5 w-5 mr-2" />
                  Official References
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {cve.references.map((ref, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <ExternalLink className="h-4 w-4 text-slate-400 flex-shrink-0" />
                      <a
                        href={ref}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-cyan-400 hover:text-cyan-300 text-sm break-all"
                      >
                        {ref}
                      </a>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-slate-50 flex items-center">
                  <Shield className="h-5 w-5 mr-2" />
                  Vendor Advisories
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {cve.vendor_advisories.map((advisory, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <Shield className="h-4 w-4 text-slate-400 flex-shrink-0" />
                      <a
                        href={advisory}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-cyan-400 hover:text-cyan-300 text-sm break-all"
                      >
                        {advisory}
                      </a>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Timeline Tab */}
        <TabsContent value="timeline" className="mt-6">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-slate-50 flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                CVE Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <p className="font-medium text-slate-50">CVE Published</p>
                    <p className="text-sm text-slate-400">
                      {new Date(cve.published_date).toLocaleDateString()} at {new Date(cve.published_date).toLocaleTimeString()}
                    </p>
                    <p className="text-sm text-slate-300 mt-1">
                      Initial vulnerability disclosure and CVE assignment
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                  <div>
                    <p className="font-medium text-slate-50">CVE Modified</p>
                    <p className="text-sm text-slate-400">
                      {new Date(cve.modified_date).toLocaleDateString()} at {new Date(cve.modified_date).toLocaleTimeString()}
                    </p>
                    <p className="text-sm text-slate-300 mt-1">
                      Updated vulnerability information and additional details
                    </p>
                  </div>
                </div>

                {cve.patches_available && (
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <p className="font-medium text-slate-50">Patches Available</p>
                      <p className="text-sm text-slate-400">
                        Security patches released by vendors
                      </p>
                    </div>
                  </div>
                )}

                {cve.exploit_available && (
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                    <div>
                      <p className="font-medium text-slate-50">Exploit Available</p>
                      <p className="text-sm text-slate-400">
                        Public exploits are available for this vulnerability
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
