'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CVECard } from '@/components/ui/cve-card';
import { 
  Search, 
  Filter, 
  SortAsc, 
  SortDesc, 
  Download, 
  RefreshCw,
  AlertTriangle,
  Shield,
  Eye,
  Calendar,
  TrendingUp
} from 'lucide-react';

interface CVE {
  id: string;
  cve_id: string;
  description: string;
  severity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  cvss_score: number;
  published_date: string;
  modified_date: string;
  references: string[];
  affected_products?: string[];
  cwe_id?: string;
  exploitability_score?: number;
  impact_score?: number;
}

interface CVEFilters {
  search: string;
  severity: string[];
  dateRange: string;
  cvssMin: number;
  cvssMax: number;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export default function CVEsPage() {
  const [cves, setCVEs] = useState<CVE[]>([]);
  const [filteredCVEs, setFilteredCVEs] = useState<CVE[]>([]);
  const [loading, setLoading] = useState(true);
  const [watchedCVEs, setWatchedCVEs] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 12;

  const [filters, setFilters] = useState<CVEFilters>({
    search: '',
    severity: [],
    dateRange: 'all',
    cvssMin: 0,
    cvssMax: 10,
    sortBy: 'published_date',
    sortOrder: 'desc',
  });

  // Mock CVE data - replace with actual API call
  const mockCVEs: CVE[] = [
    {
      id: '1',
      cve_id: 'CVE-2024-0001',
      description: 'Remote code execution vulnerability in popular web framework allowing attackers to execute arbitrary code through malicious HTTP requests',
      severity: 'CRITICAL',
      cvss_score: 9.8,
      published_date: '2024-01-15T10:30:00Z',
      modified_date: '2024-01-16T14:20:00Z',
      references: ['https://nvd.nist.gov/vuln/detail/CVE-2024-0001'],
      affected_products: ['React', 'Next.js', 'Express.js'],
      cwe_id: '78',
      exploitability_score: 3.9,
      impact_score: 5.9,
    },
    {
      id: '2',
      cve_id: 'CVE-2024-0002',
      description: 'SQL injection vulnerability in database connector enabling unauthorized data access and potential data exfiltration',
      severity: 'HIGH',
      cvss_score: 8.1,
      published_date: '2024-01-14T15:45:00Z',
      modified_date: '2024-01-15T09:30:00Z',
      references: ['https://nvd.nist.gov/vuln/detail/CVE-2024-0002'],
      affected_products: ['PostgreSQL', 'MySQL', 'MongoDB'],
      cwe_id: '89',
      exploitability_score: 3.1,
      impact_score: 5.0,
    },
    {
      id: '3',
      cve_id: 'CVE-2024-0003',
      description: 'Authentication bypass vulnerability in OAuth implementation allowing unauthorized access to protected resources',
      severity: 'HIGH',
      cvss_score: 7.5,
      published_date: '2024-01-13T09:20:00Z',
      modified_date: '2024-01-14T11:15:00Z',
      references: ['https://nvd.nist.gov/vuln/detail/CVE-2024-0003'],
      affected_products: ['OAuth2', 'JWT', 'Auth0'],
      cwe_id: '287',
      exploitability_score: 2.8,
      impact_score: 4.7,
    },
    {
      id: '4',
      cve_id: 'CVE-2024-0004',
      description: 'Cross-site scripting vulnerability in UI component library enabling malicious script execution in user browsers',
      severity: 'MEDIUM',
      cvss_score: 6.1,
      published_date: '2024-01-12T14:10:00Z',
      modified_date: '2024-01-13T16:45:00Z',
      references: ['https://nvd.nist.gov/vuln/detail/CVE-2024-0004'],
      affected_products: ['React', 'Vue.js', 'Angular'],
      cwe_id: '79',
      exploitability_score: 2.2,
      impact_score: 3.9,
    },
    {
      id: '5',
      cve_id: 'CVE-2024-0005',
      description: 'Path traversal vulnerability in file upload functionality allowing access to sensitive system files',
      severity: 'MEDIUM',
      cvss_score: 5.4,
      published_date: '2024-01-11T11:30:00Z',
      modified_date: '2024-01-12T08:20:00Z',
      references: ['https://nvd.nist.gov/vuln/detail/CVE-2024-0005'],
      affected_products: ['Apache', 'Nginx', 'IIS'],
      cwe_id: '22',
      exploitability_score: 1.8,
      impact_score: 3.6,
    },
    {
      id: '6',
      cve_id: 'CVE-2024-0006',
      description: 'Information disclosure vulnerability in API endpoint exposing sensitive user data without proper authorization',
      severity: 'LOW',
      cvss_score: 3.7,
      published_date: '2024-01-10T16:15:00Z',
      modified_date: '2024-01-11T10:30:00Z',
      references: ['https://nvd.nist.gov/vuln/detail/CVE-2024-0006'],
      affected_products: ['REST API', 'GraphQL', 'FastAPI'],
      cwe_id: '200',
      exploitability_score: 1.2,
      impact_score: 2.5,
    },
  ];

  useEffect(() => {
    // Simulate API call
    const loadCVEs = async () => {
      setLoading(true);
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCVEs(mockCVEs);
      setFilteredCVEs(mockCVEs);
      setTotalPages(Math.ceil(mockCVEs.length / itemsPerPage));
      setLoading(false);
    };

    loadCVEs();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [filters, cves]);

  const applyFilters = () => {
    let filtered = [...cves];

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(cve => 
        cve.cve_id.toLowerCase().includes(searchLower) ||
        cve.description.toLowerCase().includes(searchLower) ||
        cve.affected_products?.some(product => 
          product.toLowerCase().includes(searchLower)
        )
      );
    }

    // Severity filter
    if (filters.severity.length > 0) {
      filtered = filtered.filter(cve => filters.severity.includes(cve.severity));
    }

    // CVSS score filter
    filtered = filtered.filter(cve => 
      cve.cvss_score >= filters.cvssMin && cve.cvss_score <= filters.cvssMax
    );

    // Date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      const days = parseInt(filters.dateRange);
      const cutoffDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
      
      filtered = filtered.filter(cve => 
        new Date(cve.published_date) >= cutoffDate
      );
    }

    // Sort
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (filters.sortBy) {
        case 'cvss_score':
          aValue = a.cvss_score;
          bValue = b.cvss_score;
          break;
        case 'published_date':
          aValue = new Date(a.published_date);
          bValue = new Date(b.published_date);
          break;
        case 'severity':
          const severityOrder = { 'CRITICAL': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
          aValue = severityOrder[a.severity];
          bValue = severityOrder[b.severity];
          break;
        default:
          aValue = a.cve_id;
          bValue = b.cve_id;
      }

      if (filters.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredCVEs(filtered);
    setTotalPages(Math.ceil(filtered.length / itemsPerPage));
    setCurrentPage(1);
  };

  const handleWatchToggle = (cve: CVE) => {
    const newWatched = new Set(watchedCVEs);
    if (newWatched.has(cve.cve_id)) {
      newWatched.delete(cve.cve_id);
    } else {
      newWatched.add(cve.cve_id);
    }
    setWatchedCVEs(newWatched);
  };

  const handleSeverityFilter = (severity: string) => {
    const newSeverities = filters.severity.includes(severity)
      ? filters.severity.filter(s => s !== severity)
      : [...filters.severity, severity];
    
    setFilters({ ...filters, severity: newSeverities });
  };

  const getSeverityStats = () => {
    const stats = {
      CRITICAL: filteredCVEs.filter(cve => cve.severity === 'CRITICAL').length,
      HIGH: filteredCVEs.filter(cve => cve.severity === 'HIGH').length,
      MEDIUM: filteredCVEs.filter(cve => cve.severity === 'MEDIUM').length,
      LOW: filteredCVEs.filter(cve => cve.severity === 'LOW').length,
    };
    return stats;
  };

  const paginatedCVEs = filteredCVEs.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const severityStats = getSeverityStats();

  return (
    <div className="space-y-6" data-testid="cves-page">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-slate-50">CVE Feed</h1>
          <p className="text-slate-400 mt-1">
            Monitor and manage Common Vulnerabilities and Exposures
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-700">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => window.location.reload()}
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-sm font-medium text-slate-300">Critical</p>
                <p className="text-2xl font-bold text-red-400">{severityStats.CRITICAL}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-slate-300">High</p>
                <p className="text-2xl font-bold text-orange-400">{severityStats.HIGH}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-yellow-500" />
              <div>
                <p className="text-sm font-medium text-slate-300">Medium</p>
                <p className="text-2xl font-bold text-yellow-400">{severityStats.MEDIUM}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Eye className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-slate-300">Low</p>
                <p className="text-2xl font-bold text-green-400">{severityStats.LOW}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-slate-50 flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {/* Search */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-slate-300">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-slate-400" />
                <Input
                  placeholder="Search CVEs, descriptions..."
                  value={filters.search}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                  className="pl-10 bg-slate-700 border-slate-600 text-slate-50"
                />
              </div>
            </div>

            {/* Severity Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-slate-300">Severity</label>
              <div className="flex flex-wrap gap-2">
                {['CRITICAL', 'HIGH', 'MEDIUM', 'LOW'].map((severity) => (
                  <Button
                    key={severity}
                    variant={filters.severity.includes(severity) ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleSeverityFilter(severity)}
                    className={`text-xs ${
                      filters.severity.includes(severity)
                        ? severity === 'CRITICAL' ? 'bg-red-600 hover:bg-red-700' :
                          severity === 'HIGH' ? 'bg-orange-600 hover:bg-orange-700' :
                          severity === 'MEDIUM' ? 'bg-yellow-600 hover:bg-yellow-700' :
                          'bg-green-600 hover:bg-green-700'
                        : 'border-slate-600 text-slate-300 hover:bg-slate-700'
                    }`}
                  >
                    {severity}
                  </Button>
                ))}
              </div>
            </div>

            {/* Date Range */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-slate-300">Date Range</label>
              <Select value={filters.dateRange} onValueChange={(value) => setFilters({ ...filters, dateRange: value })}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-slate-50">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600">
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="1">Last 24 Hours</SelectItem>
                  <SelectItem value="7">Last 7 Days</SelectItem>
                  <SelectItem value="30">Last 30 Days</SelectItem>
                  <SelectItem value="90">Last 90 Days</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Sort */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-slate-300">Sort By</label>
              <div className="flex space-x-2">
                <Select value={filters.sortBy} onValueChange={(value) => setFilters({ ...filters, sortBy: value })}>
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-slate-50">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700 border-slate-600">
                    <SelectItem value="published_date">Published Date</SelectItem>
                    <SelectItem value="cvss_score">CVSS Score</SelectItem>
                    <SelectItem value="severity">Severity</SelectItem>
                    <SelectItem value="cve_id">CVE ID</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFilters({ ...filters, sortOrder: filters.sortOrder === 'asc' ? 'desc' : 'asc' })}
                  className="border-slate-600 text-slate-300 hover:bg-slate-700"
                >
                  {filters.sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-slate-400">
          Showing {paginatedCVEs.length} of {filteredCVEs.length} CVEs
        </p>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-slate-400">Watched: {watchedCVEs.size}</span>
        </div>
      </div>

      {/* CVE Grid */}
      {loading ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="bg-slate-800 border-slate-700 animate-pulse">
              <CardContent className="p-6">
                <div className="space-y-3">
                  <div className="h-4 bg-slate-700 rounded w-3/4"></div>
                  <div className="h-3 bg-slate-700 rounded w-1/2"></div>
                  <div className="h-20 bg-slate-700 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : paginatedCVEs.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {paginatedCVEs.map((cve) => (
            <CVECard
              key={cve.id}
              cve={cve}
              isWatched={watchedCVEs.has(cve.cve_id)}
              onAddToWatchlist={() => handleWatchToggle(cve)}
              onViewDetails={(cve) => {
                // Navigate to CVE details page
                window.location.href = `/cves/${cve.cve_id}`;
              }}
            />
          ))}
        </div>
      ) : (
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-12 text-center">
            <AlertTriangle className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-50 mb-2">No CVEs Found</h3>
            <p className="text-slate-400 mb-4">
              No vulnerabilities match your current filters. Try adjusting your search criteria.
            </p>
            <Button
              onClick={() => setFilters({
                search: '',
                severity: [],
                dateRange: 'all',
                cvssMin: 0,
                cvssMax: 10,
                sortBy: 'published_date',
                sortOrder: 'desc',
              })}
              className="bg-cyan-600 hover:bg-cyan-700"
            >
              Clear Filters
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            Previous
          </Button>

          <div className="flex items-center space-x-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = i + 1;
              return (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPage(page)}
                  className={
                    currentPage === page
                      ? "bg-cyan-600 hover:bg-cyan-700"
                      : "border-slate-600 text-slate-300 hover:bg-slate-700"
                  }
                >
                  {page}
                </Button>
              );
            })}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}
