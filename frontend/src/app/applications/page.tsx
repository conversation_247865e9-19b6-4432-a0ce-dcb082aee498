'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ApplicationForm } from '@/components/forms/application-form';
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye,
  Package,
  Server,
  Globe,
  Users,
  AlertTriangle,
  CheckCircle,
  Clock,
  Settings
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface Application {
  id: string;
  name: string;
  description: string;
  version: string;
  environment: 'development' | 'staging' | 'production';
  criticality: 'low' | 'medium' | 'high' | 'critical';
  owner_email: string;
  repository_url?: string;
  deployment_url?: string;
  technologies: string[];
  compliance_requirements?: string[];
  business_unit: string;
  contact_person: string;
  created_at: string;
  updated_at: string;
  status: 'active' | 'inactive' | 'deprecated';
  vulnerability_count: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
}

interface ApplicationFilters {
  search: string;
  environment: string;
  criticality: string;
  status: string;
  businessUnit: string;
}

export default function ApplicationsPage() {
  const [applications, setApplications] = useState<Application[]>([]);
  const [filteredApplications, setFilteredApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingApplication, setEditingApplication] = useState<Application | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 12;

  const [filters, setFilters] = useState<ApplicationFilters>({
    search: '',
    environment: 'all',
    criticality: 'all',
    status: 'all',
    businessUnit: 'all',
  });

  // Mock application data
  const mockApplications: Application[] = [
    {
      id: '1',
      name: 'Customer Portal',
      description: 'Main customer-facing web application for account management and service access',
      version: '2.1.4',
      environment: 'production',
      criticality: 'critical',
      owner_email: '<EMAIL>',
      repository_url: 'https://github.com/company/customer-portal',
      deployment_url: 'https://portal.company.com',
      technologies: ['React', 'Node.js', 'PostgreSQL', 'Redis'],
      compliance_requirements: ['SOX', 'PCI-DSS'],
      business_unit: 'Customer Experience',
      contact_person: 'John Doe',
      created_at: '2023-01-15T10:30:00Z',
      updated_at: '2024-01-10T14:20:00Z',
      status: 'active',
      vulnerability_count: { critical: 2, high: 5, medium: 12, low: 8 },
    },
    {
      id: '2',
      name: 'Internal API Gateway',
      description: 'Centralized API gateway for microservices communication and external integrations',
      version: '1.8.2',
      environment: 'production',
      criticality: 'high',
      owner_email: '<EMAIL>',
      repository_url: 'https://github.com/company/api-gateway',
      deployment_url: 'https://api-internal.company.com',
      technologies: ['Go', 'Docker', 'Kubernetes', 'MongoDB'],
      compliance_requirements: ['SOC2'],
      business_unit: 'Engineering',
      contact_person: 'Jane Smith',
      created_at: '2023-03-20T09:15:00Z',
      updated_at: '2024-01-08T11:30:00Z',
      status: 'active',
      vulnerability_count: { critical: 0, high: 3, medium: 7, low: 15 },
    },
    {
      id: '3',
      name: 'Analytics Dashboard',
      description: 'Business intelligence dashboard for real-time analytics and reporting',
      version: '3.0.1',
      environment: 'staging',
      criticality: 'medium',
      owner_email: '<EMAIL>',
      repository_url: 'https://github.com/company/analytics-dashboard',
      deployment_url: 'https://analytics-staging.company.com',
      technologies: ['Vue.js', 'Python', 'ClickHouse', 'Grafana'],
      compliance_requirements: ['GDPR'],
      business_unit: 'Data & Analytics',
      contact_person: 'Mike Wilson',
      created_at: '2023-06-10T16:45:00Z',
      updated_at: '2024-01-05T09:10:00Z',
      status: 'active',
      vulnerability_count: { critical: 1, high: 2, medium: 8, low: 12 },
    },
    {
      id: '4',
      name: 'Legacy CRM System',
      description: 'Customer relationship management system scheduled for migration',
      version: '1.2.8',
      environment: 'production',
      criticality: 'low',
      owner_email: '<EMAIL>',
      repository_url: 'https://github.com/company/legacy-crm',
      technologies: ['PHP', 'MySQL', 'jQuery'],
      business_unit: 'Sales',
      contact_person: 'Sarah Johnson',
      created_at: '2021-08-15T12:00:00Z',
      updated_at: '2023-12-20T15:30:00Z',
      status: 'deprecated',
      vulnerability_count: { critical: 5, high: 12, medium: 20, low: 35 },
    },
  ];

  useEffect(() => {
    const loadApplications = async () => {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 800));
      setApplications(mockApplications);
      setFilteredApplications(mockApplications);
      setTotalPages(Math.ceil(mockApplications.length / itemsPerPage));
      setLoading(false);
    };

    loadApplications();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [filters, applications]);

  const applyFilters = () => {
    let filtered = [...applications];

    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(app => 
        app.name.toLowerCase().includes(searchLower) ||
        app.description.toLowerCase().includes(searchLower) ||
        app.business_unit.toLowerCase().includes(searchLower) ||
        app.technologies.some(tech => tech.toLowerCase().includes(searchLower))
      );
    }

    if (filters.environment !== 'all') {
      filtered = filtered.filter(app => app.environment === filters.environment);
    }

    if (filters.criticality !== 'all') {
      filtered = filtered.filter(app => app.criticality === filters.criticality);
    }

    if (filters.status !== 'all') {
      filtered = filtered.filter(app => app.status === filters.status);
    }

    if (filters.businessUnit !== 'all') {
      filtered = filtered.filter(app => app.business_unit === filters.businessUnit);
    }

    setFilteredApplications(filtered);
    setTotalPages(Math.ceil(filtered.length / itemsPerPage));
    setCurrentPage(1);
  };

  const getCriticalityColor = (criticality: string) => {
    switch (criticality) {
      case 'critical': return 'bg-red-600 text-white';
      case 'high': return 'bg-orange-600 text-white';
      case 'medium': return 'bg-yellow-600 text-white';
      case 'low': return 'bg-green-600 text-white';
      default: return 'bg-slate-600 text-white';
    }
  };

  const getEnvironmentColor = (environment: string) => {
    switch (environment) {
      case 'production': return 'bg-red-100 text-red-800 border-red-200';
      case 'staging': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'development': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-slate-100 text-slate-800 border-slate-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'deprecated': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-slate-100 text-slate-800 border-slate-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4" />;
      case 'inactive': return <Clock className="h-4 w-4" />;
      case 'deprecated': return <AlertTriangle className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const handleCreateApplication = async (data: any) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newApp: Application = {
      id: Date.now().toString(),
      ...data,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      status: 'active' as const,
      vulnerability_count: { critical: 0, high: 0, medium: 0, low: 0 },
    };

    setApplications([newApp, ...applications]);
    setShowCreateForm(false);
  };

  const handleEditApplication = async (data: any) => {
    if (!editingApplication) return;

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const updatedApp: Application = {
      ...editingApplication,
      ...data,
      updated_at: new Date().toISOString(),
    };

    setApplications(applications.map(app => 
      app.id === editingApplication.id ? updatedApp : app
    ));
    setEditingApplication(null);
  };

  const handleDeleteApplication = async (id: string) => {
    if (confirm('Are you sure you want to delete this application?')) {
      setApplications(applications.filter(app => app.id !== id));
    }
  };

  const paginatedApplications = filteredApplications.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const businessUnits = [...new Set(applications.map(app => app.business_unit))];

  return (
    <div className="space-y-6" data-testid="applications-page">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-slate-50">Applications</h1>
          <p className="text-slate-400 mt-1">
            Manage and monitor your application portfolio
          </p>
        </div>
        <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
          <DialogTrigger asChild>
            <Button className="bg-cyan-600 hover:bg-cyan-700">
              <Plus className="h-4 w-4 mr-2" />
              Add Application
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Application</DialogTitle>
              <DialogDescription>
                Add a new application to your vulnerability management system
              </DialogDescription>
            </DialogHeader>
            <ApplicationForm
              onSubmit={handleCreateApplication}
              onCancel={() => setShowCreateForm(false)}
              mode="create"
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Package className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-slate-300">Total Apps</p>
                <p className="text-2xl font-bold text-slate-50">{applications.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Server className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-sm font-medium text-slate-300">Production</p>
                <p className="text-2xl font-bold text-red-400">
                  {applications.filter(app => app.environment === 'production').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-slate-300">Critical Risk</p>
                <p className="text-2xl font-bold text-orange-400">
                  {applications.filter(app => app.criticality === 'critical').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-slate-300">Active</p>
                <p className="text-2xl font-bold text-green-400">
                  {applications.filter(app => app.status === 'active').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-slate-50 flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            {/* Search */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-slate-300">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-slate-400" />
                <Input
                  placeholder="Search applications..."
                  value={filters.search}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                  className="pl-10 bg-slate-700 border-slate-600 text-slate-50"
                />
              </div>
            </div>

            {/* Environment Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-slate-300">Environment</label>
              <Select value={filters.environment} onValueChange={(value) => setFilters({ ...filters, environment: value })}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-slate-50">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600">
                  <SelectItem value="all">All Environments</SelectItem>
                  <SelectItem value="production">Production</SelectItem>
                  <SelectItem value="staging">Staging</SelectItem>
                  <SelectItem value="development">Development</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Criticality Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-slate-300">Criticality</label>
              <Select value={filters.criticality} onValueChange={(value) => setFilters({ ...filters, criticality: value })}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-slate-50">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600">
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-slate-300">Status</label>
              <Select value={filters.status} onValueChange={(value) => setFilters({ ...filters, status: value })}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-slate-50">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600">
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="deprecated">Deprecated</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Business Unit Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-slate-300">Business Unit</label>
              <Select value={filters.businessUnit} onValueChange={(value) => setFilters({ ...filters, businessUnit: value })}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-slate-50">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600">
                  <SelectItem value="all">All Units</SelectItem>
                  {businessUnits.map((unit) => (
                    <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-slate-400">
          Showing {paginatedApplications.length} of {filteredApplications.length} applications
        </p>
      </div>

      {/* Applications Grid */}
      {loading ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="bg-slate-800 border-slate-700 animate-pulse">
              <CardContent className="p-6">
                <div className="space-y-3">
                  <div className="h-4 bg-slate-700 rounded w-3/4"></div>
                  <div className="h-3 bg-slate-700 rounded w-1/2"></div>
                  <div className="h-20 bg-slate-700 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : paginatedApplications.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {paginatedApplications.map((app) => (
            <Card key={app.id} className="bg-slate-800 border-slate-700 hover:border-slate-600 transition-colors">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg font-semibold text-slate-50 flex items-center space-x-2">
                      <span>{app.name}</span>
                      {getStatusIcon(app.status)}
                    </CardTitle>
                    <p className="text-sm text-slate-400 mt-1">v{app.version}</p>
                  </div>
                  <div className="flex flex-col space-y-1">
                    <Badge className={getCriticalityColor(app.criticality)}>
                      {app.criticality.toUpperCase()}
                    </Badge>
                    <Badge className={getEnvironmentColor(app.environment)}>
                      {app.environment.toUpperCase()}
                    </Badge>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-4">
                  {/* Description */}
                  <p className="text-sm text-slate-300 line-clamp-2">
                    {app.description}
                  </p>

                  {/* Technologies */}
                  <div>
                    <h4 className="text-xs font-medium text-slate-400 mb-2">Technologies</h4>
                    <div className="flex flex-wrap gap-1">
                      {app.technologies.slice(0, 3).map((tech, index) => (
                        <Badge key={index} variant="secondary" className="text-xs bg-slate-700 text-slate-300">
                          {tech}
                        </Badge>
                      ))}
                      {app.technologies.length > 3 && (
                        <Badge variant="secondary" className="text-xs bg-slate-700 text-slate-300">
                          +{app.technologies.length - 3}
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Vulnerability Summary */}
                  <div>
                    <h4 className="text-xs font-medium text-slate-400 mb-2">Vulnerabilities</h4>
                    <div className="grid grid-cols-4 gap-2 text-xs">
                      <div className="text-center">
                        <div className="text-red-400 font-bold">{app.vulnerability_count.critical}</div>
                        <div className="text-slate-500">Critical</div>
                      </div>
                      <div className="text-center">
                        <div className="text-orange-400 font-bold">{app.vulnerability_count.high}</div>
                        <div className="text-slate-500">High</div>
                      </div>
                      <div className="text-center">
                        <div className="text-yellow-400 font-bold">{app.vulnerability_count.medium}</div>
                        <div className="text-slate-500">Medium</div>
                      </div>
                      <div className="text-center">
                        <div className="text-green-400 font-bold">{app.vulnerability_count.low}</div>
                        <div className="text-slate-500">Low</div>
                      </div>
                    </div>
                  </div>

                  {/* Contact Info */}
                  <div className="text-xs text-slate-400">
                    <div className="flex items-center space-x-1">
                      <Users className="h-3 w-3" />
                      <span>{app.contact_person}</span>
                    </div>
                    <div className="flex items-center space-x-1 mt-1">
                      <Package className="h-3 w-3" />
                      <span>{app.business_unit}</span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-2 border-t border-slate-700">
                    <Badge className={getStatusColor(app.status)}>
                      {app.status}
                    </Badge>
                    <div className="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingApplication(app)}
                        className="text-slate-400 hover:text-slate-50 hover:bg-slate-700"
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      {app.deployment_url && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(app.deployment_url, '_blank')}
                          className="text-slate-400 hover:text-slate-50 hover:bg-slate-700"
                        >
                          <Globe className="h-3 w-3" />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteApplication(app.id)}
                        className="text-red-400 hover:text-red-300 hover:bg-slate-700"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-12 text-center">
            <Package className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-50 mb-2">No Applications Found</h3>
            <p className="text-slate-400 mb-4">
              No applications match your current filters. Try adjusting your search criteria.
            </p>
            <Button
              onClick={() => setFilters({
                search: '',
                environment: 'all',
                criticality: 'all',
                status: 'all',
                businessUnit: 'all',
              })}
              className="bg-cyan-600 hover:bg-cyan-700"
            >
              Clear Filters
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Edit Application Dialog */}
      {editingApplication && (
        <Dialog open={!!editingApplication} onOpenChange={() => setEditingApplication(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Application</DialogTitle>
              <DialogDescription>
                Update application information and configuration
              </DialogDescription>
            </DialogHeader>
            <ApplicationForm
              initialData={editingApplication}
              onSubmit={handleEditApplication}
              onCancel={() => setEditingApplication(null)}
              mode="edit"
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
