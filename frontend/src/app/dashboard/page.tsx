'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Shield,
  AlertTriangle,
  TrendingUp,
  Database,
  Activity,
  Users,
  Clock,
  CheckCircle,
  Eye,
  FileText,
  Settings,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  ExternalLink
} from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';

interface DashboardStats {
  totalCVEs: number;
  criticalCVEs: number;
  highCVEs: number;
  mediumCVEs: number;
  lowCVEs: number;
  applications: number;
  componentsScanned: number;
  lastUpdate: string;
  trendsData: {
    critical: { current: number; change: number; trend: 'up' | 'down' | 'stable' };
    high: { current: number; change: number; trend: 'up' | 'down' | 'stable' };
    applications: { current: number; change: number; trend: 'up' | 'down' | 'stable' };
  };
}

interface RecentCVE {
  id: string;
  cve_id: string;
  severity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  description: string;
  published_date: string;
  cvss_score: number;
  affected_products: string[];
}

interface SystemStatus {
  service: string;
  status: 'healthy' | 'warning' | 'error';
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  lastCheck: string;
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalCVEs: 1247,
    criticalCVEs: 23,
    highCVEs: 156,
    mediumCVEs: 342,
    lowCVEs: 726,
    applications: 45,
    componentsScanned: 1234,
    lastUpdate: '2 minutes ago',
    trendsData: {
      critical: { current: 23, change: 12, trend: 'up' },
      high: { current: 156, change: -5, trend: 'down' },
      applications: { current: 45, change: 3, trend: 'up' },
    },
  });

  const [recentCVEs, setRecentCVEs] = useState<RecentCVE[]>([
    {
      id: '1',
      cve_id: 'CVE-2024-0001',
      severity: 'CRITICAL',
      description: 'Remote code execution vulnerability in popular web framework allowing attackers to execute arbitrary code',
      published_date: '2024-01-15T10:30:00Z',
      cvss_score: 9.8,
      affected_products: ['React', 'Next.js', 'Express'],
    },
    {
      id: '2',
      cve_id: 'CVE-2024-0002',
      severity: 'HIGH',
      description: 'SQL injection vulnerability in database connector enabling unauthorized data access',
      published_date: '2024-01-14T15:45:00Z',
      cvss_score: 8.1,
      affected_products: ['PostgreSQL', 'MySQL'],
    },
    {
      id: '3',
      cve_id: 'CVE-2024-0003',
      severity: 'HIGH',
      description: 'Authentication bypass vulnerability in OAuth implementation',
      published_date: '2024-01-13T09:20:00Z',
      cvss_score: 7.5,
      affected_products: ['OAuth2', 'JWT'],
    },
    {
      id: '4',
      cve_id: 'CVE-2024-0004',
      severity: 'MEDIUM',
      description: 'Cross-site scripting vulnerability in UI component library',
      published_date: '2024-01-12T14:10:00Z',
      cvss_score: 6.1,
      affected_products: ['React', 'Vue.js'],
    },
  ]);

  const systemStatus: SystemStatus[] = [
    {
      service: 'CVE Feed',
      status: 'healthy',
      description: 'Real-time vulnerability data streaming',
      icon: Shield,
      lastCheck: '30 seconds ago',
    },
    {
      service: 'Database',
      status: 'healthy',
      description: 'All database connections active',
      icon: Database,
      lastCheck: '1 minute ago',
    },
    {
      service: 'API Gateway',
      status: 'warning',
      description: 'Elevated response times detected',
      icon: Activity,
      lastCheck: '2 minutes ago',
    },
    {
      service: 'Scanning Engine',
      status: 'healthy',
      description: 'Component scanning operational',
      icon: Eye,
      lastCheck: '45 seconds ago',
    },
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return 'bg-red-600 hover:bg-red-700 text-white border-red-600';
      case 'HIGH':
        return 'bg-orange-600 hover:bg-orange-700 text-white border-orange-600';
      case 'MEDIUM':
        return 'bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600';
      case 'LOW':
        return 'bg-green-600 hover:bg-green-700 text-white border-green-600';
      default:
        return 'bg-slate-600 hover:bg-slate-700 text-white border-slate-600';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-slate-100 text-slate-800 border-slate-200';
    }
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <ArrowUpRight className="h-3 w-3 text-red-500" />;
      case 'down':
        return <ArrowDownRight className="h-3 w-3 text-green-500" />;
      case 'stable':
        return <Minus className="h-3 w-3 text-slate-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="space-y-6" data-testid="dashboard-page">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-slate-50">Security Dashboard</h1>
          <p className="text-slate-400 mt-1">
            Comprehensive overview of your vulnerability management system
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-700">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button size="sm" className="bg-cyan-600 hover:bg-cyan-700">
            <FileText className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-300">Total CVEs</CardTitle>
            <Shield className="h-4 w-4 text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-50">{stats.totalCVEs.toLocaleString()}</div>
            <div className="flex items-center space-x-1 text-xs text-slate-400 mt-1">
              <span>Last 30 days</span>
            </div>
            <Progress
              value={75}
              className="mt-2 h-1"
            />
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-300">Critical CVEs</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-400">{stats.criticalCVEs}</div>
            <div className="flex items-center space-x-1 text-xs text-slate-400 mt-1">
              {getTrendIcon(stats.trendsData.critical.trend)}
              <span>{Math.abs(stats.trendsData.critical.change)}% from last week</span>
            </div>
            <div className="text-xs text-red-400 mt-1">Requires immediate attention</div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-300">High Severity</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-400">{stats.highCVEs}</div>
            <div className="flex items-center space-x-1 text-xs text-slate-400 mt-1">
              {getTrendIcon(stats.trendsData.high.trend)}
              <span>{Math.abs(stats.trendsData.high.change)}% from last week</span>
            </div>
            <div className="text-xs text-orange-400 mt-1">Priority remediation needed</div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-300">Applications</CardTitle>
            <Database className="h-4 w-4 text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-50">{stats.applications}</div>
            <div className="flex items-center space-x-1 text-xs text-slate-400 mt-1">
              {getTrendIcon(stats.trendsData.applications.trend)}
              <span>{Math.abs(stats.trendsData.applications.change)} new this month</span>
            </div>
            <div className="text-xs text-slate-400 mt-1">Actively monitored</div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Recent CVEs */}
        <Card className="lg:col-span-2 bg-slate-800 border-slate-700">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-slate-50">Recent CVEs</CardTitle>
                <CardDescription className="text-slate-400">
                  Latest vulnerabilities discovered in the past 24 hours
                </CardDescription>
              </div>
              <Link href="/cves">
                <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-700">
                  View All
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentCVEs.map((cve) => (
                <div key={cve.id} className="flex items-start space-x-4 p-3 rounded-lg bg-slate-700/50 hover:bg-slate-700 transition-colors">
                  <Badge className={getSeverityColor(cve.severity)}>
                    {cve.severity}
                  </Badge>
                  <div className="flex-1 space-y-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium text-slate-50">{cve.cve_id}</p>
                      <Badge variant="outline" className="text-xs border-slate-600 text-slate-400">
                        CVSS: {cve.cvss_score}
                      </Badge>
                    </div>
                    <p className="text-sm text-slate-300 line-clamp-2">
                      {cve.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {cve.affected_products.slice(0, 2).map((product, index) => (
                          <Badge key={index} variant="secondary" className="text-xs bg-slate-600 text-slate-300">
                            {product}
                          </Badge>
                        ))}
                        {cve.affected_products.length > 2 && (
                          <Badge variant="secondary" className="text-xs bg-slate-600 text-slate-300">
                            +{cve.affected_products.length - 2}
                          </Badge>
                        )}
                      </div>
                      <div className="text-xs text-slate-400">
                        {formatDate(cve.published_date)}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* System Status */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-slate-50">System Status</CardTitle>
            <CardDescription className="text-slate-400">
              Current status of monitoring systems
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {systemStatus.map((system) => (
                <div key={system.service} className="flex items-center space-x-3">
                  <div className={`p-2 rounded-full ${
                    system.status === 'healthy' ? 'bg-green-100' :
                    system.status === 'warning' ? 'bg-yellow-100' : 'bg-red-100'
                  }`}>
                    <system.icon className={`h-4 w-4 ${
                      system.status === 'healthy' ? 'text-green-600' :
                      system.status === 'warning' ? 'text-yellow-600' : 'text-red-600'
                    }`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-slate-50">{system.service}</p>
                    <p className="text-xs text-slate-400 truncate">{system.description}</p>
                    <p className="text-xs text-slate-500">{system.lastCheck}</p>
                  </div>
                  <Badge className={getStatusColor(system.status)}>
                    {system.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-slate-50">Quick Actions</CardTitle>
          <CardDescription className="text-slate-400">
            Common tasks and shortcuts for vulnerability management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Link href="/cves">
              <Button className="w-full justify-start bg-slate-700 hover:bg-slate-600 text-slate-50">
                <Shield className="mr-2 h-4 w-4" />
                View All CVEs
              </Button>
            </Link>
            <Link href="/applications">
              <Button variant="outline" className="w-full justify-start border-slate-600 text-slate-300 hover:bg-slate-700">
                <Database className="mr-2 h-4 w-4" />
                Manage Applications
              </Button>
            </Link>
            <Link href="/reports">
              <Button variant="outline" className="w-full justify-start border-slate-600 text-slate-300 hover:bg-slate-700">
                <FileText className="mr-2 h-4 w-4" />
                Generate Report
              </Button>
            </Link>
            <Link href="/settings">
              <Button variant="outline" className="w-full justify-start border-slate-600 text-slate-300 hover:bg-slate-700">
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
