{"name": "CVE Feed Service", "short_name": "<PERSON><PERSON>ed", "description": "Enterprise vulnerability management and CVE tracking system", "start_url": "/", "display": "standalone", "background_color": "#0f172a", "theme_color": "#06b6d4", "orientation": "portrait-primary", "scope": "/", "lang": "en", "dir": "ltr", "categories": ["security", "productivity", "business"], "screenshots": [{"src": "/screenshots/desktop-dashboard.png", "sizes": "1280x720", "type": "image/png", "platform": "wide", "label": "Dashboard view on desktop"}, {"src": "/screenshots/mobile-cve-list.png", "sizes": "390x844", "type": "image/png", "platform": "narrow", "label": "CVE list on mobile"}], "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "shortcuts": [{"name": "Dashboard", "short_name": "Dashboard", "description": "View security dashboard", "url": "/dashboard", "icons": [{"src": "/icons/shortcut-dashboard.png", "sizes": "96x96"}]}, {"name": "<PERSON><PERSON>ed", "short_name": "CVEs", "description": "Browse vulnerability feed", "url": "/cves", "icons": [{"src": "/icons/shortcut-cves.png", "sizes": "96x96"}]}, {"name": "Applications", "short_name": "Apps", "description": "Manage applications", "url": "/applications", "icons": [{"src": "/icons/shortcut-apps.png", "sizes": "96x96"}]}, {"name": "Reports", "short_name": "Reports", "description": "Generate security reports", "url": "/reports", "icons": [{"src": "/icons/shortcut-reports.png", "sizes": "96x96"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "capture_links": "existing-client-navigate"}