/**
 * Service Worker for CVE Feed Service PWA
 * Provides offline functionality, caching, and background sync
 */

const CACHE_NAME = 'cve-feed-v1.0.0';
const STATIC_CACHE = 'cve-feed-static-v1.0.0';
const DYNAMIC_CACHE = 'cve-feed-dynamic-v1.0.0';
const API_CACHE = 'cve-feed-api-v1.0.0';

// Static assets to cache
const STATIC_ASSETS = [
  '/',
  '/dashboard',
  '/cves',
  '/applications',
  '/search',
  '/reports',
  '/notifications',
  '/admin/users',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
];

// API endpoints to cache
const API_ENDPOINTS = [
  '/api/v1/dashboard/metrics',
  '/api/v1/cves/recent',
  '/api/v1/applications/status',
  '/api/v1/auth/me'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      }),
      // Skip waiting to activate immediately
      self.skipWaiting()
    ])
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE && 
                cacheName !== API_CACHE) {
              console.log('Service Worker: Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Take control of all clients
      self.clients.claim()
    ])
  );
});

// Fetch event - handle requests with caching strategy
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Handle different types of requests
  if (url.pathname.startsWith('/api/')) {
    // API requests - Network First with Cache Fallback
    event.respondWith(handleApiRequest(request));
  } else if (url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2)$/)) {
    // Static assets - Cache First
    event.respondWith(handleStaticAssets(request));
  } else {
    // HTML pages - Stale While Revalidate
    event.respondWith(handlePageRequest(request));
  }
});

// Handle API requests with Network First strategy
async function handleApiRequest(request) {
  const url = new URL(request.url);
  
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache successful responses
      const cache = await caches.open(API_CACHE);
      cache.put(request, networkResponse.clone());
      return networkResponse;
    }
    
    throw new Error('Network response not ok');
  } catch (error) {
    console.log('Service Worker: Network failed, trying cache for:', url.pathname);
    
    // Fallback to cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline response for critical endpoints
    if (API_ENDPOINTS.some(endpoint => url.pathname.includes(endpoint))) {
      return new Response(
        JSON.stringify({
          error: 'Offline',
          message: 'This data is not available offline',
          cached: false
        }),
        {
          status: 503,
          statusText: 'Service Unavailable',
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    throw error;
  }
}

// Handle static assets with Cache First strategy
async function handleStaticAssets(request) {
  try {
    // Try cache first
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Fallback to network
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache the response
      const cache = await caches.open(STATIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Service Worker: Failed to fetch static asset:', request.url);
    throw error;
  }
}

// Handle page requests with Stale While Revalidate strategy
async function handlePageRequest(request) {
  try {
    // Get from cache
    const cachedResponse = await caches.match(request);
    
    // Fetch from network in background
    const networkPromise = fetch(request).then((networkResponse) => {
      if (networkResponse.ok) {
        const cache = caches.open(DYNAMIC_CACHE);
        cache.then(c => c.put(request, networkResponse.clone()));
      }
      return networkResponse;
    });
    
    // Return cached version immediately if available
    if (cachedResponse) {
      // Update cache in background
      networkPromise.catch(() => {
        console.log('Service Worker: Background update failed for:', request.url);
      });
      return cachedResponse;
    }
    
    // Wait for network if no cache
    return await networkPromise;
  } catch (error) {
    console.log('Service Worker: Page request failed:', request.url);
    
    // Return offline page for navigation requests
    if (request.mode === 'navigate') {
      const offlineResponse = await caches.match('/');
      if (offlineResponse) {
        return offlineResponse;
      }
    }
    
    throw error;
  }
}

// Background sync for failed requests
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync triggered:', event.tag);
  
  if (event.tag === 'audit-sync') {
    event.waitUntil(syncAuditEvents());
  }
  
  if (event.tag === 'data-sync') {
    event.waitUntil(syncOfflineData());
  }
});

// Sync audit events that failed to send
async function syncAuditEvents() {
  try {
    console.log('Service Worker: Syncing audit events...');
    
    // Get failed audit events from IndexedDB or localStorage
    const failedEvents = await getFailedAuditEvents();
    
    for (const event of failedEvents) {
      try {
        const response = await fetch('/api/v1/audit', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(event)
        });
        
        if (response.ok) {
          await removeFailedAuditEvent(event.id);
        }
      } catch (error) {
        console.log('Service Worker: Failed to sync audit event:', event.id);
      }
    }
  } catch (error) {
    console.log('Service Worker: Audit sync failed:', error);
  }
}

// Sync offline data changes
async function syncOfflineData() {
  try {
    console.log('Service Worker: Syncing offline data...');
    
    // Implementation would sync any offline changes
    // This is a placeholder for actual sync logic
    
  } catch (error) {
    console.log('Service Worker: Data sync failed:', error);
  }
}

// Helper functions for audit event management
async function getFailedAuditEvents() {
  // This would typically use IndexedDB
  // For now, return empty array
  return [];
}

async function removeFailedAuditEvent(eventId) {
  // This would typically remove from IndexedDB
  console.log('Service Worker: Removing synced audit event:', eventId);
}

// Push notification handling
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push notification received');
  
  const options = {
    body: 'New security alert available',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.png',
    vibrate: [200, 100, 200],
    data: {
      url: '/notifications'
    },
    actions: [
      {
        action: 'view',
        title: 'View Alert',
        icon: '/icons/action-view.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/icons/action-dismiss.png'
      }
    ]
  };
  
  if (event.data) {
    const data = event.data.json();
    options.body = data.message || options.body;
    options.data = { ...options.data, ...data };
  }
  
  event.waitUntil(
    self.registration.showNotification('CVE Feed Service', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked');
  
  event.notification.close();
  
  const action = event.action;
  const data = event.notification.data;
  
  if (action === 'view' || !action) {
    // Open the app
    event.waitUntil(
      clients.openWindow(data.url || '/notifications')
    );
  }
  // Dismiss action does nothing (notification already closed)
});

// Message handling from main thread
self.addEventListener('message', (event) => {
  console.log('Service Worker: Message received:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CACHE_URLS') {
    event.waitUntil(
      caches.open(DYNAMIC_CACHE).then((cache) => {
        return cache.addAll(event.data.urls);
      })
    );
  }
});
