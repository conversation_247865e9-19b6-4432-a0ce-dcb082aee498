# CVE Feed Service MVP

A tailored vulnerability feed service that provides application-specific CVE data based on component inventories.

## 🎯 Phase 1 Complete - Core Data Foundation

This MVP provides the essential functionality for managing application inventories and delivering tailored CVE feeds.

### ✅ Features Implemented

#### 🗄️ Database Schema & Soft-Delete Implementation
- Comprehensive database models with soft-delete functionality
- Tables: Applications, Components, CPE_Mappings, CVEs, CVE_CPE_Applicability, Users, API_Keys
- Alembic migrations with proper indexing and constraints
- PostgreSQL with async support

#### 🔌 Application Inventory Management API
- **Applications**: Full CRUD operations with environment and criticality tracking
- **Components**: Manage application dependencies with version tracking
- **CPE Mappings**: Link components to Common Platform Enumeration identifiers
- Soft-delete support throughout all operations
- Comprehensive API documentation with OpenAPI/Swagger

#### 🔍 CPE Mapping & Validation
- CPE 2.3 format validation and parsing
- Manual CPE string input with real-time validation
- CPE-to-CVE matching logic for vulnerability correlation
- CPE normalization utilities

#### 📡 Tailored CVE Feed
- Application-specific vulnerability feeds based on component CPE mappings
- Severity-based filtering (LOW, MEDIUM, HIGH, CRITICAL)
- Pagination and advanced filtering options
- CVE detail endpoints with full applicability data

#### 🔐 Authentication & Authorization
- JWT-based authentication system
- Role-based access control (RBAC):
  - `it_admin`: Full system administration
  - `security_analyst`: Vulnerability analysis and reporting
- API key support for programmatic access
- User management (admin-only)
- Secure password handling with bcrypt

#### 🔄 CVE Data Ingestion
- NVD CVE API integration with rate limiting (10 requests/minute)
- Bulk import for initial data load (configurable years)
- Incremental updates using NVD's lastModified timestamps
- CVE enrichment with CVSS scores, CWE IDs, and references
- CLI commands for data management
- Comprehensive error handling and retry logic

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- PostgreSQL 12+
- Nix (recommended for development)

### Development Setup

1. **Clone the repository**
   ```bash
   <NAME_EMAIL>:forkrul/day3-cve-feed.git
   cd day3-cve-feed
   ```

2. **Enter development environment**
   ```bash
   nix-shell
   ```

3. **Set up database**
   ```bash
   # Create database
   createdb cve_feed_dev
   
   # Run migrations
   alembic upgrade head
   ```

4. **Start the API server**
   ```bash
   python -m src.cve_feed_service.main
   ```

5. **Access the API documentation**
   - Swagger UI: http://localhost:8000/api/v1/docs
   - ReDoc: http://localhost:8000/api/v1/redoc

### Initial Data Import

```bash
# Import CVE data from the last 2 years
python -m src.cve_feed_service.cli.main cve bulk-import --years 2

# Or import a specific CVE
python -m src.cve_feed_service.cli.main cve import-single CVE-2023-1234
```

## 📚 Documentation

Comprehensive documentation is available in the `docs/` directory:

- **User Guide**: Complete step-by-step instructions for all functionality
- **API Reference**: Detailed endpoint documentation with examples
- **Development Guide**: Setup, architecture, and contributing guidelines
- **Deployment Guide**: Production deployment with Docker and Kubernetes

**Build and view the documentation:**

```bash
cd docs
make html
make serve  # Serves at http://localhost:8080
```

## 🔌 API Usage

### Authentication

1. **Create a user** (requires admin access or direct database insert)
2. **Login to get JWT token**:
   ```bash
   curl -X POST "http://localhost:8000/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username": "admin", "password": "password"}'
   ```

3. **Use token in requests**:
   ```bash
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications"
   ```

### Basic Workflow

1. **Create an application**:
   ```bash
   curl -X POST "http://localhost:8000/api/v1/applications" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "My Web App",
          "environment": "production",
          "criticality": "high"
        }'
   ```

2. **Add components**:
   ```bash
   curl -X POST "http://localhost:8000/api/v1/applications/{app_id}/components" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "nginx",
          "version": "1.20.1",
          "vendor": "nginx"
        }'
   ```

3. **Add CPE mappings**:
   ```bash
   curl -X POST "http://localhost:8000/api/v1/components/{component_id}/cpe-mappings" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*"
        }'
   ```

4. **Get tailored CVE feed**:
   ```bash
   curl "http://localhost:8000/api/v1/cves/feed?application_id={app_id}&severity=HIGH" \
        -H "Authorization: Bearer YOUR_TOKEN"
   ```

## 🏗️ Architecture

### Technology Stack
- **Backend**: FastAPI with async/await
- **Database**: PostgreSQL with SQLAlchemy 2.0
- **Authentication**: JWT + API Keys
- **Validation**: Pydantic v2
- **Migrations**: Alembic
- **HTTP Client**: httpx for NVD API
- **Logging**: structlog
- **CLI**: Typer

### Project Structure
```
src/cve_feed_service/
├── api/v1/endpoints/     # API route handlers
├── cli/                  # Command-line interface
├── core/                 # Core utilities (auth, config, dependencies)
├── db/                   # Database configuration
├── models/               # SQLAlchemy models
├── schemas/              # Pydantic schemas
├── services/             # Business logic layer
└── utils/                # Utility functions
```

## 🔧 Configuration

Key environment variables:
- `DATABASE_URL`: PostgreSQL connection string
- `SECRET_KEY`: JWT signing key
- `NVD_API_KEY`: Optional NVD API key for higher rate limits
- `ENVIRONMENT`: deployment environment (development/staging/production)

## 📈 Success Criteria Met

✅ **Successfully ingest and maintain NVD CVE data**
- Automated bulk import and incremental updates
- Rate-limited API client with retry logic
- Comprehensive CVE data enrichment

✅ **Create/manage application inventory via API**
- Full CRUD operations for applications and components
- CPE mapping management with validation
- Soft-delete support throughout

✅ **Retrieve filtered CVE feed based on application components**
- Application-specific vulnerability matching
- Severity and date-based filtering
- Efficient database queries with pagination

✅ **API-first design with comprehensive documentation**
- OpenAPI/Swagger documentation
- Type-safe request/response handling
- Consistent error handling

## 🚦 Next Steps - Phase 2

The foundation is now ready for Phase 2 enhancements:
- Advanced CPE matching with version ranges
- SBOM (Software Bill of Materials) integration
- Enhanced CVE data from CISA Vulnrichment
- Basic web UI for inventory management
- Improved performance and caching

## 📄 License

MIT License - see LICENSE file for details.
