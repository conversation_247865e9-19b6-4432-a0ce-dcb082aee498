# Docker Compose override for development environment
# This file is automatically loaded by docker-compose and overrides settings in docker-compose.yml

version: '3.8'

services:
  # Development overrides for API service
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
      target: builder  # Use builder stage for development
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - CORS_ORIGINS=http://localhost:3000,http://localhost:3001,https://app.feedme.localhost,https://admin.feedme.localhost
    volumes:
      - ./src:/app/src:ro
      - ./logs:/app/logs
      - ./scripts:/app/scripts:ro
    command: ["python", "-m", "uvicorn", "src.cve_feed_service.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--workers", "1"]
    labels:
      - "traefik.http.routers.cve-api.rule=Host(`api.feedme.localhost`) || Host(`localhost`) && PathPrefix(`/api`)"

  # Development overrides for frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: deps  # Use deps stage for development
    environment:
      - NEXT_PUBLIC_API_URL=https://api.feedme.localhost
      - NEXT_PUBLIC_APP_ENV=development
      - NODE_ENV=development
    volumes:
      - ./frontend/src:/app/src:ro
      - ./frontend/public:/app/public:ro
      - ./frontend/package.json:/app/package.json:ro
      - ./frontend/next.config.js:/app/next.config.js:ro
      - ./frontend/tailwind.config.ts:/app/tailwind.config.ts:ro
      - ./frontend/tsconfig.json:/app/tsconfig.json:ro
    command: ["npm", "run", "dev"]
    labels:
      - "traefik.http.routers.cve-app.rule=Host(`app.feedme.localhost`) || Host(`localhost`)"

  # Development overrides for admin
  admin:
    build:
      context: ./admin
      dockerfile: Dockerfile
      target: deps  # Use deps stage for development
    environment:
      - REACT_APP_API_URL=https://api.feedme.localhost
      - REACT_APP_ENV=development
      - NODE_ENV=development
    volumes:
      - ./admin/src:/app/src:ro
      - ./admin/public:/app/public:ro
      - ./admin/package.json:/app/package.json:ro
    command: ["npm", "start"]
    labels:
      - "traefik.http.routers.cve-admin.rule=Host(`admin.feedme.localhost`)"
      - "traefik.http.routers.cve-admin.middlewares=admin-headers"  # Remove auth for development

  # Development overrides for docs
  docs:
    build:
      context: ./docs
      dockerfile: Dockerfile
      target: builder  # Use builder stage for development
    volumes:
      - ./docs:/docs:ro
    command: ["sphinx-autobuild", ".", "_build/html", "--host", "0.0.0.0", "--port", "80"]

  # Development overrides for worker
  worker:
    environment:
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
      - CVE_SYNC_INTERVAL=7200  # Longer interval for development
    volumes:
      - ./src:/app/src:ro
      - ./logs:/app/logs
      - ./scripts:/app/scripts:ro

  # Development database with exposed port
  postgres:
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=cve_feed_db_dev
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro

  # Development Redis with exposed port
  redis:
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data

  # Development Prometheus with exposed port
  prometheus:
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus-dev.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_dev_data:/prometheus

  # Development Grafana with exposed port
  grafana:
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=true
      - GF_SERVER_DOMAIN=localhost:3001
      - GF_SERVER_ROOT_URL=http://localhost:3001
    volumes:
      - grafana_dev_data:/var/lib/grafana

  # Add development tools
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: cve-feed-pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - cve-network
    depends_on:
      - postgres
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.pgadmin.rule=Host(`pgadmin.feedme.localhost`)"
      - "traefik.http.routers.pgadmin.entrypoints=web"
      - "traefik.http.services.pgadmin.loadbalancer.server.port=80"

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: cve-feed-redis-commander
    environment:
      - REDIS_HOSTS=local:redis:6379:0:redis_password_secure_2024
    networks:
      - cve-network
    depends_on:
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.redis-commander.rule=Host(`redis.feedme.localhost`)"
      - "traefik.http.routers.redis-commander.entrypoints=web"
      - "traefik.http.services.redis-commander.loadbalancer.server.port=8081"

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  prometheus_dev_data:
    driver: local
  grafana_dev_data:
    driver: local
  pgadmin_data:
    driver: local
