Installation
============

This guide covers the installation and setup of the CVE Feed Service for both development and production environments.

Prerequisites
-------------

System Requirements
~~~~~~~~~~~~~~~~~~~

* **Python**: 3.11 or higher
* **PostgreSQL**: 12 or higher
* **Memory**: Minimum 2GB RAM (4GB+ recommended for production)
* **Storage**: 10GB+ for CVE data storage
* **Network**: Internet access for NVD API integration

Development Tools (Optional)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

* **Nix**: For reproducible development environment
* **Docker**: For containerized deployment
* **Git**: For version control

Quick Start with Nix (Recommended)
-----------------------------------

The fastest way to get started is using the provided Nix environment:

.. code-block:: bash

   # Clone the repository
   <NAME_EMAIL>:forkrul/day3-cve-feed.git
   cd day3-cve-feed

   # Enter the development environment
   nix-shell

   # The environment will automatically set up Python, dependencies, and environment variables

Manual Installation
-------------------

If you prefer not to use Nix, follow these steps:

1. **Clone the Repository**

   .. code-block:: bash

      <NAME_EMAIL>:forkrul/day3-cve-feed.git
      cd day3-cve-feed

2. **Set Up Python Environment**

   .. code-block:: bash

      # Create virtual environment
      python3.11 -m venv venv
      source venv/bin/activate  # On Windows: venv\Scripts\activate

      # Install the package in development mode
      pip install -e .

3. **Install Development Dependencies**

   .. code-block:: bash

      pip install -e ".[dev]"

Database Setup
--------------

PostgreSQL Installation
~~~~~~~~~~~~~~~~~~~~~~~

**Ubuntu/Debian:**

.. code-block:: bash

   sudo apt update
   sudo apt install postgresql postgresql-contrib
   sudo systemctl start postgresql
   sudo systemctl enable postgresql

**macOS (with Homebrew):**

.. code-block:: bash

   brew install postgresql
   brew services start postgresql

**Windows:**

Download and install from `PostgreSQL official website <https://www.postgresql.org/download/windows/>`_.

Database Configuration
~~~~~~~~~~~~~~~~~~~~~~

1. **Create Database and User**

   .. code-block:: bash

      # Switch to postgres user
      sudo -u postgres psql

   .. code-block:: sql

      -- Create database
      CREATE DATABASE cve_feed_dev;
      
      -- Create user (optional, for production)
      CREATE USER cve_feed_user WITH PASSWORD 'your_secure_password';
      GRANT ALL PRIVILEGES ON DATABASE cve_feed_dev TO cve_feed_user;
      
      -- Exit psql
      \q

2. **Run Database Migrations**

   .. code-block:: bash

      # Run all migrations
      alembic upgrade head

Environment Configuration
-------------------------

Create a ``.env`` file in the project root:

.. code-block:: bash

   # Development environment settings
   ENVIRONMENT=development
   DEBUG=true
   
   # Database configuration
   DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/cve_feed_dev
   
   # Security settings
   SECRET_KEY=your-secret-key-change-in-production
   
   # NVD API configuration (optional)
   NVD_API_KEY=your-nvd-api-key
   NVD_RATE_LIMIT_PER_MINUTE=10
   
   # Logging
   LOG_LEVEL=INFO

Configuration Options
~~~~~~~~~~~~~~~~~~~~~

**Required Settings:**

* ``DATABASE_URL``: PostgreSQL connection string
* ``SECRET_KEY``: JWT signing key (use a secure random string in production)

**Optional Settings:**

* ``NVD_API_KEY``: Increases rate limits from 5 to 50 requests per 30 seconds
* ``ENVIRONMENT``: ``development``, ``staging``, or ``production``
* ``DEBUG``: Enable debug mode (development only)
* ``LOG_LEVEL``: ``DEBUG``, ``INFO``, ``WARNING``, ``ERROR``, or ``CRITICAL``

Verification
------------

1. **Test the Installation**

   .. code-block:: bash

      # Run basic tests
      python -m pytest tests/test_main.py -v

2. **Start the Development Server**

   .. code-block:: bash

      # Start the API server
      python -m src.cve_feed_service.main

   The server will start on ``http://localhost:8000``

3. **Check API Documentation**

   Open your browser and navigate to:
   
   * Swagger UI: http://localhost:8000/api/v1/docs
   * ReDoc: http://localhost:8000/api/v1/redoc

4. **Verify Health Endpoints**

   .. code-block:: bash

      # Check health
      curl http://localhost:8000/health
      
      # Check readiness
      curl http://localhost:8000/readiness

Initial Data Setup
------------------

1. **Create Initial User**

   Since the system requires authentication, you'll need to create an initial admin user. You can do this by directly inserting into the database or creating a management command.

   **Direct Database Insert:**

   .. code-block:: sql

      -- Connect to your database
      psql -d cve_feed_dev
      
      -- Insert admin user (password: 'admin123')
      INSERT INTO users (id, username, email, full_name, hashed_password, is_active, role, created_at, updated_at)
      VALUES (
          gen_random_uuid(),
          'admin',
          '<EMAIL>',
          'System Administrator',
          '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXwtO5S5vy6e',  -- bcrypt hash of 'admin123'
          true,
          'it_admin',
          NOW(),
          NOW()
      );

2. **Import CVE Data**

   .. code-block:: bash

      # Import CVE data from the last 2 years (this may take a while)
      python -m src.cve_feed_service.cli.main cve bulk-import --years 2
      
      # Or start with a smaller dataset
      python -m src.cve_feed_service.cli.main cve bulk-import --years 1

Production Considerations
-------------------------

**Security:**

* Use a strong, randomly generated ``SECRET_KEY``
* Set up proper database user with limited privileges
* Use environment variables for sensitive configuration
* Enable HTTPS/TLS in production

**Performance:**

* Configure PostgreSQL for your workload
* Set up connection pooling
* Consider read replicas for high-traffic scenarios
* Monitor database performance and query optimization

**Monitoring:**

* Set up application logging
* Monitor API response times
* Track CVE ingestion success rates
* Set up alerts for system health

**Backup:**

* Regular database backups
* Test backup restoration procedures
* Consider point-in-time recovery setup

Troubleshooting
---------------

**Common Issues:**

**Database Connection Errors**
   * Verify PostgreSQL is running
   * Check database URL format
   * Ensure database exists and user has permissions

**Migration Failures**
   * Check database connectivity
   * Verify user has DDL permissions
   * Review migration logs for specific errors

**NVD API Rate Limiting**
   * Reduce import batch sizes
   * Consider getting an NVD API key
   * Monitor rate limit compliance

**Memory Issues During CVE Import**
   * Reduce batch size in configuration
   * Increase available memory
   * Monitor memory usage during imports

**Getting Help:**

* Check the logs for detailed error messages
* Review the API documentation at ``/api/v1/docs``
* Search existing issues on GitHub
* Create a new issue with detailed error information
