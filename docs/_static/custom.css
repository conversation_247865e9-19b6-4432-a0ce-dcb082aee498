/* Custom CSS for CVE Feed Service Documentation */

/* Mermaid diagram styling */
.mermaid {
    text-align: center;
    margin: 20px 0;
}

.mermaid svg {
    max-width: 100%;
    height: auto;
}

/* Code block improvements */
.highlight {
    margin: 1em 0;
}

.highlight pre {
    padding: 12px;
    border-radius: 4px;
    overflow-x: auto;
}

/* Copy button styling */
.copybtn {
    position: absolute;
    top: 0.5em;
    right: 0.5em;
    width: 1.7em;
    height: 1.7em;
    opacity: 0;
    transition: opacity 0.3s, border 0.3s, background-color 0.3s;
    user-select: none;
    padding: 0;
    border: none;
    outline: none;
    border-radius: 0.4em;
    cursor: pointer;
    background-color: transparent;
}

.copybtn:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.2);
}

.highlight:hover .copybtn {
    opacity: 0.3;
}

/* Admonition styling */
.admonition {
    margin: 1em 0;
    padding: 12px;
    border-left: 4px solid;
    border-radius: 4px;
}

.admonition.note {
    border-color: #2980B9;
    background-color: #EBF3FD;
}

.admonition.warning {
    border-color: #F39C12;
    background-color: #FEF9E7;
}

.admonition.danger {
    border-color: #E74C3C;
    background-color: #FDEDEC;
}

.admonition.tip {
    border-color: #27AE60;
    background-color: #EAFAF1;
}

/* Table styling */
table.docutils {
    border-collapse: collapse;
    margin: 1em 0;
    width: 100%;
}

table.docutils th,
table.docutils td {
    border: 1px solid #E1E4E5;
    padding: 8px 12px;
    text-align: left;
}

table.docutils th {
    background-color: #F8F9FA;
    font-weight: bold;
}

table.docutils tr:nth-child(even) {
    background-color: #F8F9FA;
}

/* Navigation improvements */
.wy-nav-content {
    max-width: 1200px;
}

.wy-nav-content-wrap {
    margin-left: 300px;
}

/* API documentation styling */
.py.class > dt,
.py.function > dt,
.py.method > dt {
    background-color: #F8F9FA;
    border: 1px solid #E1E4E5;
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 8px;
}

.py.class .sig-name,
.py.function .sig-name,
.py.method .sig-name {
    font-weight: bold;
    color: #2980B9;
}

/* Tabs styling */
.sphinx-tabs {
    margin: 1em 0;
}

.sphinx-tabs-tab {
    background-color: #F8F9FA;
    border: 1px solid #E1E4E5;
    border-bottom: none;
    padding: 8px 16px;
    cursor: pointer;
    display: inline-block;
    margin-right: 2px;
    border-radius: 4px 4px 0 0;
}

.sphinx-tabs-tab[aria-selected="true"] {
    background-color: #2980B9;
    color: white;
    border-color: #2980B9;
}

.sphinx-tabs-panel {
    border: 1px solid #E1E4E5;
    padding: 16px;
    border-radius: 0 4px 4px 4px;
    background-color: white;
}

/* Responsive design */
@media screen and (max-width: 768px) {
    .wy-nav-content-wrap {
        margin-left: 0;
    }
    
    .mermaid svg {
        max-width: 100%;
        height: auto;
    }
    
    table.docutils {
        font-size: 0.9em;
    }
    
    .sphinx-tabs-tab {
        display: block;
        margin-bottom: 2px;
        margin-right: 0;
        border-radius: 4px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .admonition.note {
        background-color: #1E3A5F;
        color: #E8F4FD;
    }
    
    .admonition.warning {
        background-color: #3D2914;
        color: #FEF9E7;
    }
    
    .admonition.danger {
        background-color: #3B1F1F;
        color: #FDEDEC;
    }
    
    .admonition.tip {
        background-color: #1F3A2E;
        color: #EAFAF1;
    }
}

/* Print styles */
@media print {
    .mermaid {
        break-inside: avoid;
    }
    
    .copybtn {
        display: none;
    }
    
    .sphinx-tabs-tab {
        display: none;
    }
    
    .sphinx-tabs-panel {
        border: none;
        padding: 0;
    }
}

/* Custom classes for documentation */
.architecture-diagram {
    text-align: center;
    margin: 2em 0;
    padding: 1em;
    background-color: #F8F9FA;
    border-radius: 8px;
}

.api-endpoint {
    background-color: #E8F5E8;
    border-left: 4px solid #27AE60;
    padding: 8px 12px;
    margin: 8px 0;
    border-radius: 0 4px 4px 0;
}

.security-note {
    background-color: #FFF3CD;
    border: 1px solid #FFEAA7;
    border-radius: 4px;
    padding: 12px;
    margin: 1em 0;
}

.security-note::before {
    content: "🔒 ";
    font-weight: bold;
}

.performance-tip {
    background-color: #E1F5FE;
    border: 1px solid #81D4FA;
    border-radius: 4px;
    padding: 12px;
    margin: 1em 0;
}

.performance-tip::before {
    content: "⚡ ";
    font-weight: bold;
}

/* Version badge */
.version-badge {
    display: inline-block;
    background-color: #2980B9;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
    margin-left: 8px;
}

/* Status indicators */
.status-stable {
    color: #27AE60;
    font-weight: bold;
}

.status-beta {
    color: #F39C12;
    font-weight: bold;
}

.status-experimental {
    color: #E74C3C;
    font-weight: bold;
}

/* Code language indicators */
.highlight-python::before {
    content: "Python";
    background-color: #3776AB;
    color: white;
    padding: 2px 8px;
    border-radius: 0 0 4px 0;
    font-size: 0.8em;
    position: absolute;
    top: 0;
    right: 0;
}

.highlight-typescript::before {
    content: "TypeScript";
    background-color: #3178C6;
    color: white;
    padding: 2px 8px;
    border-radius: 0 0 4px 0;
    font-size: 0.8em;
    position: absolute;
    top: 0;
    right: 0;
}

.highlight-bash::before {
    content: "Bash";
    background-color: #4EAA25;
    color: white;
    padding: 2px 8px;
    border-radius: 0 0 4px 0;
    font-size: 0.8em;
    position: absolute;
    top: 0;
    right: 0;
}

.highlight-json::before {
    content: "JSON";
    background-color: #000000;
    color: white;
    padding: 2px 8px;
    border-radius: 0 0 4px 0;
    font-size: 0.8em;
    position: absolute;
    top: 0;
    right: 0;
}
