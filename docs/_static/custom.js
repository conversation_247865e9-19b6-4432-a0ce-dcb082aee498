// Custom JavaScript for CVE Feed Service Documentation

document.addEventListener('DOMContentLoaded', function() {
    // Initialize mermaid diagrams
    if (typeof mermaid !== 'undefined') {
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#2980B9',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#1f5f99',
                lineColor: '#34495e',
                sectionBkgColor: '#ecf0f1',
                altSectionBkgColor: '#bdc3c7',
                gridColor: '#95a5a6',
                secondaryColor: '#e74c3c',
                tertiaryColor: '#f39c12'
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            }
        });
    }

    // Add copy functionality to code blocks
    addCopyButtons();
    
    // Add expand/collapse functionality to large code blocks
    addCodeBlockToggle();
    
    // Add smooth scrolling to anchor links
    addSmoothScrolling();
    
    // Add table of contents highlighting
    addTocHighlighting();
    
    // Add search functionality enhancements
    enhanceSearch();
    
    // Add responsive table handling
    makeTablesResponsive();
});

function addCopyButtons() {
    const codeBlocks = document.querySelectorAll('pre');
    
    codeBlocks.forEach(function(codeBlock) {
        if (codeBlock.querySelector('.copybtn')) {
            return; // Already has copy button
        }
        
        const button = document.createElement('button');
        button.className = 'copybtn';
        button.innerHTML = '📋';
        button.title = 'Copy to clipboard';
        
        button.addEventListener('click', function() {
            const code = codeBlock.querySelector('code');
            const text = code ? code.textContent : codeBlock.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                button.innerHTML = '✅';
                button.title = 'Copied!';
                
                setTimeout(function() {
                    button.innerHTML = '📋';
                    button.title = 'Copy to clipboard';
                }, 2000);
            }).catch(function() {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                button.innerHTML = '✅';
                button.title = 'Copied!';
                
                setTimeout(function() {
                    button.innerHTML = '📋';
                    button.title = 'Copy to clipboard';
                }, 2000);
            });
        });
        
        codeBlock.style.position = 'relative';
        codeBlock.appendChild(button);
    });
}

function addCodeBlockToggle() {
    const longCodeBlocks = document.querySelectorAll('pre');
    
    longCodeBlocks.forEach(function(codeBlock) {
        const lines = codeBlock.textContent.split('\n').length;
        
        if (lines > 20) {
            const toggleButton = document.createElement('button');
            toggleButton.className = 'code-toggle';
            toggleButton.innerHTML = 'Show more';
            toggleButton.style.cssText = `
                display: block;
                margin: 8px auto;
                padding: 4px 12px;
                background-color: #2980B9;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 0.9em;
            `;
            
            codeBlock.style.maxHeight = '400px';
            codeBlock.style.overflow = 'hidden';
            
            toggleButton.addEventListener('click', function() {
                if (codeBlock.style.maxHeight === '400px') {
                    codeBlock.style.maxHeight = 'none';
                    toggleButton.innerHTML = 'Show less';
                } else {
                    codeBlock.style.maxHeight = '400px';
                    toggleButton.innerHTML = 'Show more';
                }
            });
            
            codeBlock.parentNode.insertBefore(toggleButton, codeBlock.nextSibling);
        }
    });
}

function addSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(function(link) {
        link.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                e.preventDefault();
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Update URL without jumping
                history.pushState(null, null, '#' + targetId);
            }
        });
    });
}

function addTocHighlighting() {
    const tocLinks = document.querySelectorAll('.wy-menu-vertical a');
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    
    if (tocLinks.length === 0 || headings.length === 0) {
        return;
    }
    
    function highlightTocItem() {
        let current = '';
        
        headings.forEach(function(heading) {
            const rect = heading.getBoundingClientRect();
            if (rect.top <= 100) {
                current = heading.id;
            }
        });
        
        tocLinks.forEach(function(link) {
            link.classList.remove('current');
            if (link.getAttribute('href') === '#' + current) {
                link.classList.add('current');
            }
        });
    }
    
    window.addEventListener('scroll', highlightTocItem);
    highlightTocItem(); // Initial call
}

function enhanceSearch() {
    const searchInput = document.querySelector('input[name="q"]');
    
    if (searchInput) {
        // Add search suggestions
        const suggestions = [
            'API endpoints',
            'Authentication',
            'Vulnerabilities',
            'Applications',
            'Components',
            'Installation',
            'Configuration',
            'Testing',
            'Deployment'
        ];
        
        const datalist = document.createElement('datalist');
        datalist.id = 'search-suggestions';
        
        suggestions.forEach(function(suggestion) {
            const option = document.createElement('option');
            option.value = suggestion;
            datalist.appendChild(option);
        });
        
        searchInput.setAttribute('list', 'search-suggestions');
        searchInput.parentNode.appendChild(datalist);
        
        // Add search keyboard shortcut
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                searchInput.focus();
            }
        });
    }
}

function makeTablesResponsive() {
    const tables = document.querySelectorAll('table.docutils');
    
    tables.forEach(function(table) {
        const wrapper = document.createElement('div');
        wrapper.className = 'table-responsive';
        wrapper.style.cssText = `
            overflow-x: auto;
            margin: 1em 0;
            border: 1px solid #E1E4E5;
            border-radius: 4px;
        `;
        
        table.parentNode.insertBefore(wrapper, table);
        wrapper.appendChild(table);
    });
}

// Add version comparison functionality
function addVersionComparison() {
    const versionElements = document.querySelectorAll('.version-badge');
    
    versionElements.forEach(function(element) {
        element.addEventListener('click', function() {
            const version = this.textContent;
            showVersionInfo(version);
        });
    });
}

function showVersionInfo(version) {
    const modal = document.createElement('div');
    modal.className = 'version-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    `;
    
    const content = document.createElement('div');
    content.style.cssText = `
        background-color: white;
        padding: 20px;
        border-radius: 8px;
        max-width: 500px;
        max-height: 80vh;
        overflow-y: auto;
    `;
    
    content.innerHTML = `
        <h3>Version ${version}</h3>
        <p>Information about version ${version} features and changes.</p>
        <button onclick="this.closest('.version-modal').remove()">Close</button>
    `;
    
    modal.appendChild(content);
    document.body.appendChild(modal);
    
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// Add print functionality
function addPrintButton() {
    const printButton = document.createElement('button');
    printButton.innerHTML = '🖨️ Print';
    printButton.className = 'print-button';
    printButton.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 8px 16px;
        background-color: #2980B9;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        z-index: 100;
    `;
    
    printButton.addEventListener('click', function() {
        window.print();
    });
    
    document.body.appendChild(printButton);
}

// Initialize additional features
document.addEventListener('DOMContentLoaded', function() {
    addVersionComparison();
    addPrintButton();
});

// Add dark mode toggle
function addDarkModeToggle() {
    const toggle = document.createElement('button');
    toggle.innerHTML = '🌙';
    toggle.className = 'dark-mode-toggle';
    toggle.style.cssText = `
        position: fixed;
        top: 70px;
        right: 20px;
        padding: 8px;
        background-color: #2980B9;
        color: white;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        z-index: 100;
        width: 40px;
        height: 40px;
    `;
    
    toggle.addEventListener('click', function() {
        document.body.classList.toggle('dark-mode');
        this.innerHTML = document.body.classList.contains('dark-mode') ? '☀️' : '🌙';
        
        // Save preference
        localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
    });
    
    // Load saved preference
    if (localStorage.getItem('darkMode') === 'true') {
        document.body.classList.add('dark-mode');
        toggle.innerHTML = '☀️';
    }
    
    document.body.appendChild(toggle);
}

// Initialize dark mode toggle
document.addEventListener('DOMContentLoaded', function() {
    addDarkModeToggle();
});
