# Sphinx Documentation Requirements
# Core Sphinx packages
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0
sphinx-copybutton>=0.5.2
sphinx-tabs>=3.4.1
sphinx-design>=0.5.0

# Mermaid diagram support
sphinxcontrib-mermaid>=0.9.2

# Markdown support
myst-parser>=2.0.0

# API documentation
sphinx-autodoc-typehints>=1.24.0
sphinx-autosummary-accessors>=2023.4.0

# Code quality and formatting
black>=23.7.0
isort>=5.12.0
flake8>=6.0.0

# Additional extensions
sphinx-external-toc>=1.0.1
sphinx-togglebutton>=0.3.2
sphinx-inline-tabs>=2023.4.21

# Development dependencies
livereload>=2.6.3
watchdog>=3.0.0

# Optional: For PDF generation
# rst2pdf>=0.101
# weasyprint>=59.0

# Optional: For advanced features
# sphinx-gallery>=0.13.0
# nbsphinx>=0.9.1
# jupyter>=1.0.0
