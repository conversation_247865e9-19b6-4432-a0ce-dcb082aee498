# CVE Feed Service Documentation

This directory contains the comprehensive documentation for the CVE Feed Service, built with <PERSON>phinx and featuring **mermaid diagrams**, **React setup guides**, and **comprehensive user flows**.

## Building the Documentation

### Prerequisites

The documentation dependencies are included in the Nix development environment or can be installed via pip:

```bash
# Using Nix (recommended)
nix-shell

# Or install documentation dependencies
pip install -e ".[docs]"
```

### Build Commands

```bash
# Build HTML documentation
cd docs
make html

# Clean build files
make clean

# Build and serve with auto-reload (development)
make livehtml

# Build and serve locally
make serve
```

### Viewing the Documentation

After building, the documentation will be available at:

- **Local file**: `docs/_build/html/index.html`
- **Local server**: `http://localhost:8080` (after `make serve`)
- **Live reload**: `http://localhost:8000` (after `make livehtml`)

## Documentation Structure

```
docs/
├── index.rst                    # Main documentation index
├── overview.rst                 # System overview and concepts
├── installation.rst             # Installation and setup guide
├── user-guide/                  # Complete user documentation
│   ├── index.rst               # User guide index
│   ├── authentication.rst      # Auth and user management
│   ├── application-management.rst # Application CRUD operations
│   ├── component-management.rst   # Component inventory management
│   ├── cpe-mapping.rst         # CPE mapping and validation
│   ├── vulnerability-feeds.rst # CVE feeds and filtering
│   ├── data-management.rst     # CVE data import and maintenance
│   └── workflows.rst           # End-to-end workflows and examples
├── api-reference/              # API documentation
│   └── index.rst              # API reference index
├── development/                # Developer documentation
│   └── index.rst              # Development setup and guidelines
├── deployment/                 # Production deployment guides
│   └── index.rst              # Docker, Kubernetes, monitoring
├── conf.py                     # Sphinx configuration
├── Makefile                    # Build commands
└── README.md                   # This file
```

## Documentation Features

### Comprehensive Coverage

- **User Guide**: Complete step-by-step instructions for all functionality
- **API Reference**: Detailed endpoint documentation with examples
- **Development Guide**: Setup, architecture, and contributing guidelines
- **React Setup Guide**: Complete frontend development environment setup
- **Service Architecture**: Detailed service documentation with mermaid diagrams
- **Testing Documentation**: TDD, BDD, integration, and UX testing frameworks
- **Enterprise Deployment**: Production-ready deployment patterns
- **BDD Business Validation**: Natural language business requirement testing

### Interactive Examples

- **Real curl commands** for all API endpoints
- **Code samples** in Python, TypeScript, and Bash
- **Complete workflow examples** with sequence diagrams
- **React component examples** with TypeScript
- **Mermaid architecture diagrams** for visual understanding
- **User flow sequence diagrams** for frontend interactions
- **Troubleshooting guides** with common solutions

### Professional Formatting

- **Sphinx with Read the Docs theme** - Professional appearance
- **Mermaid diagram support** - Interactive service architecture diagrams
- **React setup documentation** - Complete frontend development guide
- **MyST parser for Markdown support** - Flexible content authoring
- **Syntax highlighting for code blocks** - 20+ programming languages
- **Cross-references and navigation** - Seamless content linking
- **Search functionality** - Full-text search with indexing
- **Dark mode support** - Optimized for security professionals
- **Mobile responsive design** - Works on all devices

## Contributing to Documentation

### Writing Guidelines

1. **Use clear, concise language**
2. **Include practical examples**
3. **Provide troubleshooting information**
4. **Keep examples up-to-date**
5. **Follow existing structure and style**

### Adding New Documentation

1. Create new `.rst` files in appropriate directories
2. Add to the relevant `toctree` directive
3. Build and test locally
4. Submit pull request with changes

### Style Guidelines

- Use reStructuredText (`.rst`) format
- Include code examples for all procedures
- Add troubleshooting sections for complex topics
- Use consistent heading levels
- Include cross-references where helpful

## Maintenance

### Regular Updates

- Keep API examples current with code changes
- Update version numbers and compatibility information
- Review and update troubleshooting guides
- Verify all external links work

### Building in CI/CD

The documentation can be built automatically in CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Build Documentation
  run: |
    pip install -e ".[docs]"
    cd docs
    make html
    
- name: Deploy to GitHub Pages
  uses: peaceiris/actions-gh-pages@v3
  with:
    github_token: ${{ secrets.GITHUB_TOKEN }}
    publish_dir: ./docs/_build/html
```

## Support

For documentation issues:

1. Check the build output for errors
2. Verify Sphinx and theme versions
3. Review the Sphinx documentation
4. Create an issue with build logs if needed

## External Resources

- [Sphinx Documentation](https://www.sphinx-doc.org/)
- [reStructuredText Primer](https://www.sphinx-doc.org/en/master/usage/restructuredtext/basics.html)
- [Read the Docs Theme](https://sphinx-rtd-theme.readthedocs.io/)
- [MyST Parser](https://myst-parser.readthedocs.io/)
