Integration Testing
===================

Integration tests verify that different components of the CVE Feed Service work correctly together. These tests focus on service interactions, database operations, and external API integrations.

Overview
--------

Integration tests bridge the gap between unit tests and end-to-end tests by testing component interactions while maintaining reasonable execution speed and reliability.

**Integration Test Characteristics**:
* **Real Dependencies**: Use actual database connections and services
* **Component Interactions**: Test how services work together
* **Data Persistence**: Verify database operations and transactions
* **API Integration**: Test external service integrations
* **Moderate Speed**: Slower than unit tests but faster than E2E tests

Integration Test Architecture
----------------------------

Test Database Strategy
~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[Test Execution] --> B[Create Test Database]
       B --> C[Run Migrations]
       C --> D[Execute Tests]
       D --> E[Cleanup Test Data]
       E --> F[Drop Test Database]
       
       subgraph "Test Isolation"
           G[Test 1] --> H[Transaction]
           I[Test 2] --> J[Transaction]
           K[Test 3] --> L[Transaction]
           
           H --> M[Rollback]
           J --> M
           L --> M
       end

**Database Setup Strategy**:
* Use separate test database
* Run migrations before tests
* Use transactions for test isolation
* Clean up data between tests

Service Integration Testing
--------------------------

Application-Component Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/integration/test_application_component_integration.py
   import pytest
   from sqlalchemy.ext.asyncio import AsyncSession
   
   from src.cve_feed_service.services.application_service import ApplicationService
   from src.cve_feed_service.services.component_service import ComponentService
   from src.cve_feed_service.schemas.application import ApplicationCreate
   from src.cve_feed_service.schemas.component import ComponentCreate
   from src.cve_feed_service.schemas.cpe_mapping import CPEMappingCreate
   
   
   @pytest.mark.integration
   class TestApplicationComponentIntegration:
       """Integration tests for application and component services."""
       
       async def test_create_application_with_components_and_cpe_mappings(
           self, db_session: AsyncSession
       ):
           """Test creating application with components and CPE mappings."""
           # Arrange
           app_service = ApplicationService(db_session)
           comp_service = ComponentService(db_session)
           
           app_data = ApplicationCreate(
               name="Web Application",
               environment="production",
               criticality="high"
           )
           
           # Act - Create application
           application = await app_service.create_application(app_data)
           
           # Act - Add components
           nginx_data = ComponentCreate(
               name="nginx",
               version="1.20.1",
               vendor="nginx",
               component_type="web_server"
           )
           nginx_component = await comp_service.create_component(
               application.id, nginx_data
           )
           
           postgres_data = ComponentCreate(
               name="postgresql",
               version="13.8",
               vendor="postgresql",
               component_type="database"
           )
           postgres_component = await comp_service.create_component(
               application.id, postgres_data
           )
           
           # Act - Add CPE mappings
           nginx_cpe_data = CPEMappingCreate(
               cpe_string="cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
               confidence=1.0,
               mapping_source="official"
           )
           nginx_cpe = await comp_service.create_cpe_mapping(
               nginx_component.id, nginx_cpe_data
           )
           
           postgres_cpe_data = CPEMappingCreate(
               cpe_string="cpe:2.3:a:postgresql:postgresql:13.8:*:*:*:*:*:*:*",
               confidence=1.0,
               mapping_source="official"
           )
           postgres_cpe = await comp_service.create_cpe_mapping(
               postgres_component.id, postgres_cpe_data
           )
           
           # Assert - Verify complete structure
           retrieved_app = await app_service.get_application(
               application.id, include_components=True
           )
           
           assert retrieved_app is not None
           assert len(retrieved_app.components) == 2
           
           # Verify nginx component
           nginx_comp = next(
               (c for c in retrieved_app.components if c.name == "nginx"), None
           )
           assert nginx_comp is not None
           assert nginx_comp.version == "1.20.1"
           assert len(nginx_comp.cpe_mappings) == 1
           assert nginx_comp.cpe_mappings[0].cpe_string == nginx_cpe_data.cpe_string
           
           # Verify postgres component
           postgres_comp = next(
               (c for c in retrieved_app.components if c.name == "postgresql"), None
           )
           assert postgres_comp is not None
           assert postgres_comp.version == "13.8"
           assert len(postgres_comp.cpe_mappings) == 1
           assert postgres_comp.cpe_mappings[0].cpe_string == postgres_cpe_data.cpe_string
       
       async def test_delete_application_cascades_to_components_and_cpe_mappings(
           self, db_session: AsyncSession
       ):
           """Test that deleting application cascades to components and CPE mappings."""
           # Arrange - Create application with components and CPE mappings
           app_service = ApplicationService(db_session)
           comp_service = ComponentService(db_session)
           
           # Create application
           app_data = ApplicationCreate(name="Test App", environment="test")
           application = await app_service.create_application(app_data)
           
           # Create component
           comp_data = ComponentCreate(name="test-component", version="1.0.0")
           component = await comp_service.create_component(application.id, comp_data)
           
           # Create CPE mapping
           cpe_data = CPEMappingCreate(
               cpe_string="cpe:2.3:a:test:component:1.0.0:*:*:*:*:*:*:*",
               confidence=0.9,
               mapping_source="manual_research"
           )
           cpe_mapping = await comp_service.create_cpe_mapping(component.id, cpe_data)
           
           # Act - Delete application
           result = await app_service.delete_application(application.id)
           
           # Assert - Verify cascade deletion
           assert result is True
           
           # Verify application is soft deleted
           deleted_app = await app_service.get_application(application.id)
           assert deleted_app is None
           
           # Verify component is soft deleted
           deleted_component = await comp_service.get_component(component.id)
           assert deleted_component is None
           
           # Note: CPE mapping deletion is handled by component service cascade

CVE Service Integration
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/integration/test_cve_service_integration.py
   import pytest
   from datetime import datetime, timedelta
   from sqlalchemy.ext.asyncio import AsyncSession
   
   from src.cve_feed_service.services.cve_service import CVEService
   from src.cve_feed_service.services.application_service import ApplicationService
   from src.cve_feed_service.services.component_service import ComponentService
   from tests.fixtures.cve_data import create_test_cves_with_applicability
   
   
   @pytest.mark.integration
   class TestCVEServiceIntegration:
       """Integration tests for CVE service with real data."""
       
       async def test_get_tailored_cve_feed_with_real_data(
           self, db_session: AsyncSession
       ):
           """Test tailored CVE feed generation with real database data."""
           # Arrange - Create test data
           await create_test_cves_with_applicability(db_session)
           
           app_service = ApplicationService(db_session)
           comp_service = ComponentService(db_session)
           cve_service = CVEService(db_session)
           
           # Create application with vulnerable component
           app_data = ApplicationCreate(name="Vulnerable App", environment="prod")
           application = await app_service.create_application(app_data)
           
           # Create component with known vulnerability
           comp_data = ComponentCreate(
               name="log4j",
               version="2.14.1",  # Vulnerable version
               vendor="apache",
               component_type="library"
           )
           component = await comp_service.create_component(application.id, comp_data)
           
           # Create CPE mapping for vulnerable component
           cpe_data = CPEMappingCreate(
               cpe_string="cpe:2.3:a:apache:log4j:2.14.1:*:*:*:*:*:*:*",
               confidence=1.0,
               mapping_source="official"
           )
           await comp_service.create_cpe_mapping(component.id, cpe_data)
           
           # Act - Get tailored CVE feed
           cves, total = await cve_service.get_tailored_cve_feed(
               application.id, severity="HIGH", limit=10, offset=0
           )
           
           # Assert - Verify results
           assert total > 0
           assert len(cves) > 0
           assert all(cve.cvss_v3_severity == "HIGH" for cve in cves)
           
           # Verify CVEs are relevant to the component
           log4j_cve_found = any(
               "log4j" in cve.description.lower() for cve in cves
           )
           assert log4j_cve_found
       
       async def test_cve_feed_performance_with_large_dataset(
           self, db_session: AsyncSession
       ):
           """Test CVE feed performance with large application inventory."""
           # Arrange - Create application with many components
           app_service = ApplicationService(db_session)
           comp_service = ComponentService(db_session)
           cve_service = CVEService(db_session)
           
           app_data = ApplicationCreate(name="Large App", environment="prod")
           application = await app_service.create_application(app_data)
           
           # Create 50 components with CPE mappings
           components = []
           for i in range(50):
               comp_data = ComponentCreate(
                   name=f"component-{i}",
                   version="1.0.0",
                   vendor="test-vendor",
                   component_type="library"
               )
               component = await comp_service.create_component(application.id, comp_data)
               
               cpe_data = CPEMappingCreate(
                   cpe_string=f"cpe:2.3:a:test-vendor:component-{i}:1.0.0:*:*:*:*:*:*:*",
                   confidence=0.8,
                   mapping_source="automated_tool"
               )
               await comp_service.create_cpe_mapping(component.id, cpe_data)
               components.append(component)
           
           # Act - Measure performance
           start_time = datetime.utcnow()
           cves, total = await cve_service.get_tailored_cve_feed(application.id)
           duration = (datetime.utcnow() - start_time).total_seconds()
           
           # Assert - Performance requirements
           assert duration < 5.0  # Should complete within 5 seconds
           assert isinstance(cves, list)
           assert isinstance(total, int)

Database Integration Testing
---------------------------

Transaction Testing
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/integration/test_database_transactions.py
   import pytest
   from sqlalchemy.ext.asyncio import AsyncSession
   from sqlalchemy.exc import IntegrityError
   
   from src.cve_feed_service.services.application_service import ApplicationService
   from src.cve_feed_service.schemas.application import ApplicationCreate
   
   
   @pytest.mark.integration
   class TestDatabaseTransactions:
       """Test database transaction handling."""
       
       async def test_transaction_rollback_on_error(self, db_session: AsyncSession):
           """Test that transactions are rolled back on errors."""
           # Arrange
           app_service = ApplicationService(db_session)
           
           # Create first application
           app_data1 = ApplicationCreate(name="First App", environment="test")
           app1 = await app_service.create_application(app_data1)
           
           # Act - Try to create duplicate application (should fail)
           app_data2 = ApplicationCreate(name="First App", environment="test")
           
           with pytest.raises(ValueError):
               await app_service.create_application(app_data2)
           
           # Assert - Verify first application still exists
           retrieved_app = await app_service.get_application(app1.id)
           assert retrieved_app is not None
           assert retrieved_app.name == "First App"
       
       async def test_concurrent_application_creation(self, db_session: AsyncSession):
           """Test handling of concurrent application creation."""
           import asyncio
           
           # Arrange
           app_service1 = ApplicationService(db_session)
           app_service2 = ApplicationService(db_session)
           
           app_data = ApplicationCreate(name="Concurrent App", environment="test")
           
           # Act - Try to create same application concurrently
           async def create_app(service):
               try:
                   return await service.create_application(app_data)
               except ValueError:
                   return None
           
           results = await asyncio.gather(
               create_app(app_service1),
               create_app(app_service2),
               return_exceptions=True
           )
           
           # Assert - Only one should succeed
           successful_creations = [r for r in results if r is not None and not isinstance(r, Exception)]
           assert len(successful_creations) == 1

External API Integration
-----------------------

NVD API Integration Testing
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/integration/test_nvd_api_integration.py
   import pytest
   from datetime import datetime, timedelta
   
   from src.cve_feed_service.services.nvd_client import NVDAPIClient
   from src.cve_feed_service.services.cve_ingestion_service import CVEIngestionService
   
   
   @pytest.mark.integration
   @pytest.mark.slow
   class TestNVDAPIIntegration:
       """Integration tests with real NVD API (marked as slow)."""
       
       async def test_nvd_client_get_recent_cves(self):
           """Test retrieving recent CVEs from NVD API."""
           # Arrange
           async with NVDAPIClient() as client:
               end_date = datetime.utcnow()
               start_date = end_date - timedelta(days=1)  # Last 24 hours
               
               # Act
               cves = await client.get_cves_by_date_range(start_date, end_date)
               
               # Assert
               assert isinstance(cves, list)
               if cves:  # May be empty if no CVEs published recently
                   assert "cve" in cves[0]
                   assert "id" in cves[0]["cve"]
       
       async def test_nvd_client_rate_limiting(self):
           """Test that NVD client respects rate limits."""
           import time
           
           # Arrange
           async with NVDAPIClient() as client:
               # Act - Make multiple requests
               start_time = time.time()
               
               for _ in range(3):
                   await client.get_cves(results_per_page=1)
               
               duration = time.time() - start_time
               
               # Assert - Should take time due to rate limiting
               # With 5 requests per 30 seconds, 3 requests should take at least 12 seconds
               # But we'll be more lenient for test stability
               assert duration >= 2.0  # At least some delay
       
       @pytest.mark.skipif(
           not pytest.config.getoption("--run-external"),
           reason="External API tests disabled"
       )
       async def test_cve_ingestion_from_nvd(self, db_session):
           """Test full CVE ingestion from NVD API."""
           # Arrange
           ingestion_service = CVEIngestionService(db_session)
           
           # Act - Perform small incremental update
           count = await ingestion_service.perform_incremental_update(hours=1)
           
           # Assert
           assert count >= 0  # May be 0 if no new CVEs
           
           # If CVEs were ingested, verify they're in database
           if count > 0:
               from src.cve_feed_service.services.cve_service import CVEService
               cve_service = CVEService(db_session)
               
               cves, total = await cve_service.list_cves(limit=1)
               assert total > 0
               assert len(cves) > 0

API Endpoint Integration
-----------------------

FastAPI Integration Testing
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/integration/test_api_endpoints.py
   import pytest
   from httpx import AsyncClient
   from fastapi.testclient import TestClient
   
   from src.cve_feed_service.main import app
   from tests.fixtures.auth import create_test_user_with_api_key
   
   
   @pytest.mark.integration
   class TestAPIEndpoints:
       """Integration tests for API endpoints."""
       
       async def test_application_crud_workflow(self, db_session, test_client):
           """Test complete application CRUD workflow through API."""
           # Arrange - Create test user and API key
           user, api_key = await create_test_user_with_api_key(db_session)
           headers = {"X-API-Key": api_key}
           
           # Act & Assert - Create application
           app_data = {
               "name": "Test API App",
               "description": "Created via API",
               "environment": "test",
               "criticality": "medium"
           }
           
           response = await test_client.post(
               "/api/v1/applications",
               json=app_data,
               headers=headers
           )
           assert response.status_code == 201
           created_app = response.json()
           app_id = created_app["id"]
           
           # Act & Assert - Get application
           response = await test_client.get(
               f"/api/v1/applications/{app_id}",
               headers=headers
           )
           assert response.status_code == 200
           retrieved_app = response.json()
           assert retrieved_app["name"] == app_data["name"]
           
           # Act & Assert - Update application
           update_data = {"description": "Updated via API"}
           response = await test_client.patch(
               f"/api/v1/applications/{app_id}",
               json=update_data,
               headers=headers
           )
           assert response.status_code == 200
           updated_app = response.json()
           assert updated_app["description"] == "Updated via API"
           
           # Act & Assert - Delete application
           response = await test_client.delete(
               f"/api/v1/applications/{app_id}",
               headers=headers
           )
           assert response.status_code == 204
           
           # Verify deletion
           response = await test_client.get(
               f"/api/v1/applications/{app_id}",
               headers=headers
           )
           assert response.status_code == 404
       
       async def test_cve_feed_endpoint_integration(self, db_session, test_client):
           """Test CVE feed endpoint with real data."""
           # Arrange - Create application with components
           user, api_key = await create_test_user_with_api_key(db_session)
           headers = {"X-API-Key": api_key}
           
           # Create application
           app_data = {"name": "CVE Test App", "environment": "test"}
           response = await test_client.post(
               "/api/v1/applications",
               json=app_data,
               headers=headers
           )
           app_id = response.json()["id"]
           
           # Create component
           comp_data = {
               "name": "nginx",
               "version": "1.20.1",
               "vendor": "nginx",
               "component_type": "web_server"
           }
           response = await test_client.post(
               f"/api/v1/applications/{app_id}/components",
               json=comp_data,
               headers=headers
           )
           comp_id = response.json()["id"]
           
           # Create CPE mapping
           cpe_data = {
               "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
               "confidence": 1.0,
               "mapping_source": "official"
           }
           await test_client.post(
               f"/api/v1/components/{comp_id}/cpe-mappings",
               json=cpe_data,
               headers=headers
           )
           
           # Act - Get CVE feed
           response = await test_client.get(
               f"/api/v1/cves/feed?application_id={app_id}",
               headers=headers
           )
           
           # Assert
           assert response.status_code == 200
           feed_data = response.json()
           assert "cves" in feed_data
           assert "total" in feed_data
           assert isinstance(feed_data["cves"], list)
           assert isinstance(feed_data["total"], int)

Test Configuration
-----------------

Integration Test Fixtures
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/conftest.py
   import pytest
   import asyncio
   from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
   from sqlalchemy.pool import StaticPool
   
   from src.cve_feed_service.core.database import Base
   from src.cve_feed_service.core.config import get_settings
   
   
   @pytest.fixture(scope="session")
   def event_loop():
       """Create an instance of the default event loop for the test session."""
       loop = asyncio.get_event_loop_policy().new_event_loop()
       yield loop
       loop.close()
   
   
   @pytest.fixture(scope="session")
   async def test_engine():
       """Create test database engine."""
       settings = get_settings()
       
       # Use in-memory SQLite for fast tests
       engine = create_async_engine(
           "sqlite+aiosqlite:///:memory:",
           poolclass=StaticPool,
           connect_args={"check_same_thread": False},
           echo=False
       )
       
       # Create all tables
       async with engine.begin() as conn:
           await conn.run_sync(Base.metadata.create_all)
       
       yield engine
       
       await engine.dispose()
   
   
   @pytest.fixture
   async def db_session(test_engine):
       """Create database session for each test."""
       async with AsyncSession(test_engine) as session:
           yield session
           await session.rollback()  # Rollback any changes

Performance Benchmarks
----------------------

Integration Test Performance
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @pytest.mark.integration
   @pytest.mark.performance
   class TestIntegrationPerformance:
       """Performance tests for integration scenarios."""
       
       async def test_bulk_application_creation_performance(self, db_session):
           """Test performance of bulk application creation."""
           import time
           
           # Arrange
           app_service = ApplicationService(db_session)
           app_count = 100
           
           # Act
           start_time = time.time()
           
           for i in range(app_count):
               app_data = ApplicationCreate(
                   name=f"Bulk App {i}",
                   environment="test"
               )
               await app_service.create_application(app_data)
           
           duration = time.time() - start_time
           
           # Assert
           assert duration < 10.0  # Should create 100 apps in under 10 seconds
           
           # Verify all applications were created
           apps, total = await app_service.list_applications(limit=app_count)
           assert total == app_count

Next Steps
----------

* :doc:`ux-testing` - User experience testing approaches
* :doc:`testing-patterns` - Common testing patterns and utilities
* :doc:`continuous-integration` - CI/CD testing pipeline
* :doc:`test-configuration` - Advanced test configuration
