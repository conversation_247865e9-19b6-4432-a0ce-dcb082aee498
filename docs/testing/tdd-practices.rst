Test-Driven Development (TDD)
==============================

Test-Driven Development is a core practice in the CVE Feed Service development process. This document outlines our TDD methodology, patterns, and practical examples for implementing new features and fixing bugs.

Overview
--------

TDD is a software development approach where tests are written before the implementation code. This practice ensures that code is testable, meets requirements, and maintains high quality throughout the development lifecycle.

**Benefits of TDD**:
* **Design Quality**: Forces good design decisions upfront
* **Documentation**: Tests serve as living documentation
* **Confidence**: High test coverage provides confidence in changes
* **Debugging**: Failing tests pinpoint issues quickly
* **Refactoring**: Safe code improvements with test safety net

The TDD Cycle
-------------

Red-Green-Refactor
~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph LR
       A[🔴 Red: Write Failing Test] --> B[🟢 Green: Make Test Pass]
       B --> C[🔵 Refactor: Improve Code]
       C --> A
       
       style A fill:#ffcccc,stroke:#ff0000
       style B fill:#ccffcc,stroke:#00ff00
       style C fill:#ccccff,stroke:#0000ff

**Phase 1 - Red**: Write a failing test that describes the desired behavior
**Phase 2 - Green**: Write the minimal code to make the test pass
**Phase 3 - Refactor**: Improve the code while keeping all tests green

Detailed TDD Process
~~~~~~~~~~~~~~~~~~~

.. mermaid::

   flowchart TD
       A[Start Feature] --> B[Write Failing Test]
       B --> C[Run Test - Should Fail]
       C --> D{Test Fails?}
       D -->|No| E[Fix Test]
       E --> C
       D -->|Yes| F[Write Minimal Code]
       F --> G[Run Test]
       G --> H{Test Passes?}
       H -->|No| I[Fix Code]
       I --> G
       H -->|Yes| J[Run All Tests]
       J --> K{All Tests Pass?}
       K -->|No| L[Fix Broken Tests]
       L --> J
       K -->|Yes| M[Refactor Code]
       M --> N[Run All Tests]
       N --> O{All Tests Pass?}
       O -->|No| P[Fix Refactoring]
       P --> N
       O -->|Yes| Q{More Features?}
       Q -->|Yes| A
       Q -->|No| R[Complete]

TDD in Practice
---------------

Example 1: Application Service
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Let's walk through implementing a new feature using TDD: adding application validation.

**Step 1 - Red: Write the failing test**

.. code-block:: python

   # tests/unit/services/test_application_service.py
   import pytest
   from src.cve_feed_service.services.application_service import ApplicationService
   from src.cve_feed_service.schemas.application import ApplicationCreate
   
   
   class TestApplicationValidation:
       """Test application validation logic."""
       
       async def test_create_application_with_empty_name_raises_validation_error(
           self, application_service: ApplicationService
       ):
           """Test that creating an application with empty name raises ValidationError."""
           # Arrange
           app_data = ApplicationCreate(
               name="",  # Empty name should be invalid
               environment="production"
           )
           
           # Act & Assert
           with pytest.raises(ValueError, match="Application name cannot be empty"):
               await application_service.create_application(app_data)

**Step 2 - Run the test (should fail)**

.. code-block:: bash

   $ pytest tests/unit/services/test_application_service.py::TestApplicationValidation::test_create_application_with_empty_name_raises_validation_error -v
   
   FAILED - AssertionError: ValueError not raised

**Step 3 - Green: Write minimal code to make test pass**

.. code-block:: python

   # src/cve_feed_service/services/application_service.py
   async def create_application(self, application_data: ApplicationCreate) -> Application:
       """Create a new application with validation."""
       # Validate application name
       if not application_data.name or not application_data.name.strip():
           raise ValueError("Application name cannot be empty")
       
       # Existing implementation...
       # Check for duplicates, create entity, etc.

**Step 4 - Run the test (should pass)**

.. code-block:: bash

   $ pytest tests/unit/services/test_application_service.py::TestApplicationValidation::test_create_application_with_empty_name_raises_validation_error -v
   
   PASSED

**Step 5 - Refactor: Improve the implementation**

.. code-block:: python

   # src/cve_feed_service/services/application_service.py
   def _validate_application_data(self, application_data: ApplicationCreate) -> None:
       """Validate application data before creation."""
       if not application_data.name or not application_data.name.strip():
           raise ValueError("Application name cannot be empty")
       
       if len(application_data.name.strip()) > 255:
           raise ValueError("Application name cannot exceed 255 characters")
   
   async def create_application(self, application_data: ApplicationCreate) -> Application:
       """Create a new application with validation."""
       self._validate_application_data(application_data)
       
       # Existing implementation...

Example 2: CVE Service Feature
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Implementing severity filtering with TDD:

**Step 1 - Red: Write failing tests**

.. code-block:: python

   # tests/unit/services/test_cve_service.py
   class TestCVESeverityFiltering:
       """Test CVE severity filtering functionality."""
       
       async def test_list_cves_with_high_severity_filter_returns_only_high_severity_cves(
           self, cve_service: CVEService, sample_cves
       ):
           """Test that filtering by HIGH severity returns only HIGH severity CVEs."""
           # Arrange
           # sample_cves fixture provides CVEs with different severities
           
           # Act
           cves, total = await cve_service.list_cves(severity="HIGH")
           
           # Assert
           assert len(cves) > 0
           assert all(cve.cvss_v3_severity == "HIGH" for cve in cves)
           assert total == len([cve for cve in sample_cves if cve.cvss_v3_severity == "HIGH"])

**Step 2 - Green: Implement the feature**

.. code-block:: python

   # src/cve_feed_service/services/cve_service.py
   async def list_cves(
       self,
       severity: Optional[str] = None,
       # ... other parameters
   ) -> Tuple[List[CVE], int]:
       """List CVEs with optional severity filtering."""
       query = select(CVE).where(CVE.deleted_at.is_(None))
       
       # Apply severity filter
       if severity:
           query = query.where(CVE.cvss_v3_severity == severity)
       
       # Execute query and return results
       # ... implementation

**Step 3 - Refactor: Add validation and error handling**

.. code-block:: python

   VALID_SEVERITIES = {"LOW", "MEDIUM", "HIGH", "CRITICAL"}
   
   async def list_cves(
       self,
       severity: Optional[str] = None,
       # ... other parameters
   ) -> Tuple[List[CVE], int]:
       """List CVEs with optional severity filtering."""
       # Validate severity parameter
       if severity and severity not in VALID_SEVERITIES:
           raise ValueError(f"Invalid severity: {severity}. Must be one of {VALID_SEVERITIES}")
       
       # ... rest of implementation

TDD Best Practices
------------------

Test Naming Conventions
~~~~~~~~~~~~~~~~~~~~~~

Use descriptive test names that follow the pattern:
``test_[method_under_test]_[scenario]_[expected_result]``

.. code-block:: python

   # Good test names
   def test_create_application_with_valid_data_returns_application():
       pass
   
   def test_create_application_with_duplicate_name_raises_value_error():
       pass
   
   def test_get_application_with_nonexistent_id_returns_none():
       pass
   
   # Poor test names
   def test_create_application():  # Too vague
       pass
   
   def test_application_creation_failure():  # Unclear scenario
       pass

Test Structure (Given-When-Then)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Structure tests using the Given-When-Then pattern (also known as Arrange-Act-Assert):

.. code-block:: python

   async def test_delete_application_with_components_cascades_deletion():
       """Test that deleting an application also deletes its components."""
       # Given (Arrange)
       app = await create_test_application()
       component1 = await create_test_component(app.id)
       component2 = await create_test_component(app.id)
       
       # When (Act)
       result = await application_service.delete_application(app.id)
       
       # Then (Assert)
       assert result is True
       
       # Verify application is deleted
       deleted_app = await application_service.get_application(app.id)
       assert deleted_app is None
       
       # Verify components are deleted
       deleted_comp1 = await component_service.get_component(component1.id)
       deleted_comp2 = await component_service.get_component(component2.id)
       assert deleted_comp1 is None
       assert deleted_comp2 is None

Start with the Simplest Test
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Begin with the simplest possible test case:

.. code-block:: python

   # Start simple
   def test_create_application_with_minimal_data():
       """Test creating an application with only required fields."""
       app_data = ApplicationCreate(name="Test App")
       result = await application_service.create_application(app_data)
       assert result.name == "Test App"
   
   # Then add complexity
   def test_create_application_with_all_fields():
       """Test creating an application with all optional fields."""
       app_data = ApplicationCreate(
           name="Test App",
           description="Test Description",
           version="1.0.0",
           owner="Test Owner",
           environment="production",
           criticality="high"
       )
       result = await application_service.create_application(app_data)
       assert result.name == "Test App"
       assert result.description == "Test Description"
       # ... assert all fields

Test One Thing at a Time
~~~~~~~~~~~~~~~~~~~~~~~~

Each test should verify one specific behavior:

.. code-block:: python

   # Good: Tests one specific behavior
   async def test_create_application_with_duplicate_name_raises_value_error():
       """Test that duplicate application names raise ValueError."""
       # Create first application
       app_data = ApplicationCreate(name="Duplicate App")
       await application_service.create_application(app_data)
       
       # Attempt to create duplicate
       with pytest.raises(ValueError, match="already exists"):
           await application_service.create_application(app_data)
   
   # Avoid: Testing multiple behaviors in one test
   async def test_application_creation_and_retrieval_and_deletion():
       """This test does too many things."""
       # Creates, retrieves, and deletes - should be separate tests
       pass

TDD for Bug Fixes
-----------------

When fixing bugs, use TDD to ensure the bug doesn't regress:

**Step 1: Write a test that reproduces the bug**

.. code-block:: python

   async def test_get_tailored_cve_feed_with_no_components_returns_empty_list():
       """Test that applications with no components return empty CVE feed."""
       # This test reproduces a bug where the service crashed
       # when an application had no components
       
       # Create application without components
       app = await create_test_application()
       
       # Should not crash and should return empty list
       cves, total = await cve_service.get_tailored_cve_feed(app.id)
       
       assert cves == []
       assert total == 0

**Step 2: Verify the test fails (reproduces the bug)**

**Step 3: Fix the bug**

.. code-block:: python

   async def get_tailored_cve_feed(self, application_id: UUID) -> Tuple[List[CVE], int]:
       """Get tailored CVE feed for an application."""
       app = await self.application_service.get_application(
           application_id, include_components=True
       )
       
       if not app:
           raise ValueError(f"Application {application_id} not found")
       
       # Bug fix: Handle case where application has no components
       if not app.components:
           return [], 0
       
       # ... rest of implementation

**Step 4: Verify the test passes**

TDD with Async Code
------------------

Special considerations for async TDD:

.. code-block:: python

   import pytest
   import asyncio
   from unittest.mock import AsyncMock
   
   
   class TestAsyncService:
       """Example of TDD with async services."""
       
       @pytest.mark.asyncio
       async def test_async_operation_with_mock_dependency():
           """Test async operation with mocked dependencies."""
           # Arrange
           mock_db = AsyncMock()
           mock_db.execute.return_value.scalar_one_or_none.return_value = None
           
           service = ApplicationService(mock_db)
           
           # Act
           result = await service.get_application(UUID("12345678-1234-5678-9012-123456789012"))
           
           # Assert
           assert result is None
           mock_db.execute.assert_called_once()
       
       @pytest.mark.asyncio
       async def test_async_operation_with_timeout():
           """Test that async operations handle timeouts properly."""
           # Arrange
           slow_service = SlowExternalService()
           
           # Act & Assert
           with pytest.raises(asyncio.TimeoutError):
               await asyncio.wait_for(slow_service.slow_operation(), timeout=1.0)

TDD Metrics and Monitoring
--------------------------

Track TDD effectiveness with metrics:

**Code Coverage**
* Aim for >90% line coverage
* Focus on branch coverage for complex logic
* Use coverage reports to identify untested code

**Test Quality Metrics**
* Test-to-code ratio (aim for 1:1 or higher)
* Test execution time (keep unit tests fast)
* Test failure rate (should be low in CI)

**Development Metrics**
* Time from test to implementation
* Defect detection rate
* Refactoring frequency

Common TDD Pitfalls
------------------

Avoid These Anti-Patterns
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Anti-pattern: Testing implementation details
   def test_create_application_calls_database_add():
       """Don't test that specific methods are called."""
       # This test is brittle and tests implementation, not behavior
       mock_db.add.assert_called_once()
   
   # Better: Test behavior
   def test_create_application_persists_application():
       """Test that the application is actually persisted."""
       app = await application_service.create_application(app_data)
       retrieved_app = await application_service.get_application(app.id)
       assert retrieved_app is not None

   # Anti-pattern: Overly complex tests
   def test_complex_workflow_with_many_steps():
       """This test is doing too much."""
       # 50 lines of setup and assertions
       # Break this into smaller, focused tests
   
   # Anti-pattern: Tests that don't fail when they should
   def test_that_always_passes():
       """This test doesn't actually test anything."""
       assert True  # This test is useless

TDD Tools and Utilities
----------------------

Useful Testing Tools
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Test data factories
   import factory
   from factory import Faker
   
   class ApplicationFactory(factory.Factory):
       class Meta:
           model = ApplicationCreate
       
       name = Faker('company')
       environment = Faker('random_element', elements=['dev', 'staging', 'prod'])
       criticality = Faker('random_element', elements=['low', 'medium', 'high'])
   
   # Property-based testing
   from hypothesis import given, strategies as st
   
   @given(st.text(min_size=1, max_size=255))
   async def test_create_application_with_any_valid_name(name):
       """Test application creation with any valid name."""
       app_data = ApplicationCreate(name=name)
       result = await application_service.create_application(app_data)
       assert result.name == name

Custom Test Utilities
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # tests/utils.py
   async def create_test_application(**kwargs) -> Application:
       """Create a test application with default values."""
       defaults = {
           'name': f'Test App {uuid4()}',
           'environment': 'test',
           'criticality': 'medium'
       }
       defaults.update(kwargs)
       
       app_data = ApplicationCreate(**defaults)
       return await application_service.create_application(app_data)
   
   def assert_cve_structure(cve_data: dict) -> None:
       """Assert that CVE data has the expected structure."""
       required_fields = ['cve_id', 'description', 'published_date']
       for field in required_fields:
           assert field in cve_data, f"Missing required field: {field}"

Next Steps
----------

* :doc:`unit-testing` - Detailed unit testing patterns
* :doc:`integration-testing` - Integration testing strategies
* :doc:`testing-patterns` - Common testing patterns and utilities
* :doc:`continuous-integration` - CI/CD integration for TDD
