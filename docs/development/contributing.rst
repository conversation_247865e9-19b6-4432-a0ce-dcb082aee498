Contributing Guidelines
=======================

Welcome to the CVE Feed Service project! This guide covers everything you need to know about contributing to the project, from setting up your development environment to submitting pull requests.

Getting Started
--------------

Prerequisites
~~~~~~~~~~~~

Before contributing, ensure you have:

* **Technical Skills**: Python 3.11+, FastAPI, SQLAlchemy, PostgreSQL
* **Development Tools**: Git, Docker (optional), code editor
* **Understanding**: Familiarity with CVE/vulnerability management concepts
* **Communication**: Ability to collaborate effectively in English

**Recommended Reading**:
* :doc:`setup` - Development environment setup
* :doc:`architecture` - System architecture overview
* :doc:`../services/index` - Service layer documentation
* :doc:`../api-reference/index` - API reference

First-Time Setup
~~~~~~~~~~~~~~~

.. code-block:: bash

   # 1. Fork the repository on GitHub
   # 2. Clone your fork
   git clone https://github.com/YOUR_USERNAME/day3-cve-feed.git
   cd day3-cve-feed
   
   # 3. Add upstream remote
   git remote add upstream https://github.com/forkrul/day3-cve-feed.git
   
   # 4. Set up development environment
   nix-shell  # or manual setup as per setup.rst
   
   # 5. Create development database
   createdb cve_feed_dev
   alembic upgrade head
   
   # 6. Run tests to verify setup
   pytest
   
   # 7. Install pre-commit hooks
   pre-commit install

Finding Ways to Contribute
~~~~~~~~~~~~~~~~~~~~~~~~~

**Good First Issues**:
* Documentation improvements
* Adding test cases
* Fixing typos or formatting
* Small bug fixes
* Code cleanup and refactoring

**Areas for Contribution**:
* **Bug Fixes**: Resolve reported issues
* **Features**: Implement new functionality
* **Performance**: Optimize queries and algorithms
* **Security**: Enhance security measures
* **Documentation**: Improve guides and API docs
* **Testing**: Expand test coverage
* **DevOps**: Improve CI/CD and deployment

**Finding Issues**:
* Check GitHub Issues with labels: ``good first issue``, ``help wanted``
* Look for ``TODO`` comments in the codebase
* Review open pull requests for collaboration opportunities
* Suggest improvements based on your experience

Development Workflow
-------------------

Branch Strategy
~~~~~~~~~~~~~~

We use Git Flow with the following branches:

* **master**: Production-ready code
* **develop**: Integration branch for features
* **feature/\***: Feature development branches
* **hotfix/\***: Critical bug fixes
* **release/\***: Release preparation branches

**Branch Naming Conventions**:

.. code-block:: text

   feature/add-cpe-validation
   feature/improve-nvd-client-performance
   bugfix/fix-authentication-token-expiry
   hotfix/critical-sql-injection-fix
   docs/update-api-documentation

Workflow Steps
~~~~~~~~~~~~~

**1. Create Feature Branch**:

.. code-block:: bash

   # Sync with upstream
   git checkout develop
   git pull upstream develop
   
   # Create feature branch
   git checkout -b feature/your-feature-name
   
   # Push branch to your fork
   git push -u origin feature/your-feature-name

**2. Development Process**:

.. code-block:: bash

   # Make changes following TDD
   # 1. Write failing test
   # 2. Implement minimal code
   # 3. Refactor
   
   # Run tests frequently
   pytest -m unit
   
   # Check code quality
   pre-commit run --all-files

**3. Commit Guidelines**:

Follow Conventional Commits format:

.. code-block:: text

   <type>[optional scope]: <description>
   
   [optional body]
   
   [optional footer(s)]

**Commit Types**:
* ``feat``: New feature
* ``fix``: Bug fix
* ``docs``: Documentation changes
* ``style``: Code style changes (formatting, etc.)
* ``refactor``: Code refactoring
* ``test``: Adding or updating tests
* ``chore``: Maintenance tasks

**Examples**:

.. code-block:: text

   feat(api): add CPE validation endpoint
   
   Add new endpoint for validating CPE 2.3 format strings.
   Includes comprehensive validation rules and error handling.
   
   Closes #123

   fix(auth): resolve JWT token expiration issue
   
   Fixed bug where JWT tokens were not properly validated
   for expiration time, causing security vulnerability.
   
   Fixes #456

**4. Pull Request Process**:

.. code-block:: bash

   # Push changes
   git push origin feature/your-feature-name
   
   # Create pull request on GitHub
   # Fill out PR template
   # Request review from maintainers

Code Standards
-------------

Python Style Guide
~~~~~~~~~~~~~~~~~

**PEP 8 Compliance**:
* Line length: 100 characters
* Use 4 spaces for indentation
* Follow naming conventions
* Add docstrings to all public functions

**Type Hints**:

.. code-block:: python

   from typing import List, Optional, Dict, Any
   from uuid import UUID
   
   
   async def get_application_vulnerabilities(
       db: AsyncSession,
       application_id: UUID,
       severity: Optional[str] = None,
       limit: int = 100,
   ) -> List[CVE]:
       """Get vulnerabilities for a specific application.
       
       Args:
           db: Database session
           application_id: Application UUID
           severity: Optional severity filter (LOW, MEDIUM, HIGH, CRITICAL)
           limit: Maximum number of results to return
           
       Returns:
           List of CVE objects matching the criteria
           
       Raises:
           ValueError: If application not found
           ValidationError: If severity value is invalid
       """
       pass

**Docstring Format**:

.. code-block:: python

   def validate_cpe_format(cpe_string: str) -> bool:
       """Validate CPE 2.3 format string.
       
       Validates that the provided string follows the CPE 2.3 specification
       with exactly 13 colon-separated components.
       
       Args:
           cpe_string: The CPE string to validate
           
       Returns:
           True if valid CPE 2.3 format, False otherwise
           
       Example:
           >>> validate_cpe_format("cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*")
           True
           >>> validate_cpe_format("invalid_cpe")
           False
       """
       pass

**Error Handling**:

.. code-block:: python

   import structlog
   
   logger = structlog.get_logger(__name__)
   
   
   async def create_application(self, data: ApplicationCreate) -> Application:
       """Create a new application."""
       try:
           # Validate business rules
           await self._validate_application_data(data)
           
           # Create application
           application = Application(**data.model_dump())
           self.db.add(application)
           await self.db.commit()
           await self.db.refresh(application)
           
           logger.info("Application created", application_id=application.id)
           return application
           
       except ValueError as e:
           logger.warning("Application creation failed", error=str(e))
           raise
       except Exception as e:
           logger.error("Unexpected error creating application", error=str(e))
           await self.db.rollback()
           raise

Code Quality Tools
~~~~~~~~~~~~~~~~~

**Required Tools**:

.. code-block:: bash

   # Code formatting
   black src/ tests/
   
   # Import sorting
   isort src/ tests/
   
   # Linting
   flake8 src/ tests/
   
   # Type checking
   mypy src/
   
   # Security scanning
   bandit -r src/

**Pre-commit Configuration**:

.. code-block:: yaml

   # .pre-commit-config.yaml
   repos:
     - repo: https://github.com/psf/black
       rev: 23.1.0
       hooks:
         - id: black
           language_version: python3.11
   
     - repo: https://github.com/pycqa/isort
       rev: 5.12.0
       hooks:
         - id: isort
           args: ["--profile", "black"]
   
     - repo: https://github.com/pycqa/flake8
       rev: 6.0.0
       hooks:
         - id: flake8
           args: ["--max-line-length=100"]
   
     - repo: https://github.com/pre-commit/mirrors-mypy
       rev: v1.0.1
       hooks:
         - id: mypy
           additional_dependencies: [types-all]

Testing Requirements
~~~~~~~~~~~~~~~~~~~

**Test Coverage**:
* Minimum 90% overall coverage
* 95% coverage for service layer
* All new features must include tests
* Bug fixes must include regression tests

**Test Categories**:

.. code-block:: python

   # Unit tests (fast, isolated)
   @pytest.mark.unit
   async def test_application_service_create():
       pass
   
   # Integration tests (database required)
   @pytest.mark.integration
   async def test_application_crud_operations():
       pass
   
   # End-to-end tests (full system)
   @pytest.mark.e2e
   async def test_complete_vulnerability_workflow():
       pass

**Test Structure**:

.. code-block:: python

   class TestApplicationService:
       """Test suite for ApplicationService."""
       
       @pytest.fixture
       def application_service(self, db_session):
           """ApplicationService instance for testing."""
           return ApplicationService(db_session)
       
       async def test_create_application_with_valid_data_returns_application(
           self, application_service
       ):
           """Test that creating application with valid data returns application."""
           # Arrange
           app_data = ApplicationCreate(name="Test App", environment="test")
           
           # Act
           result = await application_service.create_application(app_data)
           
           # Assert
           assert result.name == "Test App"
           assert result.environment == "test"
           assert result.id is not None

Documentation Standards
~~~~~~~~~~~~~~~~~~~~~~

**API Documentation**:
* All endpoints must have comprehensive docstrings
* Include request/response examples
* Document error conditions
* Update OpenAPI schemas

**Code Documentation**:
* Public functions require docstrings
* Complex algorithms need inline comments
* Business logic should be self-documenting

**User Documentation**:
* Update relevant user guides
* Include migration instructions for breaking changes
* Provide examples for new features

Pull Request Guidelines
----------------------

PR Template
~~~~~~~~~~

When creating a pull request, use this template:

.. code-block:: markdown

   ## Description
   Brief description of changes and motivation.
   
   ## Type of Change
   - [ ] Bug fix (non-breaking change that fixes an issue)
   - [ ] New feature (non-breaking change that adds functionality)
   - [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
   - [ ] Documentation update
   
   ## Testing
   - [ ] Unit tests pass
   - [ ] Integration tests pass
   - [ ] New tests added for new functionality
   - [ ] Manual testing completed
   
   ## Checklist
   - [ ] Code follows style guidelines
   - [ ] Self-review completed
   - [ ] Documentation updated
   - [ ] No new warnings introduced
   
   ## Related Issues
   Closes #123
   Related to #456

Review Process
~~~~~~~~~~~~~

**Review Criteria**:
* Code quality and style compliance
* Test coverage and quality
* Documentation completeness
* Performance impact
* Security considerations
* Backward compatibility

**Review Timeline**:
* Initial review within 2 business days
* Follow-up reviews within 1 business day
* Approval required from at least one maintainer
* All CI checks must pass

**Addressing Feedback**:

.. code-block:: bash

   # Make requested changes
   git add .
   git commit -m "address review feedback: improve error handling"
   git push origin feature/your-feature-name
   
   # Respond to review comments
   # Mark conversations as resolved when addressed

Security Considerations
----------------------

Security Guidelines
~~~~~~~~~~~~~~~~~~

**Input Validation**:
* Validate all user inputs with Pydantic
* Sanitize data before database operations
* Use parameterized queries
* Implement rate limiting

**Authentication & Authorization**:
* Never commit secrets or API keys
* Use environment variables for configuration
* Implement proper role-based access control
* Validate JWT tokens properly

**Data Protection**:
* Hash passwords with bcrypt
* Use HTTPS in production
* Implement proper CORS policies
* Log security events

**Vulnerability Reporting**:
* Report security issues privately to maintainers
* Do not create public issues for security vulnerabilities
* Allow reasonable time for fixes before disclosure

Performance Guidelines
---------------------

Database Performance
~~~~~~~~~~~~~~~~~~~

**Query Optimization**:
* Use appropriate indexes
* Implement pagination for large datasets
* Use eager loading to avoid N+1 queries
* Monitor query performance

**Example Optimized Query**:

.. code-block:: python

   # Good: Eager loading with selectinload
   async def get_application_with_components(self, app_id: UUID):
       query = select(Application).where(Application.id == app_id).options(
           selectinload(Application.components).selectinload(Component.cpe_mappings)
       )
       result = await self.db.execute(query)
       return result.scalar_one_or_none()
   
   # Bad: N+1 query problem
   async def get_application_with_components_bad(self, app_id: UUID):
       app = await self.get_application(app_id)
       for component in app.components:  # This triggers N queries
           component.cpe_mappings  # This triggers N more queries
       return app

API Performance
~~~~~~~~~~~~~~

**Response Optimization**:
* Implement response caching where appropriate
* Use async/await throughout
* Optimize serialization
* Monitor response times

**Memory Management**:
* Process large datasets in batches
* Use streaming for large responses
* Monitor memory usage
* Implement proper connection pooling

Community Guidelines
-------------------

Code of Conduct
~~~~~~~~~~~~~~

**Our Standards**:
* Be respectful and inclusive
* Welcome newcomers and help them learn
* Provide constructive feedback
* Focus on what's best for the community
* Show empathy towards other community members

**Unacceptable Behavior**:
* Harassment or discriminatory language
* Personal attacks or trolling
* Publishing private information
* Spam or off-topic discussions

Communication
~~~~~~~~~~~~

**Channels**:
* GitHub Issues: Bug reports and feature requests
* GitHub Discussions: General questions and ideas
* Pull Request Comments: Code review discussions
* Email: Security issues and private matters

**Best Practices**:
* Search existing issues before creating new ones
* Provide clear, detailed descriptions
* Include steps to reproduce for bugs
* Be patient and respectful in discussions

Getting Help
-----------

**Resources**:
* :doc:`setup` - Development environment setup
* :doc:`architecture` - System architecture
* :doc:`testing` - Testing guidelines
* :doc:`../api-reference/index` - API documentation

**Support Channels**:
* GitHub Issues: Technical questions
* GitHub Discussions: General questions
* Documentation: Comprehensive guides

**Mentorship**:
* New contributors are welcome to ask for guidance
* Maintainers are available to help with complex issues
* Pair programming sessions can be arranged for major features

Recognition
----------

**Contributors**:
* All contributors are recognized in the project
* Significant contributions are highlighted in release notes
* Regular contributors may be invited to become maintainers

**Types of Recognition**:
* Contributor list in README
* Release notes mentions
* Social media acknowledgments
* Conference presentation opportunities

Thank you for contributing to the CVE Feed Service! Your efforts help make vulnerability management more accessible and effective for everyone.

Next Steps
----------

* :doc:`setup` - Set up your development environment
* :doc:`architecture` - Understand the system architecture
* :doc:`testing` - Learn about testing practices
* :doc:`../testing/tdd-practices` - Master test-driven development
