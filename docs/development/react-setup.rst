React Frontend Setup
====================

This guide covers the complete setup and development of the React frontend for the CVE Feed Service, implementing a dark-mode-first interface with comprehensive testing coverage.

Overview
--------

The React frontend provides a modern, responsive web interface for the CVE Feed Service with the following key features:

* **Dark-mode-first design** with Tailwind CSS
* **TypeScript** for type safety and better developer experience
* **Redux Toolkit + RTK Query** for state management and API integration
* **Comprehensive testing** with <PERSON><PERSON> (E2E) and Jest (Unit)
* **Accessibility-first** approach with WCAG 2.1 AA compliance
* **Performance optimized** with Vite build system

🏗️ **React Architecture Overview**
==================================

.. mermaid::

   graph TB
       subgraph "User Interface Layer"
           PAGES[Pages & Routes]
           COMPONENTS[Reusable Components]
           LAYOUTS[Layout Components]
           FORMS[Form Components]
       end

       subgraph "State Management Layer"
           STORE[Redux Store]
           RTK[RTK Query APIs]
           SLICES[Redux Slices]
           HOOKS[Custom Hooks]
       end

       subgraph "Service Layer"
           API[API Client]
           AUTH[Auth Service]
           STORAGE[Local Storage]
           UTILS[Utility Functions]
       end

       subgraph "Backend Integration"
           REST[REST API Endpoints]
           WEBSOCKET[WebSocket Connection]
           NOTIFICATIONS[Push Notifications]
       end

       PAGES --> COMPONENTS
       PAGES --> LAYOUTS
       COMPONENTS --> FORMS

       PAGES --> STORE
       COMPONENTS --> RTK
       HOOKS --> SLICES
       STORE --> RTK

       RTK --> API
       HOOKS --> AUTH
       AUTH --> STORAGE
       API --> UTILS

       API --> REST
       API --> WEBSOCKET
       API --> NOTIFICATIONS

       style PAGES fill:#e1f5fe
       style COMPONENTS fill:#f3e5f5
       style STORE fill:#e8f5e8
       style API fill:#fff3e0

📱 **User Flow Architecture**
============================

.. mermaid::

   sequenceDiagram
       participant User
       participant React App
       participant Redux Store
       participant RTK Query
       participant Backend API
       participant Database

       User->>React App: Navigate to Dashboard
       React App->>Redux Store: Check auth state
       Redux Store-->>React App: User authenticated

       React App->>RTK Query: Fetch dashboard data
       RTK Query->>Backend API: GET /api/v1/dashboard
       Backend API->>Database: Query aggregated data
       Database-->>Backend API: Return metrics
       Backend API-->>RTK Query: Dashboard response
       RTK Query->>Redux Store: Cache response
       Redux Store-->>React App: Update UI state
       React App-->>User: Display dashboard

       User->>React App: Filter vulnerabilities
       React App->>RTK Query: Fetch filtered CVEs
       RTK Query->>Backend API: GET /api/v1/cves?severity=critical
       Backend API->>Database: Query filtered CVEs
       Database-->>Backend API: Return CVE list
       Backend API-->>RTK Query: CVE response
       RTK Query->>Redux Store: Update cache
       Redux Store-->>React App: Re-render components
       React App-->>User: Show filtered results

Technology Stack
----------------

**Core Technologies:**
* React 18+ with TypeScript
* Vite for build tooling and development server
* Tailwind CSS for styling with dark mode support
* Redux Toolkit for state management
* RTK Query for API data fetching
* React Router v6 for routing

**Testing Framework:**
* Playwright for end-to-end testing
* Jest + React Testing Library for unit testing
* MSW (Mock Service Worker) for API mocking
* Chromatic for visual regression testing

**Development Tools:**
* ESLint + Prettier for code quality
* Husky for git hooks
* TypeScript for type checking
* Storybook for component development

Project Structure
-----------------

.. code-block:: text

   frontend/
   ├── public/                        # Static assets
   │   ├── favicon.ico
   │   └── index.html
   ├── src/
   │   ├── components/                # Reusable UI components
   │   │   ├── common/                # Generic components
   │   │   │   ├── Button/
   │   │   │   ├── Modal/
   │   │   │   ├── Table/
   │   │   │   └── LoadingSpinner/
   │   │   ├── forms/                 # Form components
   │   │   │   ├── LoginForm/
   │   │   │   ├── ApplicationForm/
   │   │   │   └── ComponentForm/
   │   │   └── layout/                # Layout components
   │   │       ├── Header/
   │   │       ├── Sidebar/
   │   │       └── Footer/
   │   ├── pages/                     # Page components
   │   │   ├── Dashboard/
   │   │   ├── Applications/
   │   │   ├── CVEs/
   │   │   ├── Components/
   │   │   └── Auth/
   │   ├── hooks/                     # Custom React hooks
   │   │   ├── useAuth.ts
   │   │   ├── useLocalStorage.ts
   │   │   └── useDebounce.ts
   │   ├── store/                     # Redux store configuration
   │   │   ├── index.ts               # Store setup
   │   │   ├── slices/                # Redux slices
   │   │   │   ├── authSlice.ts
   │   │   │   ├── applicationsSlice.ts
   │   │   │   ├── cvesSlice.ts
   │   │   │   └── uiSlice.ts
   │   │   └── api/                   # RTK Query API definitions
   │   │       ├── authApi.ts
   │   │       ├── applicationsApi.ts
   │   │       ├── cvesApi.ts
   │   │       └── componentsApi.ts
   │   ├── types/                     # TypeScript type definitions
   │   │   ├── api.ts                 # API response types
   │   │   ├── auth.ts                # Authentication types
   │   │   ├── application.ts         # Application types
   │   │   └── cve.ts                 # CVE types
   │   ├── utils/                     # Utility functions
   │   │   ├── api.ts                 # API utilities
   │   │   ├── auth.ts                # Auth utilities
   │   │   ├── formatting.ts          # Data formatting
   │   │   └── validation.ts          # Form validation
   │   ├── styles/                    # Global styles
   │   │   ├── globals.css            # Global CSS
   │   │   └── components.css         # Component-specific styles
   │   ├── config/                    # Configuration files
   │   │   ├── environment.ts         # Environment variables
   │   │   └── constants.ts           # Application constants
   │   ├── App.tsx                    # Main App component
   │   ├── main.tsx                   # Application entry point
   │   └── vite-env.d.ts             # Vite type definitions
   ├── tests/                         # Test files
   │   ├── e2e/                       # Playwright E2E tests
   │   │   ├── auth.spec.ts
   │   │   ├── dashboard.spec.ts
   │   │   ├── applications.spec.ts
   │   │   └── cves.spec.ts
   │   ├── unit/                      # Jest unit tests
   │   │   ├── components/
   │   │   ├── hooks/
   │   │   ├── utils/
   │   │   └── store/
   │   ├── fixtures/                  # Test data fixtures
   │   │   ├── applications.ts
   │   │   ├── cves.ts
   │   │   └── users.ts
   │   └── setup/                     # Test setup files
   │       ├── jest.setup.ts
   │       ├── playwright.config.ts
   │       └── msw.setup.ts
   ├── .storybook/                    # Storybook configuration
   ├── playwright.config.ts           # Playwright configuration
   ├── jest.config.js                 # Jest configuration
   ├── tailwind.config.js             # Tailwind CSS configuration
   ├── tsconfig.json                  # TypeScript configuration
   ├── vite.config.ts                 # Vite configuration
   ├── package.json                   # Dependencies and scripts
   └── README.md                      # Frontend documentation

Initial Setup
-------------

**Prerequisites:**
* Node.js 18+ and npm/yarn
* Backend CVE Feed Service running
* Git for version control

**Installation Steps:**

.. code-block:: bash

   # Create React project with Vite
   npm create vite@latest frontend -- --template react-ts
   cd frontend

   # Install core dependencies
   npm install @reduxjs/toolkit react-redux react-router-dom
   npm install @headlessui/react @heroicons/react
   npm install axios react-hook-form @hookform/resolvers yup
   npm install date-fns clsx tailwind-merge

   # Install development dependencies
   npm install -D tailwindcss postcss autoprefixer
   npm install -D @types/node @types/react @types/react-dom
   npm install -D eslint @typescript-eslint/eslint-plugin
   npm install -D prettier eslint-config-prettier
   npm install -D husky lint-staged
   npm install -D @testing-library/react @testing-library/jest-dom
   npm install -D @testing-library/user-event jest-environment-jsdom
   npm install -D @playwright/test
   npm install -D msw @storybook/react-vite

   # Initialize Tailwind CSS
   npx tailwindcss init -p

   # Setup Playwright
   npx playwright install

**Configuration Files:**

.. code-block:: typescript

   // vite.config.ts
   import { defineConfig } from 'vite'
   import react from '@vitejs/plugin-react'
   import path from 'path'

   export default defineConfig({
     plugins: [react()],
     resolve: {
       alias: {
         '@': path.resolve(__dirname, './src'),
       },
     },
     server: {
       port: 3000,
       proxy: {
         '/api': {
           target: 'http://localhost:8000',
           changeOrigin: true,
         },
       },
     },
   })

.. code-block:: javascript

   // tailwind.config.js
   /** @type {import('tailwindcss').Config} */
   export default {
     content: [
       "./index.html",
       "./src/**/*.{js,ts,jsx,tsx}",
     ],
     darkMode: 'class',
     theme: {
       extend: {
         colors: {
           primary: {
             50: '#f0f9ff',
             500: '#06b6d4',
             600: '#0891b2',
             700: '#0e7490',
             900: '#164e63',
           },
           gray: {
             50: '#f8fafc',
             800: '#1e293b',
             900: '#0f172a',
           },
           severity: {
             critical: '#dc2626',
             high: '#ea580c',
             medium: '#ca8a04',
             low: '#16a34a',
           },
         },
         fontFamily: {
           sans: ['Inter', 'system-ui', 'sans-serif'],
           mono: ['JetBrains Mono', 'monospace'],
         },
       },
     },
     plugins: [],
   }

.. code-block:: typescript

   // src/config/environment.ts
   export const config = {
     apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1',
     enableDevTools: import.meta.env.DEV,
     sentryDsn: import.meta.env.VITE_SENTRY_DSN,
     version: import.meta.env.VITE_APP_VERSION || '1.0.0',
   } as const;

Development Workflow
--------------------

**Starting Development:**

.. code-block:: bash

   # Start development server
   npm run dev

   # Run tests in watch mode
   npm run test:watch

   # Run E2E tests
   npm run test:e2e

   # Build for production
   npm run build

   # Preview production build
   npm run preview

**Code Quality:**

.. code-block:: bash

   # Lint code
   npm run lint

   # Format code
   npm run format

   # Type check
   npm run type-check

   # Run all quality checks
   npm run quality-check

**Package.json Scripts:**

.. code-block:: json

   {
     "scripts": {
       "dev": "vite",
       "build": "tsc && vite build",
       "preview": "vite preview",
       "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
       "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"",
       "type-check": "tsc --noEmit",
       "test": "jest",
       "test:watch": "jest --watch",
       "test:coverage": "jest --coverage",
       "test:e2e": "playwright test",
       "test:e2e:ui": "playwright test --ui",
       "quality-check": "npm run type-check && npm run lint && npm run test",
       "storybook": "storybook dev -p 6006",
       "build-storybook": "storybook build"
     }
   }

State Management Setup
----------------------

**Redux Store Configuration:**

.. code-block:: typescript

   // src/store/index.ts
   import { configureStore } from '@reduxjs/toolkit';
   import { setupListeners } from '@reduxjs/toolkit/query';
   import { authApi } from './api/authApi';
   import { applicationsApi } from './api/applicationsApi';
   import { cvesApi } from './api/cvesApi';
   import authSlice from './slices/authSlice';
   import uiSlice from './slices/uiSlice';

   export const store = configureStore({
     reducer: {
       auth: authSlice,
       ui: uiSlice,
       [authApi.reducerPath]: authApi.reducer,
       [applicationsApi.reducerPath]: applicationsApi.reducer,
       [cvesApi.reducerPath]: cvesApi.reducer,
     },
     middleware: (getDefaultMiddleware) =>
       getDefaultMiddleware().concat(
         authApi.middleware,
         applicationsApi.middleware,
         cvesApi.middleware
       ),
   });

   setupListeners(store.dispatch);

   export type RootState = ReturnType<typeof store.getState>;
   export type AppDispatch = typeof store.dispatch;

**RTK Query API Setup:**

.. code-block:: typescript

   // src/store/api/authApi.ts
   import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
   import { config } from '@/config/environment';
   import type { LoginRequest, TokenResponse, User } from '@/types/auth';

   export const authApi = createApi({
     reducerPath: 'authApi',
     baseQuery: fetchBaseQuery({
       baseUrl: `${config.apiBaseUrl}/auth`,
       prepareHeaders: (headers, { getState }) => {
         const token = (getState() as any).auth.token;
         if (token) {
           headers.set('authorization', `Bearer ${token}`);
         }
         return headers;
       },
     }),
     tagTypes: ['User'],
     endpoints: (builder) => ({
       login: builder.mutation<TokenResponse, LoginRequest>({
         query: (credentials) => ({
           url: '/login',
           method: 'POST',
           body: credentials,
         }),
       }),
       getCurrentUser: builder.query<User, void>({
         query: () => '/me',
         providesTags: ['User'],
       }),
       logout: builder.mutation<void, void>({
         query: () => ({
           url: '/logout',
           method: 'POST',
         }),
       }),
     }),
   });

   export const { useLoginMutation, useGetCurrentUserQuery, useLogoutMutation } = authApi;

Testing Configuration
---------------------

**Jest Setup:**

.. code-block:: javascript

   // jest.config.js
   export default {
     testEnvironment: 'jsdom',
     setupFilesAfterEnv: ['<rootDir>/tests/setup/jest.setup.ts'],
     moduleNameMapping: {
       '^@/(.*)$': '<rootDir>/src/$1',
       '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
     },
     collectCoverageFrom: [
       'src/**/*.{ts,tsx}',
       '!src/**/*.d.ts',
       '!src/main.tsx',
       '!src/vite-env.d.ts',
     ],
     coverageThreshold: {
       global: {
         branches: 80,
         functions: 80,
         lines: 80,
         statements: 80,
       },
     },
   };

**Playwright Configuration:**

.. code-block:: typescript

   // playwright.config.ts
   import { defineConfig, devices } from '@playwright/test';

   export default defineConfig({
     testDir: './tests/e2e',
     fullyParallel: true,
     forbidOnly: !!process.env.CI,
     retries: process.env.CI ? 2 : 0,
     workers: process.env.CI ? 1 : undefined,
     reporter: 'html',
     use: {
       baseURL: 'http://localhost:3000',
       trace: 'on-first-retry',
     },
     projects: [
       {
         name: 'chromium',
         use: { ...devices['Desktop Chrome'] },
       },
       {
         name: 'firefox',
         use: { ...devices['Desktop Firefox'] },
       },
       {
         name: 'webkit',
         use: { ...devices['Desktop Safari'] },
       },
     ],
     webServer: {
       command: 'npm run dev',
       url: 'http://localhost:3000',
       reuseExistingServer: !process.env.CI,
     },
   });

Next Steps
----------

1. **Component Development**: Start with basic layout components (Header, Sidebar, Footer)
2. **Authentication Flow**: Implement login/logout functionality
3. **Dashboard Implementation**: Create the main dashboard with metrics
4. **API Integration**: Connect all components to backend APIs
5. **Testing Implementation**: Write comprehensive test suites
6. **Performance Optimization**: Implement code splitting and lazy loading
7. **Accessibility Audit**: Ensure WCAG 2.1 AA compliance
8. **Production Deployment**: Configure build and deployment pipeline

For detailed implementation guides, see the following sections:
* :doc:`react-components` - Component development patterns
* :doc:`react-testing` - Testing strategies and examples
* :doc:`react-deployment` - Production deployment guide
