Development Setup
=================

This guide covers setting up a complete development environment for the CVE Feed Service, including all dependencies, tools, and configurations needed for effective development.

Prerequisites
-------------

System Requirements
~~~~~~~~~~~~~~~~~~

**Operating System**:
* Linux (Ubuntu 20.04+ recommended)
* macOS 10.15+
* Windows 10+ with WSL2

**Required Software**:
* Python 3.11 or higher
* PostgreSQL 12 or higher
* Git 2.20+
* Node.js 16+ (for documentation tools)

**Recommended Tools**:
* Nix package manager (for reproducible environments)
* Docker and Docker Compose (for containerized development)
* VS Code or PyCharm (with Python extensions)

Installation Methods
-------------------

Method 1: Nix Development Environment (Recommended)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

<PERSON> provides a reproducible development environment with all dependencies.

**Install Nix**:

.. code-block:: bash

   # Install Nix (single-user installation)
   curl -L https://nixos.org/nix/install | sh
   
   # Reload shell
   source ~/.bashrc

**Setup Development Environment**:

.. code-block:: bash

   # Clone repository
   git clone https://github.com/forkrul/day3-cve-feed.git
   cd day3-cve-feed
   
   # Enter Nix shell (automatically installs all dependencies)
   nix-shell
   
   # Verify Python version
   python --version  # Should be 3.11+
   
   # Verify PostgreSQL
   psql --version

**What Nix Provides**:
* Python 3.11 with all required packages
* PostgreSQL with development tools
* Node.js for documentation building
* Development tools (black, mypy, pytest, etc.)
* Pre-configured environment variables

Method 2: Manual Setup
~~~~~~~~~~~~~~~~~~~~~

For systems without Nix or when you prefer manual control.

**Install Python 3.11+**:

.. code-block:: bash

   # Ubuntu/Debian
   sudo apt update
   sudo apt install python3.11 python3.11-venv python3.11-dev
   
   # macOS with Homebrew
   brew install python@3.11
   
   # Verify installation
   python3.11 --version

**Install PostgreSQL**:

.. code-block:: bash

   # Ubuntu/Debian
   sudo apt install postgresql postgresql-contrib libpq-dev
   
   # macOS with Homebrew
   brew install postgresql
   
   # Start PostgreSQL service
   sudo systemctl start postgresql  # Linux
   brew services start postgresql   # macOS

**Setup Project**:

.. code-block:: bash

   # Clone repository
   git clone https://github.com/forkrul/day3-cve-feed.git
   cd day3-cve-feed
   
   # Create virtual environment
   python3.11 -m venv venv
   source venv/bin/activate  # Linux/macOS
   # or
   venv\Scripts\activate     # Windows
   
   # Upgrade pip
   pip install --upgrade pip
   
   # Install project in development mode
   pip install -e ".[dev]"

Method 3: Docker Development
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Use Docker for a containerized development environment.

**Install Docker**:

.. code-block:: bash

   # Ubuntu
   sudo apt install docker.io docker-compose
   sudo usermod -aG docker $USER
   # Log out and back in
   
   # macOS
   # Download Docker Desktop from docker.com

**Setup with Docker Compose**:

.. code-block:: bash

   # Clone repository
   git clone https://github.com/forkrul/day3-cve-feed.git
   cd day3-cve-feed
   
   # Start development environment
   docker-compose -f docker-compose.dev.yml up -d
   
   # Enter development container
   docker-compose -f docker-compose.dev.yml exec app bash
   
   # Install dependencies
   pip install -e ".[dev]"

Database Setup
--------------

PostgreSQL Configuration
~~~~~~~~~~~~~~~~~~~~~~~~

**Create Development Database**:

.. code-block:: bash

   # Switch to postgres user (Linux)
   sudo -u postgres psql
   
   # Or connect directly (if configured)
   psql -U postgres
   
   # Create database and user
   CREATE DATABASE cve_feed_dev;
   CREATE USER cve_feed_user WITH PASSWORD 'dev_password';
   GRANT ALL PRIVILEGES ON DATABASE cve_feed_dev TO cve_feed_user;
   \q

**Create Test Database**:

.. code-block:: bash

   psql -U postgres
   CREATE DATABASE cve_feed_test;
   GRANT ALL PRIVILEGES ON DATABASE cve_feed_test TO cve_feed_user;
   \q

**Configure Connection**:

Create ``.env`` file in project root:

.. code-block:: bash

   # Database Configuration
   DATABASE_URL=postgresql+asyncpg://cve_feed_user:dev_password@localhost/cve_feed_dev
   TEST_DATABASE_URL=postgresql+asyncpg://cve_feed_user:dev_password@localhost/cve_feed_test
   
   # Application Configuration
   SECRET_KEY=your-secret-key-for-development
   ENVIRONMENT=development
   LOG_LEVEL=DEBUG
   
   # NVD API Configuration (optional for development)
   NVD_API_KEY=your-nvd-api-key
   
   # Development Settings
   RELOAD=true
   DEBUG=true

Database Migrations
~~~~~~~~~~~~~~~~~~

**Initialize Alembic** (if not already done):

.. code-block:: bash

   # Initialize migration repository
   alembic init alembic
   
   # Edit alembic.ini to use your database URL
   # sqlalchemy.url = postgresql+asyncpg://cve_feed_user:dev_password@localhost/cve_feed_dev

**Run Migrations**:

.. code-block:: bash

   # Apply all migrations
   alembic upgrade head
   
   # Verify tables were created
   psql -U cve_feed_user -d cve_feed_dev -c "\dt"

**Create New Migration**:

.. code-block:: bash

   # Auto-generate migration from model changes
   alembic revision --autogenerate -m "Add new feature"
   
   # Review generated migration file
   # Edit if necessary
   
   # Apply migration
   alembic upgrade head

Development Tools Setup
----------------------

Code Quality Tools
~~~~~~~~~~~~~~~~~

**Install Pre-commit Hooks**:

.. code-block:: bash

   # Install pre-commit
   pip install pre-commit
   
   # Install hooks
   pre-commit install
   
   # Run hooks manually
   pre-commit run --all-files

**Configure VS Code** (if using):

Create ``.vscode/settings.json``:

.. code-block:: json

   {
     "python.defaultInterpreterPath": "./venv/bin/python",
     "python.linting.enabled": true,
     "python.linting.flake8Enabled": true,
     "python.linting.mypyEnabled": true,
     "python.formatting.provider": "black",
     "python.formatting.blackArgs": ["--line-length", "100"],
     "python.sortImports.args": ["--profile", "black"],
     "editor.formatOnSave": true,
     "editor.codeActionsOnSave": {
       "source.organizeImports": true
     },
     "python.testing.pytestEnabled": true,
     "python.testing.pytestArgs": [
       "tests"
     ]
   }

**Configure PyCharm** (if using):

1. Open project in PyCharm
2. Configure Python interpreter: Settings → Project → Python Interpreter
3. Select the virtual environment interpreter
4. Enable code inspections: Settings → Editor → Inspections
5. Configure code style: Settings → Editor → Code Style → Python
6. Set line length to 100 characters

Testing Setup
~~~~~~~~~~~~

**Verify Test Environment**:

.. code-block:: bash

   # Run all tests
   pytest
   
   # Run with coverage
   pytest --cov=src/cve_feed_service --cov-report=html
   
   # Run specific test categories
   pytest -m unit
   pytest -m integration

**Test Database Setup**:

.. code-block:: bash

   # Set test database URL
   export TEST_DATABASE_URL=postgresql+asyncpg://cve_feed_user:dev_password@localhost/cve_feed_test
   
   # Run migrations on test database
   alembic -x database_url=$TEST_DATABASE_URL upgrade head

Documentation Setup
~~~~~~~~~~~~~~~~~~

**Install Documentation Dependencies**:

.. code-block:: bash

   # Install Sphinx and extensions
   pip install sphinx sphinx-rtd-theme myst-parser
   
   # Install Mermaid support
   npm install -g @mermaid-js/mermaid-cli

**Build Documentation**:

.. code-block:: bash

   # Build HTML documentation
   cd docs
   make html
   
   # Open in browser
   open _build/html/index.html  # macOS
   xdg-open _build/html/index.html  # Linux

**Live Documentation Server**:

.. code-block:: bash

   # Install sphinx-autobuild
   pip install sphinx-autobuild
   
   # Start live reload server
   cd docs
   sphinx-autobuild . _build/html --host 0.0.0.0 --port 8080

Environment Variables
--------------------

Development Environment
~~~~~~~~~~~~~~~~~~~~~~

Create ``.env`` file with development settings:

.. code-block:: bash

   # Application
   ENVIRONMENT=development
   DEBUG=true
   LOG_LEVEL=DEBUG
   SECRET_KEY=dev-secret-key-change-in-production
   
   # Database
   DATABASE_URL=postgresql+asyncpg://cve_feed_user:dev_password@localhost/cve_feed_dev
   TEST_DATABASE_URL=postgresql+asyncpg://cve_feed_user:dev_password@localhost/cve_feed_test
   
   # Server
   HOST=0.0.0.0
   PORT=8000
   RELOAD=true
   
   # External APIs
   NVD_API_KEY=your-nvd-api-key-here
   NVD_API_BASE_URL=https://services.nvd.nist.gov/rest/json
   
   # Security
   ACCESS_TOKEN_EXPIRE_MINUTES=60
   ALGORITHM=HS256
   
   # Features
   ENABLE_NVD_SYNC=false
   ENABLE_BACKGROUND_TASKS=false

Testing Environment
~~~~~~~~~~~~~~~~~~

Create ``.env.test`` for test-specific settings:

.. code-block:: bash

   # Test Environment
   ENVIRONMENT=test
   DEBUG=false
   LOG_LEVEL=WARNING
   SECRET_KEY=test-secret-key
   
   # Test Database
   DATABASE_URL=postgresql+asyncpg://cve_feed_user:dev_password@localhost/cve_feed_test
   
   # Disable external services in tests
   ENABLE_NVD_SYNC=false
   ENABLE_BACKGROUND_TASKS=false
   NVD_API_KEY=test-key

Running the Application
----------------------

Development Server
~~~~~~~~~~~~~~~~~

**Start FastAPI Development Server**:

.. code-block:: bash

   # Using uvicorn directly
   uvicorn src.cve_feed_service.main:app --reload --host 0.0.0.0 --port 8000
   
   # Using the CLI
   python -m src.cve_feed_service.cli dev-server
   
   # Using make (if Makefile exists)
   make dev

**Access the Application**:

* API: http://localhost:8000
* Interactive API docs: http://localhost:8000/docs
* ReDoc documentation: http://localhost:8000/redoc
* OpenAPI schema: http://localhost:8000/openapi.json

**Development Features**:
* Auto-reload on code changes
* Detailed error messages
* Debug logging
* CORS enabled for local development

CLI Development
~~~~~~~~~~~~~~

**Test CLI Commands**:

.. code-block:: bash

   # Show available commands
   python -m src.cve_feed_service.cli --help
   
   # Create admin user
   python -m src.cve_feed_service.cli create-admin-user
   
   # Import CVE data
   python -m src.cve_feed_service.cli import-cves --days 7
   
   # Run database migrations
   python -m src.cve_feed_service.cli migrate

Troubleshooting
--------------

Common Issues
~~~~~~~~~~~~

**Database Connection Issues**:

.. code-block:: bash

   # Check PostgreSQL is running
   sudo systemctl status postgresql  # Linux
   brew services list | grep postgresql  # macOS
   
   # Test connection
   psql -U cve_feed_user -d cve_feed_dev -c "SELECT 1;"
   
   # Check database exists
   psql -U postgres -c "\l" | grep cve_feed

**Python Environment Issues**:

.. code-block:: bash

   # Verify Python version
   python --version
   
   # Check virtual environment
   which python
   
   # Reinstall dependencies
   pip install --force-reinstall -e ".[dev]"

**Import Errors**:

.. code-block:: bash

   # Check PYTHONPATH
   echo $PYTHONPATH
   
   # Add project root to PYTHONPATH
   export PYTHONPATH="${PYTHONPATH}:$(pwd)"
   
   # Verify package installation
   pip show cve-feed-service

**Test Failures**:

.. code-block:: bash

   # Run tests with verbose output
   pytest -v
   
   # Run specific failing test
   pytest tests/test_specific.py::test_function -v
   
   # Check test database
   psql -U cve_feed_user -d cve_feed_test -c "\dt"

Performance Issues
~~~~~~~~~~~~~~~~~

**Slow Database Queries**:

.. code-block:: bash

   # Enable query logging in PostgreSQL
   # Edit postgresql.conf:
   # log_statement = 'all'
   # log_duration = on
   
   # Restart PostgreSQL
   sudo systemctl restart postgresql

**Memory Usage**:

.. code-block:: bash

   # Monitor memory usage
   htop
   
   # Check Python memory usage
   python -m memory_profiler your_script.py

Development Workflow
-------------------

Daily Development
~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Start development session
   cd day3-cve-feed
   nix-shell  # or source venv/bin/activate
   
   # Pull latest changes
   git pull origin develop
   
   # Run migrations
   alembic upgrade head
   
   # Run tests
   pytest
   
   # Start development server
   uvicorn src.cve_feed_service.main:app --reload

Feature Development
~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Create feature branch
   git checkout -b feature/new-feature
   
   # Make changes
   # Write tests
   # Update documentation
   
   # Run quality checks
   pre-commit run --all-files
   
   # Run tests
   pytest
   
   # Commit changes
   git add .
   git commit -m "feat: add new feature"
   
   # Push and create PR
   git push origin feature/new-feature

Next Steps
----------

* :doc:`architecture` - Understanding the system architecture
* :doc:`testing` - Comprehensive testing guide
* :doc:`contributing` - Contributing guidelines and best practices
* :doc:`../testing/index` - Detailed testing framework documentation
