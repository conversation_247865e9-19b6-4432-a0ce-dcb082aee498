React Testing Guide
====================

This guide covers comprehensive testing strategies for the React frontend, including unit tests, integration tests, end-to-end tests, and performance testing.

Testing Philosophy
------------------

**Testing Pyramid:**
* **Unit Tests (70%)**: Individual components, hooks, and utilities
* **Integration Tests (20%)**: Component interactions and API integration
* **End-to-End Tests (10%)**: Complete user workflows

**Testing Principles:**
* Test behavior, not implementation details
* Write tests that give confidence in the application
* Maintain tests as first-class citizens
* Use appropriate testing tools for each layer

Testing Stack
-------------

**Core Testing Libraries:**
* **Jest**: Test runner and assertion library
* **React Testing Library**: Component testing utilities
* **Playwright**: End-to-end testing framework
* **MSW**: API mocking for integration tests
* **Storybook**: Component development and visual testing

**Additional Tools:**
* **@testing-library/user-event**: User interaction simulation
* **@testing-library/jest-dom**: Custom Jest matchers
* **jest-environment-jsdom**: DOM environment for Jest
* **@storybook/test-runner**: Automated Storybook testing

Unit Testing
------------

Component Testing Patterns
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Basic Component Test:**

.. code-block:: typescript

   // src/components/__tests__/Button.test.tsx
   import { render, screen } from '@testing-library/react';
   import userEvent from '@testing-library/user-event';
   import { Button } from '../atoms/Button';

   describe('Button Component', () => {
     test('renders with correct text', () => {
       render(<Button>Click me</Button>);
       expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
     });

     test('handles click events', async () => {
       const handleClick = jest.fn();
       const user = userEvent.setup();
       
       render(<Button onClick={handleClick}>Click me</Button>);
       
       await user.click(screen.getByRole('button'));
       expect(handleClick).toHaveBeenCalledTimes(1);
     });

     test('shows loading state', () => {
       render(<Button loading>Loading</Button>);
       
       expect(screen.getByRole('button')).toBeDisabled();
       expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
     });

     test('applies variant styles correctly', () => {
       render(<Button variant="danger">Delete</Button>);
       
       const button = screen.getByRole('button');
       expect(button).toHaveClass('bg-red-600');
     });
   });

**Form Component Testing:**

.. code-block:: typescript

   // src/components/__tests__/LoginForm.test.tsx
   import { render, screen, waitFor } from '@testing-library/react';
   import userEvent from '@testing-library/user-event';
   import { Provider } from 'react-redux';
   import { BrowserRouter } from 'react-router-dom';
   import { LoginForm } from '../forms/LoginForm';
   import { createMockStore } from '../../test-utils/mockStore';

   const renderWithProviders = (component: React.ReactElement) => {
     const store = createMockStore();
     return render(
       <Provider store={store}>
         <BrowserRouter>
           {component}
         </BrowserRouter>
       </Provider>
     );
   };

   describe('LoginForm', () => {
     test('validates required fields', async () => {
       const user = userEvent.setup();
       renderWithProviders(<LoginForm />);

       const submitButton = screen.getByRole('button', { name: /sign in/i });
       await user.click(submitButton);

       await waitFor(() => {
         expect(screen.getByText(/email is required/i)).toBeInTheDocument();
         expect(screen.getByText(/password is required/i)).toBeInTheDocument();
       });
     });

     test('validates email format', async () => {
       const user = userEvent.setup();
       renderWithProviders(<LoginForm />);

       const emailInput = screen.getByLabelText(/email/i);
       await user.type(emailInput, 'invalid-email');
       await user.tab(); // Trigger validation

       await waitFor(() => {
         expect(screen.getByText(/invalid email format/i)).toBeInTheDocument();
       });
     });

     test('submits form with valid data', async () => {
       const user = userEvent.setup();
       const mockSubmit = jest.fn();
       
       renderWithProviders(<LoginForm onSubmit={mockSubmit} />);

       await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
       await user.type(screen.getByLabelText(/password/i), 'password123');
       await user.click(screen.getByRole('button', { name: /sign in/i }));

       await waitFor(() => {
         expect(mockSubmit).toHaveBeenCalledWith({
           email: '<EMAIL>',
           password: 'password123',
         });
       });
     });
   });

Hook Testing
~~~~~~~~~~~~~

**Custom Hook Testing:**

.. code-block:: typescript

   // src/hooks/__tests__/useAuth.test.ts
   import { renderHook, act } from '@testing-library/react';
   import { Provider } from 'react-redux';
   import { useAuth } from '../useAuth';
   import { createMockStore } from '../../test-utils/mockStore';
   import { server } from '../../test-utils/server';
   import { rest } from 'msw';

   const wrapper = ({ children }: { children: React.ReactNode }) => {
     const store = createMockStore();
     return <Provider store={store}>{children}</Provider>;
   };

   describe('useAuth Hook', () => {
     test('returns initial state', () => {
       const { result } = renderHook(() => useAuth(), { wrapper });

       expect(result.current.isAuthenticated).toBe(false);
       expect(result.current.user).toBe(null);
       expect(result.current.loading).toBe(false);
     });

     test('handles successful login', async () => {
       server.use(
         rest.post('/api/v1/auth/login', (req, res, ctx) => {
           return res(
             ctx.json({
               access_token: 'mock-token',
               user: { id: '1', email: '<EMAIL>' },
             })
           );
         })
       );

       const { result } = renderHook(() => useAuth(), { wrapper });

       await act(async () => {
         await result.current.login({
           email: '<EMAIL>',
           password: 'password123',
         });
       });

       expect(result.current.isAuthenticated).toBe(true);
       expect(result.current.user).toEqual({
         id: '1',
         email: '<EMAIL>',
       });
     });

     test('handles login error', async () => {
       server.use(
         rest.post('/api/v1/auth/login', (req, res, ctx) => {
           return res(ctx.status(401), ctx.json({ message: 'Invalid credentials' }));
         })
       );

       const { result } = renderHook(() => useAuth(), { wrapper });

       await act(async () => {
         try {
           await result.current.login({
             email: '<EMAIL>',
             password: 'wrong-password',
           });
         } catch (error) {
           expect(error.message).toBe('Invalid credentials');
         }
       });

       expect(result.current.isAuthenticated).toBe(false);
     });
   });

Integration Testing
-------------------

API Integration Tests
~~~~~~~~~~~~~~~~~~~~~

**MSW Setup:**

.. code-block:: typescript

   // src/test-utils/server.ts
   import { setupServer } from 'msw/node';
   import { rest } from 'msw';
   import { mockCVEs, mockApplications, mockUsers } from './fixtures';

   export const handlers = [
     // Auth endpoints
     rest.post('/api/v1/auth/login', (req, res, ctx) => {
       return res(
         ctx.json({
           access_token: 'mock-jwt-token',
           token_type: 'bearer',
           user: mockUsers[0],
         })
       );
     }),

     // CVE endpoints
     rest.get('/api/v1/cves/feed', (req, res, ctx) => {
       const severity = req.url.searchParams.get('severity');
       const limit = parseInt(req.url.searchParams.get('limit') || '50');
       
       let filteredCVEs = mockCVEs;
       if (severity) {
         filteredCVEs = mockCVEs.filter(cve => cve.severity === severity);
       }

       return res(
         ctx.json({
           cves: filteredCVEs.slice(0, limit),
           total: filteredCVEs.length,
           limit,
           offset: 0,
           has_more: filteredCVEs.length > limit,
         })
       );
     }),

     // Application endpoints
     rest.get('/api/v1/applications', (req, res, ctx) => {
       return res(ctx.json(mockApplications));
     }),

     rest.post('/api/v1/applications', (req, res, ctx) => {
       return res(
         ctx.status(201),
         ctx.json({
           id: 'new-app-id',
           ...req.body,
           created_at: new Date().toISOString(),
         })
       );
     }),
   ];

   export const server = setupServer(...handlers);

**Integration Test Example:**

.. code-block:: typescript

   // src/pages/__tests__/CVEFeedPage.integration.test.tsx
   import { render, screen, waitFor } from '@testing-library/react';
   import userEvent from '@testing-library/user-event';
   import { Provider } from 'react-redux';
   import { BrowserRouter } from 'react-router-dom';
   import { CVEFeedPage } from '../CVEFeedPage';
   import { createMockStore } from '../../test-utils/mockStore';
   import { server } from '../../test-utils/server';

   const renderWithProviders = (component: React.ReactElement) => {
     const store = createMockStore({
       auth: {
         isAuthenticated: true,
         user: { id: '1', email: '<EMAIL>' },
         token: 'mock-token',
       },
     });

     return render(
       <Provider store={store}>
         <BrowserRouter>
           {component}
         </BrowserRouter>
       </Provider>
     );
   };

   describe('CVEFeedPage Integration', () => {
     test('loads and displays CVE feed', async () => {
       renderWithProviders(<CVEFeedPage />);

       // Wait for loading to complete
       await waitFor(() => {
         expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
       });

       // Verify CVEs are displayed
       expect(screen.getByText('CVE-2023-1234')).toBeInTheDocument();
       expect(screen.getByText('CVE-2023-5678')).toBeInTheDocument();
     });

     test('filters CVEs by severity', async () => {
       const user = userEvent.setup();
       renderWithProviders(<CVEFeedPage />);

       // Wait for initial load
       await waitFor(() => {
         expect(screen.getByText('CVE-2023-1234')).toBeInTheDocument();
       });

       // Apply severity filter
       const severityFilter = screen.getByLabelText(/severity/i);
       await user.selectOptions(severityFilter, 'CRITICAL');

       // Wait for filtered results
       await waitFor(() => {
         expect(screen.getByText('CVE-2023-1234')).toBeInTheDocument();
         expect(screen.queryByText('CVE-2023-5678')).not.toBeInTheDocument();
       });
     });

     test('handles API errors gracefully', async () => {
       // Mock API error
       server.use(
         rest.get('/api/v1/cves/feed', (req, res, ctx) => {
           return res(ctx.status(500), ctx.json({ message: 'Server error' }));
         })
       );

       renderWithProviders(<CVEFeedPage />);

       await waitFor(() => {
         expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
       });
     });
   });

End-to-End Testing
------------------

Playwright Test Setup
~~~~~~~~~~~~~~~~~~~~~~

**Configuration:**

.. code-block:: typescript

   // playwright.config.ts
   import { defineConfig, devices } from '@playwright/test';

   export default defineConfig({
     testDir: './tests/e2e',
     fullyParallel: true,
     forbidOnly: !!process.env.CI,
     retries: process.env.CI ? 2 : 0,
     workers: process.env.CI ? 1 : undefined,
     reporter: [
       ['html'],
       ['json', { outputFile: 'test-results/results.json' }],
       ['junit', { outputFile: 'test-results/results.xml' }],
     ],
     use: {
       baseURL: 'http://localhost:3000',
       trace: 'on-first-retry',
       screenshot: 'only-on-failure',
       video: 'retain-on-failure',
     },
     projects: [
       {
         name: 'chromium',
         use: { ...devices['Desktop Chrome'] },
       },
       {
         name: 'firefox',
         use: { ...devices['Desktop Firefox'] },
       },
       {
         name: 'webkit',
         use: { ...devices['Desktop Safari'] },
       },
       {
         name: 'mobile-chrome',
         use: { ...devices['Pixel 5'] },
       },
     ],
     webServer: {
       command: 'npm run preview',
       url: 'http://localhost:3000',
       reuseExistingServer: !process.env.CI,
     },
   });

**E2E Test Examples:**

.. code-block:: typescript

   // tests/e2e/auth-flow.spec.ts
   import { test, expect } from '@playwright/test';

   test.describe('Authentication Flow', () => {
     test('user can login and logout', async ({ page }) => {
       // Navigate to login page
       await page.goto('/login');
       
       // Fill login form
       await page.fill('[data-testid=email-input]', '<EMAIL>');
       await page.fill('[data-testid=password-input]', 'password123');
       
       // Submit form
       await page.click('[data-testid=login-button]');
       
       // Verify redirect to dashboard
       await expect(page).toHaveURL('/dashboard');
       await expect(page.locator('[data-testid=user-menu]')).toBeVisible();
       
       // Logout
       await page.click('[data-testid=user-menu]');
       await page.click('[data-testid=logout-button]');
       
       // Verify redirect to login
       await expect(page).toHaveURL('/login');
     });

     test('shows error for invalid credentials', async ({ page }) => {
       await page.goto('/login');
       
       await page.fill('[data-testid=email-input]', '<EMAIL>');
       await page.fill('[data-testid=password-input]', 'wrongpassword');
       await page.click('[data-testid=login-button]');
       
       await expect(page.locator('[data-testid=error-message]')).toBeVisible();
       await expect(page.locator('[data-testid=error-message]')).toContainText('Invalid credentials');
     });
   });

**Complex User Journey:**

.. code-block:: typescript

   // tests/e2e/cve-management-journey.spec.ts
   import { test, expect } from '@playwright/test';

   test.describe('CVE Management Journey', () => {
     test.beforeEach(async ({ page }) => {
       // Login before each test
       await page.goto('/login');
       await page.fill('[data-testid=email-input]', '<EMAIL>');
       await page.fill('[data-testid=password-input]', 'password123');
       await page.click('[data-testid=login-button]');
       await expect(page).toHaveURL('/dashboard');
     });

     test('complete vulnerability assessment workflow', async ({ page }) => {
       // Navigate to applications
       await page.click('[data-testid=nav-applications]');
       await expect(page).toHaveURL('/applications');
       
       // Create new application
       await page.click('[data-testid=add-application-btn]');
       await page.fill('[data-testid=app-name-input]', 'Test Web App');
       await page.selectOption('[data-testid=environment-select]', 'production');
       await page.selectOption('[data-testid=criticality-select]', 'high');
       await page.click('[data-testid=save-application-btn]');
       
       // Verify application created
       await expect(page.locator('[data-testid=success-notification]')).toBeVisible();
       await expect(page.locator('text=Test Web App')).toBeVisible();
       
       // Add component to application
       await page.click('text=Test Web App');
       await page.click('[data-testid=add-component-btn]');
       await page.fill('[data-testid=component-name]', 'nginx');
       await page.fill('[data-testid=component-version]', '1.20.1');
       await page.click('[data-testid=save-component-btn]');
       
       // Navigate to CVE feed
       await page.click('[data-testid=nav-cves]');
       await expect(page).toHaveURL('/cves');
       
       // Filter by application
       await page.selectOption('[data-testid=application-filter]', 'Test Web App');
       await page.waitForResponse('/api/v1/cves/feed*');
       
       // Verify filtered results
       const cveItems = page.locator('[data-testid=cve-item]');
       await expect(cveItems).toHaveCount(3);
       
       // View CVE details
       await cveItems.first().click();
       await expect(page.locator('[data-testid=cve-header]')).toBeVisible();
       await expect(page.locator('[data-testid=affected-components]')).toContainText('nginx');
       
       // Export CVE data
       await page.goBack();
       const downloadPromise = page.waitForEvent('download');
       await page.click('[data-testid=export-button]');
       await page.click('[data-testid=export-csv]');
       const download = await downloadPromise;
       expect(download.suggestedFilename()).toMatch(/cves-export-.*\.csv/);
     });
   });

Visual Testing
--------------

**Storybook Integration:**

.. code-block:: typescript

   // .storybook/test-runner.ts
   import type { TestRunnerConfig } from '@storybook/test-runner';

   const config: TestRunnerConfig = {
     setup() {
       // Global setup
     },
     async postRender(page, context) {
       // Accessibility testing
       const { injectAxe, checkA11y } = await import('axe-playwright');
       await injectAxe(page);
       await checkA11y(page, '#root', {
         detailedReport: true,
         detailedReportOptions: { html: true },
       });
     },
   };

   export default config;

Performance Testing
-------------------

**Lighthouse CI:**

.. code-block:: javascript

   // lighthouserc.js
   module.exports = {
     ci: {
       collect: {
         url: [
           'http://localhost:3000/',
           'http://localhost:3000/dashboard',
           'http://localhost:3000/cves',
           'http://localhost:3000/applications',
         ],
         startServerCommand: 'npm run preview',
         numberOfRuns: 3,
       },
       assert: {
         assertions: {
           'categories:performance': ['warn', { minScore: 0.9 }],
           'categories:accessibility': ['error', { minScore: 0.95 }],
           'categories:best-practices': ['warn', { minScore: 0.9 }],
           'categories:seo': ['warn', { minScore: 0.9 }],
         },
       },
       upload: {
         target: 'temporary-public-storage',
       },
     },
   };

Test Utilities
--------------

**Custom Render Function:**

.. code-block:: typescript

   // src/test-utils/render.tsx
   import React from 'react';
   import { render, type RenderOptions } from '@testing-library/react';
   import { Provider } from 'react-redux';
   import { BrowserRouter } from 'react-router-dom';
   import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
   import { createMockStore, type MockStoreOptions } from './mockStore';

   interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
     initialState?: MockStoreOptions;
     route?: string;
   }

   export function renderWithProviders(
     ui: React.ReactElement,
     {
       initialState,
       route = '/',
       ...renderOptions
     }: CustomRenderOptions = {}
   ) {
     const store = createMockStore(initialState);
     const queryClient = new QueryClient({
       defaultOptions: {
         queries: { retry: false },
         mutations: { retry: false },
       },
     });

     window.history.pushState({}, 'Test page', route);

     function Wrapper({ children }: { children: React.ReactNode }) {
       return (
         <Provider store={store}>
           <QueryClientProvider client={queryClient}>
             <BrowserRouter>
               {children}
             </BrowserRouter>
           </QueryClientProvider>
         </Provider>
       );
     }

     return {
       store,
       queryClient,
       ...render(ui, { wrapper: Wrapper, ...renderOptions }),
     };
   }

   // Re-export everything
   export * from '@testing-library/react';

This comprehensive testing guide ensures robust quality assurance for the React frontend application.
