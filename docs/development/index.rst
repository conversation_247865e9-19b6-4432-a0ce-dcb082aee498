Development Guide
=================

This guide covers development setup, contributing guidelines, and technical details for developers working on the CVE Feed Service.

.. toctree::
   :maxdepth: 2

   setup
   react-setup
   react-components
   react-testing
   architecture
   testing
   contributing

Overview
--------

The CVE Feed Service is built with modern Python technologies and follows best practices for API development, security, and maintainability.

**Technology Stack:**
* **Backend**: FastAPI with async/await
* **Database**: PostgreSQL with SQLAlchemy 2.0
* **Authentication**: JWT + API Keys with bcrypt
* **Validation**: Pydantic v2 with type hints
* **Migrations**: Alembic
* **HTTP Client**: httpx for async requests
* **Logging**: structlog for structured logging
* **CLI**: Typer for command-line interface
* **Testing**: pytest with async support

**Development Principles:**
* Type safety with comprehensive type hints
* Async/await throughout for performance
* Comprehensive error handling and logging
* API-first design with OpenAPI documentation
* Test-driven development
* Security by design

Quick Start
-----------

**Prerequisites:**
* Python 3.11+
* PostgreSQL 12+
* Git
* Nix (recommended) or pip/virtualenv

**Setup:**

.. code-block:: bash

   # Clone repository
   <NAME_EMAIL>:forkrul/day3-cve-feed.git
   cd day3-cve-feed

   # Enter development environment (Nix)
   nix-shell

   # Or manual setup
   python -m venv venv
   source venv/bin/activate
   pip install -e ".[dev]"

   # Setup database
   createdb cve_feed_dev
   alembic upgrade head

   # Run tests
   pytest

   # Start development server
   python -m src.cve_feed_service.main

Project Structure
-----------------

.. code-block:: text

   day3-cve-feed/
   ├── src/cve_feed_service/          # Main application package
   │   ├── api/v1/                    # API endpoints
   │   │   ├── endpoints/             # Route handlers
   │   │   └── router.py              # Main router
   │   ├── cli/                       # Command-line interface
   │   ├── core/                      # Core utilities
   │   │   ├── auth.py                # Authentication utilities
   │   │   ├── config.py              # Configuration management
   │   │   └── dependencies.py       # FastAPI dependencies
   │   ├── db/                        # Database configuration
   │   │   ├── base.py                # Base model class
   │   │   └── database.py            # Database setup
   │   ├── models/                    # SQLAlchemy models
   │   │   ├── application.py         # Application models
   │   │   ├── cve.py                 # CVE models
   │   │   └── user.py                # User models
   │   ├── schemas/                   # Pydantic schemas
   │   │   ├── application.py         # Application schemas
   │   │   ├── auth.py                # Authentication schemas
   │   │   └── cve.py                 # CVE schemas
   │   ├── services/                  # Business logic layer
   │   │   ├── application_service.py # Application management
   │   │   ├── auth_service.py        # Authentication
   │   │   ├── component_service.py   # Component management
   │   │   ├── cve_ingestion_service.py # CVE data ingestion
   │   │   ├── cve_service.py         # CVE queries
   │   │   └── nvd_client.py          # NVD API client
   │   ├── utils/                     # Utility functions
   │   │   └── cpe_utils.py           # CPE validation/parsing
   │   └── main.py                    # Application entry point
   ├── tests/                         # Test suite
   │   ├── conftest.py                # Test configuration
   │   ├── test_api/                  # API tests
   │   ├── test_models/               # Model tests
   │   └── test_services/             # Service tests
   ├── alembic/                       # Database migrations
   │   ├── versions/                  # Migration files
   │   └── env.py                     # Alembic configuration
   ├── docs/                          # Documentation
   ├── shell.nix                      # Nix development environment
   ├── pyproject.toml                 # Project configuration
   └── README.md                      # Project overview

Development Workflow
--------------------

**Branch Strategy:**
* ``master``: Production-ready code
* ``develop``: Integration branch for features
* ``feature/*``: Feature development branches
* ``hotfix/*``: Critical bug fixes

**Commit Guidelines:**
* Use conventional commit format
* Include issue numbers when applicable
* Write descriptive commit messages

.. code-block:: text

   feat: add CPE validation utilities
   fix: resolve authentication token expiration issue
   docs: update API documentation for new endpoints
   test: add integration tests for vulnerability feeds

**Pull Request Process:**
1. Create feature branch from ``develop``
2. Implement changes with tests
3. Update documentation if needed
4. Submit pull request with description
5. Address review feedback
6. Merge after approval and CI passes

Code Standards
--------------

**Python Style:**
* Follow PEP 8 with line length of 100 characters
* Use type hints for all functions and methods
* Use docstrings for all public functions
* Prefer async/await over synchronous code

**Code Quality Tools:**
* **Black**: Code formatting
* **isort**: Import sorting
* **flake8**: Linting
* **mypy**: Type checking
* **pytest**: Testing

**Pre-commit Hooks:**

.. code-block:: bash

   # Install pre-commit hooks
   pre-commit install

   # Run manually
   pre-commit run --all-files

**Example Code Style:**

.. code-block:: python

   from typing import List, Optional
   from uuid import UUID

   import structlog
   from sqlalchemy.ext.asyncio import AsyncSession

   logger = structlog.get_logger(__name__)


   async def get_application_vulnerabilities(
       db: AsyncSession,
       application_id: UUID,
       severity: Optional[str] = None,
       limit: int = 100,
   ) -> List[CVE]:
       """Get vulnerabilities for a specific application.
       
       Args:
           db: Database session
           application_id: Application UUID
           severity: Optional severity filter
           limit: Maximum number of results
           
       Returns:
           List of CVE objects
           
       Raises:
           ValueError: If application not found
       """
       logger.info("Getting application vulnerabilities", 
                   application_id=application_id, severity=severity)
       
       # Implementation here
       pass

Testing Guidelines
------------------

**Test Structure:**
* Unit tests for individual functions
* Integration tests for API endpoints
* Service tests for business logic
* End-to-end tests for complete workflows

**Test Categories:**

.. code-block:: bash

   # Run all tests
   pytest

   # Run unit tests only
   pytest tests/unit/

   # Run integration tests
   pytest tests/integration/

   # Run with coverage
   pytest --cov=src/cve_feed_service

**Test Fixtures:**

.. code-block:: python

   # tests/conftest.py
   import pytest
   from sqlalchemy.ext.asyncio import AsyncSession
   from fastapi.testclient import TestClient

   from src.cve_feed_service.main import app
   from src.cve_feed_service.db.database import get_db


   @pytest.fixture
   def client():
       """Test client fixture."""
       return TestClient(app)


   @pytest.fixture
   async def db_session():
       """Database session fixture."""
       # Setup test database session
       pass


   @pytest.fixture
   def sample_application():
       """Sample application data."""
       return {
           "name": "Test App",
           "environment": "test",
           "criticality": "medium"
       }

**Example Test:**

.. code-block:: python

   # tests/test_api/test_applications.py
   import pytest
   from fastapi.testclient import TestClient


   def test_create_application(client: TestClient, auth_headers: dict):
       """Test application creation."""
       application_data = {
           "name": "Test Application",
           "environment": "test",
           "criticality": "medium"
       }
       
       response = client.post(
           "/api/v1/applications",
           json=application_data,
           headers=auth_headers
       )
       
       assert response.status_code == 201
       data = response.json()
       assert data["name"] == application_data["name"]
       assert "id" in data

Database Development
--------------------

**Migration Workflow:**

.. code-block:: bash

   # Create new migration
   alembic revision --autogenerate -m "Add new table"

   # Review generated migration
   # Edit migration file if needed

   # Apply migration
   alembic upgrade head

   # Rollback if needed
   alembic downgrade -1

**Model Development:**

.. code-block:: python

   # src/cve_feed_service/models/example.py
   from sqlalchemy import String, Text
   from sqlalchemy.orm import Mapped, mapped_column

   from ..db.base import BaseModel


   class ExampleModel(BaseModel):
       """Example model with soft delete support."""
       
       __tablename__ = "examples"
       
       name: Mapped[str] = mapped_column(String(255), nullable=False)
       description: Mapped[str] = mapped_column(Text, nullable=True)
       
       def __repr__(self) -> str:
           return f"<ExampleModel(id={self.id}, name='{self.name}')>"

**Service Development:**

.. code-block:: python

   # src/cve_feed_service/services/example_service.py
   from typing import List, Optional
   from uuid import UUID

   from sqlalchemy import select
   from sqlalchemy.ext.asyncio import AsyncSession

   from ..models.example import ExampleModel
   from ..schemas.example import ExampleCreate, ExampleUpdate


   class ExampleService:
       """Service for managing examples."""
       
       def __init__(self, db: AsyncSession) -> None:
           self.db = db
       
       async def create_example(self, data: ExampleCreate) -> ExampleModel:
           """Create a new example."""
           example = ExampleModel(**data.model_dump())
           self.db.add(example)
           await self.db.commit()
           await self.db.refresh(example)
           return example

API Development
---------------

**Endpoint Development:**

.. code-block:: python

   # src/cve_feed_service/api/v1/endpoints/examples.py
   from typing import List
   from uuid import UUID

   from fastapi import APIRouter, Depends, HTTPException, status
   from sqlalchemy.ext.asyncio import AsyncSession

   from ....core.dependencies import require_authentication
   from ....db.database import get_db
   from ....schemas.example import ExampleCreate, ExampleResponse
   from ....services.example_service import ExampleService

   router = APIRouter()


   @router.post("/", response_model=ExampleResponse, status_code=status.HTTP_201_CREATED)
   async def create_example(
       data: ExampleCreate,
       current_user = Depends(require_authentication),
       db: AsyncSession = Depends(get_db),
   ) -> ExampleResponse:
       """Create a new example."""
       service = ExampleService(db)
       example = await service.create_example(data)
       return ExampleResponse.model_validate(example)

**Schema Development:**

.. code-block:: python

   # src/cve_feed_service/schemas/example.py
   from datetime import datetime
   from typing import Optional
   from uuid import UUID

   from pydantic import BaseModel, Field


   class ExampleBase(BaseModel):
       """Base example schema."""
       name: str = Field(..., min_length=1, max_length=255)
       description: Optional[str] = None


   class ExampleCreate(ExampleBase):
       """Schema for creating examples."""
       pass


   class ExampleResponse(ExampleBase):
       """Schema for example responses."""
       id: UUID
       created_at: datetime
       updated_at: datetime
       
       class Config:
           from_attributes = True

Performance Considerations
-------------------------

**Database Optimization:**
* Use appropriate indexes for query patterns
* Implement pagination for large result sets
* Use async database operations
* Monitor query performance

**API Performance:**
* Implement response caching where appropriate
* Use async/await throughout
* Optimize serialization with Pydantic
* Monitor response times

**Memory Management:**
* Process large datasets in batches
* Use streaming for large responses
* Monitor memory usage during imports
* Implement proper connection pooling

Security Considerations
----------------------

**Authentication:**
* Use strong JWT secrets
* Implement proper token expiration
* Secure API key generation and storage
* Regular security audits

**Input Validation:**
* Validate all input with Pydantic
* Sanitize user input
* Implement rate limiting
* Use parameterized queries

**Data Protection:**
* Hash passwords with bcrypt
* Use HTTPS in production
* Implement proper CORS policies
* Regular security updates

Deployment Preparation
---------------------

**Environment Configuration:**
* Use environment variables for configuration
* Separate development/staging/production configs
* Secure secret management
* Health check endpoints

**Monitoring:**
* Structured logging with correlation IDs
* Performance metrics collection
* Error tracking and alerting
* Database performance monitoring

**Documentation:**
* Keep API documentation current
* Document deployment procedures
* Maintain troubleshooting guides
* Update configuration examples

Contributing
------------

**Getting Started:**
1. Fork the repository
2. Set up development environment
3. Read existing code and tests
4. Start with small improvements
5. Ask questions in issues or discussions

**Areas for Contribution:**
* Bug fixes and improvements
* New features and enhancements
* Documentation improvements
* Test coverage expansion
* Performance optimizations

**Community Guidelines:**
* Be respectful and inclusive
* Follow code of conduct
* Provide constructive feedback
* Help others learn and contribute
