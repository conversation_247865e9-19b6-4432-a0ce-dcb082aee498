React Component Development Guide
==================================

This guide provides detailed patterns and best practices for developing React components in the CVE Feed Service frontend, focusing on reusability, accessibility, and maintainability.

Component Architecture
----------------------

Design System Approach
~~~~~~~~~~~~~~~~~~~~~~~

The component library follows atomic design principles:

* **Atoms**: Basic building blocks (Button, Input, Badge)
* **Molecules**: Simple combinations (SearchBar, FormField)
* **Organisms**: Complex components (DataTable, CVECard)
* **Templates**: Page layouts (DashboardLayout, AuthLayout)
* **Pages**: Complete views (Dashboard, CVEList, ApplicationDetails)

**Component Structure:**

.. code-block:: text

   src/components/
   ├── atoms/
   │   ├── Button/
   │   │   ├── Button.tsx
   │   │   ├── Button.test.tsx
   │   │   ├── Button.stories.tsx
   │   │   └── index.ts
   │   ├── Badge/
   │   └── Input/
   ├── molecules/
   │   ├── SearchBar/
   │   ├── FormField/
   │   └── MetricsCard/
   ├── organisms/
   │   ├── DataTable/
   │   ├── CVECard/
   │   └── ApplicationForm/
   ├── templates/
   │   ├── DashboardLayout/
   │   └── AuthLayout/
   └── index.ts  # Barrel exports

Core Component Patterns
-----------------------

Button Component
~~~~~~~~~~~~~~~~

**Implementation:**

.. code-block:: typescript

   // src/components/atoms/Button/Button.tsx
   import React from 'react';
   import { clsx } from 'clsx';
   import { Spinner } from '../Spinner';

   export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
     variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
     size?: 'sm' | 'md' | 'lg';
     loading?: boolean;
     leftIcon?: React.ReactNode;
     rightIcon?: React.ReactNode;
     fullWidth?: boolean;
   }

   export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
     (
       {
         children,
         variant = 'primary',
         size = 'md',
         loading = false,
         leftIcon,
         rightIcon,
         fullWidth = false,
         disabled,
         className,
         ...props
       },
       ref
     ) => {
       const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';
       
       const variantClasses = {
         primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 disabled:bg-primary-300',
         secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500 disabled:bg-gray-100 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600',
         danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 disabled:bg-red-300',
         ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500 dark:text-gray-300 dark:hover:bg-gray-800',
       };

       const sizeClasses = {
         sm: 'px-3 py-1.5 text-sm',
         md: 'px-4 py-2 text-sm',
         lg: 'px-6 py-3 text-base',
       };

       const classes = clsx(
         baseClasses,
         variantClasses[variant],
         sizeClasses[size],
         fullWidth && 'w-full',
         className
       );

       return (
         <button
           ref={ref}
           className={classes}
           disabled={disabled || loading}
           {...props}
         >
           {loading && <Spinner className="mr-2 h-4 w-4" />}
           {!loading && leftIcon && <span className="mr-2">{leftIcon}</span>}
           {children}
           {!loading && rightIcon && <span className="ml-2">{rightIcon}</span>}
         </button>
       );
     }
   );

   Button.displayName = 'Button';

**Storybook Stories:**

.. code-block:: typescript

   // src/components/atoms/Button/Button.stories.tsx
   import type { Meta, StoryObj } from '@storybook/react';
   import { PlusIcon } from '@heroicons/react/24/outline';
   import { Button } from './Button';

   const meta: Meta<typeof Button> = {
     title: 'Atoms/Button',
     component: Button,
     parameters: {
       layout: 'centered',
     },
     tags: ['autodocs'],
     argTypes: {
       variant: {
         control: { type: 'select' },
         options: ['primary', 'secondary', 'danger', 'ghost'],
       },
       size: {
         control: { type: 'select' },
         options: ['sm', 'md', 'lg'],
       },
     },
   };

   export default meta;
   type Story = StoryObj<typeof meta>;

   export const Primary: Story = {
     args: {
       children: 'Button',
       variant: 'primary',
     },
   };

   export const WithIcon: Story = {
     args: {
       children: 'Add Item',
       variant: 'primary',
       leftIcon: <PlusIcon className="h-4 w-4" />,
     },
   };

   export const Loading: Story = {
     args: {
       children: 'Loading...',
       variant: 'primary',
       loading: true,
     },
   };

DataTable Component
~~~~~~~~~~~~~~~~~~~

**Advanced Table Implementation:**

.. code-block:: typescript

   // src/components/organisms/DataTable/DataTable.tsx
   import React from 'react';
   import {
     useReactTable,
     getCoreRowModel,
     getSortedRowModel,
     getFilteredRowModel,
     getPaginationRowModel,
     flexRender,
     type ColumnDef,
     type SortingState,
     type ColumnFiltersState,
   } from '@tanstack/react-table';
   import { ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
   import { Button } from '../../atoms/Button';
   import { Input } from '../../atoms/Input';

   interface DataTableProps<TData> {
     data: TData[];
     columns: ColumnDef<TData>[];
     loading?: boolean;
     searchable?: boolean;
     searchPlaceholder?: string;
     onRowClick?: (row: TData) => void;
   }

   export function DataTable<TData>({
     data,
     columns,
     loading = false,
     searchable = false,
     searchPlaceholder = 'Search...',
     onRowClick,
   }: DataTableProps<TData>) {
     const [sorting, setSorting] = React.useState<SortingState>([]);
     const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
     const [globalFilter, setGlobalFilter] = React.useState('');

     const table = useReactTable({
       data,
       columns,
       getCoreRowModel: getCoreRowModel(),
       getSortedRowModel: getSortedRowModel(),
       getFilteredRowModel: getFilteredRowModel(),
       getPaginationRowModel: getPaginationRowModel(),
       onSortingChange: setSorting,
       onColumnFiltersChange: setColumnFilters,
       onGlobalFilterChange: setGlobalFilter,
       state: {
         sorting,
         columnFilters,
         globalFilter,
       },
     });

     if (loading) {
       return <TableSkeleton />;
     }

     return (
       <div className="space-y-4">
         {searchable && (
           <div className="flex items-center space-x-2">
             <Input
               placeholder={searchPlaceholder}
               value={globalFilter}
               onChange={(e) => setGlobalFilter(e.target.value)}
               className="max-w-sm"
             />
           </div>
         )}

         <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
           <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
             <thead className="bg-gray-50 dark:bg-gray-800">
               {table.getHeaderGroups().map((headerGroup) => (
                 <tr key={headerGroup.id}>
                   {headerGroup.headers.map((header) => (
                     <th
                       key={header.id}
                       className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400"
                     >
                       {header.isPlaceholder ? null : (
                         <div
                           className={clsx(
                             'flex items-center space-x-1',
                             header.column.getCanSort() && 'cursor-pointer select-none'
                           )}
                           onClick={header.column.getToggleSortingHandler()}
                         >
                           <span>
                             {flexRender(header.column.columnDef.header, header.getContext())}
                           </span>
                           {header.column.getCanSort() && (
                             <span className="flex flex-col">
                               <ChevronUpIcon
                                 className={clsx(
                                   'h-3 w-3',
                                   header.column.getIsSorted() === 'asc'
                                     ? 'text-gray-900 dark:text-gray-100'
                                     : 'text-gray-400'
                                 )}
                               />
                               <ChevronDownIcon
                                 className={clsx(
                                   'h-3 w-3 -mt-1',
                                   header.column.getIsSorted() === 'desc'
                                     ? 'text-gray-900 dark:text-gray-100'
                                     : 'text-gray-400'
                                 )}
                               />
                             </span>
                           )}
                         </div>
                       )}
                     </th>
                   ))}
                 </tr>
               ))}
             </thead>
             <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-700">
               {table.getRowModel().rows.map((row) => (
                 <tr
                   key={row.id}
                   className={clsx(
                     'hover:bg-gray-50 dark:hover:bg-gray-800',
                     onRowClick && 'cursor-pointer'
                   )}
                   onClick={() => onRowClick?.(row.original)}
                 >
                   {row.getVisibleCells().map((cell) => (
                     <td
                       key={cell.id}
                       className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100"
                     >
                       {flexRender(cell.column.columnDef.cell, cell.getContext())}
                     </td>
                   ))}
                 </tr>
               ))}
             </tbody>
           </table>
         </div>

         {/* Pagination */}
         <div className="flex items-center justify-between">
           <div className="flex items-center space-x-2">
             <Button
               variant="secondary"
               size="sm"
               onClick={() => table.previousPage()}
               disabled={!table.getCanPreviousPage()}
             >
               Previous
             </Button>
             <Button
               variant="secondary"
               size="sm"
               onClick={() => table.nextPage()}
               disabled={!table.getCanNextPage()}
             >
               Next
             </Button>
           </div>
           <span className="text-sm text-gray-700 dark:text-gray-300">
             Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
           </span>
         </div>
       </div>
     );
   }

Form Components
~~~~~~~~~~~~~~~

**FormField with Validation:**

.. code-block:: typescript

   // src/components/molecules/FormField/FormField.tsx
   import React from 'react';
   import { useController, type Control, type FieldPath, type FieldValues } from 'react-hook-form';
   import { clsx } from 'clsx';
   import { ExclamationCircleIcon } from '@heroicons/react/24/outline';

   interface FormFieldProps<
     TFieldValues extends FieldValues = FieldValues,
     TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
   > {
     name: TName;
     control: Control<TFieldValues>;
     label: string;
     type?: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select';
     placeholder?: string;
     options?: Array<{ value: string; label: string }>;
     required?: boolean;
     disabled?: boolean;
     helperText?: string;
     className?: string;
   }

   export function FormField<
     TFieldValues extends FieldValues = FieldValues,
     TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
   >({
     name,
     control,
     label,
     type = 'text',
     placeholder,
     options,
     required = false,
     disabled = false,
     helperText,
     className,
   }: FormFieldProps<TFieldValues, TName>) {
     const {
       field,
       fieldState: { error },
     } = useController({
       name,
       control,
       rules: { required: required ? `${label} is required` : false },
     });

     const inputClasses = clsx(
       'block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset placeholder:text-gray-400 focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 dark:bg-gray-800 dark:text-gray-100',
       error
         ? 'ring-red-300 focus:ring-red-500 dark:ring-red-600'
         : 'ring-gray-300 focus:ring-primary-600 dark:ring-gray-600',
       disabled && 'bg-gray-50 text-gray-500 cursor-not-allowed dark:bg-gray-700'
     );

     const renderInput = () => {
       switch (type) {
         case 'textarea':
           return (
             <textarea
               {...field}
               placeholder={placeholder}
               disabled={disabled}
               className={clsx(inputClasses, 'min-h-[100px] resize-vertical')}
               rows={4}
             />
           );
         case 'select':
           return (
             <select {...field} disabled={disabled} className={inputClasses}>
               <option value="">Select {label}</option>
               {options?.map((option) => (
                 <option key={option.value} value={option.value}>
                   {option.label}
                 </option>
               ))}
             </select>
           );
         default:
           return (
             <input
               {...field}
               type={type}
               placeholder={placeholder}
               disabled={disabled}
               className={inputClasses}
             />
           );
       }
     };

     return (
       <div className={className}>
         <label htmlFor={field.name} className="block text-sm font-medium leading-6 text-gray-900 dark:text-gray-100">
           {label}
           {required && <span className="text-red-500 ml-1">*</span>}
         </label>
         <div className="mt-2 relative">
           {renderInput()}
           {error && (
             <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
               <ExclamationCircleIcon className="h-5 w-5 text-red-500" />
             </div>
           )}
         </div>
         {error && (
           <p className="mt-2 text-sm text-red-600 dark:text-red-400" id={`${field.name}-error`}>
             {error.message}
           </p>
         )}
         {helperText && !error && (
           <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">{helperText}</p>
         )}
       </div>
     );
   }

Accessibility Patterns
----------------------

ARIA Implementation
~~~~~~~~~~~~~~~~~~~

**Accessible Modal Component:**

.. code-block:: typescript

   // src/components/molecules/Modal/Modal.tsx
   import React from 'react';
   import { Dialog, Transition } from '@headlessui/react';
   import { XMarkIcon } from '@heroicons/react/24/outline';
   import { Button } from '../../atoms/Button';

   interface ModalProps {
     isOpen: boolean;
     onClose: () => void;
     title: string;
     children: React.ReactNode;
     size?: 'sm' | 'md' | 'lg' | 'xl';
     showCloseButton?: boolean;
   }

   export const Modal: React.FC<ModalProps> = ({
     isOpen,
     onClose,
     title,
     children,
     size = 'md',
     showCloseButton = true,
   }) => {
     const sizeClasses = {
       sm: 'max-w-md',
       md: 'max-w-lg',
       lg: 'max-w-2xl',
       xl: 'max-w-4xl',
     };

     return (
       <Transition appear show={isOpen} as={React.Fragment}>
         <Dialog as="div" className="relative z-50" onClose={onClose}>
           <Transition.Child
             as={React.Fragment}
             enter="ease-out duration-300"
             enterFrom="opacity-0"
             enterTo="opacity-100"
             leave="ease-in duration-200"
             leaveFrom="opacity-100"
             leaveTo="opacity-0"
           >
             <div className="fixed inset-0 bg-black bg-opacity-25" />
           </Transition.Child>

           <div className="fixed inset-0 overflow-y-auto">
             <div className="flex min-h-full items-center justify-center p-4 text-center">
               <Transition.Child
                 as={React.Fragment}
                 enter="ease-out duration-300"
                 enterFrom="opacity-0 scale-95"
                 enterTo="opacity-100 scale-100"
                 leave="ease-in duration-200"
                 leaveFrom="opacity-100 scale-100"
                 leaveTo="opacity-0 scale-95"
               >
                 <Dialog.Panel
                   className={clsx(
                     'w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all dark:bg-gray-800',
                     sizeClasses[size]
                   )}
                 >
                   <div className="flex items-center justify-between mb-4">
                     <Dialog.Title
                       as="h3"
                       className="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100"
                     >
                       {title}
                     </Dialog.Title>
                     {showCloseButton && (
                       <Button
                         variant="ghost"
                         size="sm"
                         onClick={onClose}
                         aria-label="Close modal"
                       >
                         <XMarkIcon className="h-5 w-5" />
                       </Button>
                     )}
                   </div>
                   {children}
                 </Dialog.Panel>
               </Transition.Child>
             </div>
           </div>
         </Dialog>
       </Transition>
     );
   };

**Keyboard Navigation:**

.. code-block:: typescript

   // src/hooks/useKeyboardNavigation.ts
   import { useEffect, useCallback } from 'react';

   interface UseKeyboardNavigationProps {
     isEnabled: boolean;
     onEscape?: () => void;
     onEnter?: () => void;
     onArrowUp?: () => void;
     onArrowDown?: () => void;
   }

   export const useKeyboardNavigation = ({
     isEnabled,
     onEscape,
     onEnter,
     onArrowUp,
     onArrowDown,
   }: UseKeyboardNavigationProps) => {
     const handleKeyDown = useCallback(
       (event: KeyboardEvent) => {
         if (!isEnabled) return;

         switch (event.key) {
           case 'Escape':
             onEscape?.();
             break;
           case 'Enter':
             onEnter?.();
             break;
           case 'ArrowUp':
             event.preventDefault();
             onArrowUp?.();
             break;
           case 'ArrowDown':
             event.preventDefault();
             onArrowDown?.();
             break;
         }
       },
       [isEnabled, onEscape, onEnter, onArrowUp, onArrowDown]
     );

     useEffect(() => {
       if (isEnabled) {
         document.addEventListener('keydown', handleKeyDown);
         return () => document.removeEventListener('keydown', handleKeyDown);
       }
     }, [isEnabled, handleKeyDown]);
   };

Performance Optimization
------------------------

**Memoization Patterns:**

.. code-block:: typescript

   // src/components/organisms/CVEList/CVEList.tsx
   import React, { useMemo } from 'react';
   import { CVECard } from '../CVECard';
   import type { CVE } from '../../../types/cve';

   interface CVEListProps {
     cves: CVE[];
     onCVEClick: (cve: CVE) => void;
     loading?: boolean;
   }

   export const CVEList = React.memo<CVEListProps>(({ cves, onCVEClick, loading }) => {
     const sortedCVEs = useMemo(() => {
       return [...cves].sort((a, b) => 
         new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime()
       );
     }, [cves]);

     const handleCVEClick = useCallback((cve: CVE) => {
       onCVEClick(cve);
     }, [onCVEClick]);

     if (loading) {
       return <CVEListSkeleton />;
     }

     return (
       <div className="space-y-4">
         {sortedCVEs.map((cve) => (
           <CVECard
             key={cve.id}
             cve={cve}
             onClick={handleCVEClick}
           />
         ))}
       </div>
     );
   });

   CVEList.displayName = 'CVEList';

Testing Patterns
----------------

**Component Testing Best Practices:**

.. code-block:: typescript

   // src/components/__tests__/DataTable.test.tsx
   import { render, screen, fireEvent, waitFor } from '@testing-library/react';
   import userEvent from '@testing-library/user-event';
   import { DataTable } from '../organisms/DataTable';

   const mockData = [
     { id: '1', name: 'Item 1', status: 'active' },
     { id: '2', name: 'Item 2', status: 'inactive' },
   ];

   const mockColumns = [
     { accessorKey: 'name', header: 'Name' },
     { accessorKey: 'status', header: 'Status' },
   ];

   describe('DataTable', () => {
     test('renders table with data', () => {
       render(<DataTable data={mockData} columns={mockColumns} />);
       
       expect(screen.getByText('Item 1')).toBeInTheDocument();
       expect(screen.getByText('Item 2')).toBeInTheDocument();
     });

     test('handles sorting', async () => {
       const user = userEvent.setup();
       render(<DataTable data={mockData} columns={mockColumns} />);
       
       const nameHeader = screen.getByText('Name');
       await user.click(nameHeader);
       
       // Verify sorting order changed
       const rows = screen.getAllByRole('row');
       expect(rows[1]).toHaveTextContent('Item 2');
       expect(rows[2]).toHaveTextContent('Item 1');
     });

     test('handles search', async () => {
       const user = userEvent.setup();
       render(<DataTable data={mockData} columns={mockColumns} searchable />);
       
       const searchInput = screen.getByPlaceholderText('Search...');
       await user.type(searchInput, 'Item 1');
       
       await waitFor(() => {
         expect(screen.getByText('Item 1')).toBeInTheDocument();
         expect(screen.queryByText('Item 2')).not.toBeInTheDocument();
       });
     });
   });

This guide provides the foundation for building consistent, accessible, and performant React components for the CVE Feed Service interface.
