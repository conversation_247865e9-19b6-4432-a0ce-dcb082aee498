System Architecture
==================

This document provides a comprehensive overview of the CVE Feed Service architecture, including the new Docker-based infrastructure, Traefik integration, service orchestration, and technical design decisions.

Overview
--------

The CVE Feed Service follows a modern microservices architecture pattern with Docker containerization, Traefik service discovery, and clear separation of concerns, designed for scalability, maintainability, and enterprise deployment.

**Architecture Principles**:
* **Microservices Architecture**: Containerized services with clear boundaries
* **Service Discovery**: Traefik-based routing and load balancing
* **Infrastructure as Code**: Docker Compose orchestration
* **Domain Management**: *.feedme.localhost service URL management
* **Separation of Concerns**: Clear boundaries between layers
* **Dependency Injection**: Loose coupling through DI
* **Async-First**: Non-blocking operations throughout
* **API-First**: RESTful API as the primary interface
* **Domain-Driven Design**: Business logic encapsulated in services
* **SOLID Principles**: Object-oriented design best practices
* **DevOps Integration**: Comprehensive monitoring and observability

Docker Infrastructure Architecture
----------------------------------

The CVE Feed Service is deployed as a comprehensive Docker-based infrastructure with Traefik service discovery and *.feedme.localhost domain management.

.. mermaid::

   graph TB
       subgraph "Traefik (Host)"
           T[Traefik Router<br/>Service Discovery]
       end

       subgraph "CVE Feed Docker Infrastructure"
           subgraph "Frontend Services"
               FE[Frontend App<br/>app.feedme.localhost<br/>Next.js + React]
               AD[Admin Dashboard<br/>admin.feedme.localhost<br/>React + Nginx]
               DOC[Documentation<br/>docs.feedme.localhost<br/>Sphinx + Nginx]
           end

           subgraph "Backend Services"
               API[API Service<br/>api.feedme.localhost<br/>FastAPI + Python]
               WK[Background Worker<br/>CVE Processing<br/>Python + Celery]
           end

           subgraph "Data Layer"
               PG[(PostgreSQL<br/>Primary Database)]
               RD[(Redis<br/>Cache + Sessions)]
           end

           subgraph "Monitoring Stack"
               PR[Prometheus<br/>metrics.feedme.localhost<br/>Metrics Collection]
               GR[Grafana<br/>dashboard.feedme.localhost<br/>Visualization]
           end

           subgraph "Development Tools"
               PGA[PgAdmin<br/>pgadmin.feedme.localhost<br/>Database Management]
               RC[Redis Commander<br/>redis.feedme.localhost<br/>Cache Management]
           end
       end

       subgraph "External Systems"
           NVD[NVD API<br/>CVE Data Source]
           CLIENT[API Clients<br/>External Integrations]
       end

       %% Traefik routing
       T --> FE
       T --> AD
       T --> DOC
       T --> API
       T --> PR
       T --> GR
       T --> PGA
       T --> RC

       %% Service connections
       FE --> API
       AD --> API
       CLIENT --> API

       %% Backend connections
       API --> PG
       API --> RD
       WK --> PG
       WK --> RD
       WK --> NVD

       %% Monitoring connections
       PR --> API
       PR --> FE
       PR --> PG
       PR --> RD
       GR --> PR

       %% Development tools
       PGA --> PG
       RC --> RD

Service Discovery and Load Balancing
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Traefik Configuration**:

.. code-block:: yaml

   # Docker Compose service labels for Traefik
   labels:
     - "traefik.enable=true"
     - "traefik.http.routers.cve-api.rule=Host(`api.feedme.localhost`)"
     - "traefik.http.routers.cve-api.entrypoints=web"
     - "traefik.http.services.cve-api.loadbalancer.server.port=8000"
     - "traefik.http.routers.cve-api.middlewares=api-cors"

**Domain Management**:

* **api.feedme.localhost** - Backend API service
* **app.feedme.localhost** - Main user interface
* **admin.feedme.localhost** - Administrative dashboard
* **docs.feedme.localhost** - Service documentation
* **metrics.feedme.localhost** - Prometheus metrics
* **dashboard.feedme.localhost** - Grafana monitoring

High-Level Service Architecture
-------------------------------

.. mermaid::

   graph TB
       subgraph "External Systems"
           NVD[NVD API]
           CLIENT[API Clients]
       end

       subgraph "CVE Feed Service Container"
           subgraph "API Layer"
               ROUTER[FastAPI Router]
               AUTH[Authentication]
               VALID[Validation]
               CORS[CORS Middleware]
           end

           subgraph "Business Logic Layer"
               APP_SVC[Application Service]
               COMP_SVC[Component Service]
               CVE_SVC[CVE Service]
               AUTH_SVC[Auth Service]
               INGEST_SVC[Ingestion Service]
               METRICS_SVC[Metrics Service]
           end

           subgraph "Data Access Layer"
               MODELS[SQLAlchemy Models]
               REPOS[Repository Pattern]
               CACHE_LAYER[Cache Layer]
           end
       end

       subgraph "Data Infrastructure"
           DB[(PostgreSQL Container)]
           CACHE[(Redis Container)]
       end

       subgraph "Background Processing"
           WORKER[Worker Container]
           SCHEDULER[Task Scheduler]
       end

       CLIENT --> ROUTER
       ROUTER --> CORS
       CORS --> AUTH
       AUTH --> AUTH_SVC
       ROUTER --> APP_SVC
       ROUTER --> COMP_SVC
       ROUTER --> CVE_SVC
       ROUTER --> METRICS_SVC

       APP_SVC --> MODELS
       COMP_SVC --> MODELS
       CVE_SVC --> MODELS
       AUTH_SVC --> MODELS
       METRICS_SVC --> MODELS

       MODELS --> DB
       CACHE_LAYER --> CACHE

       WORKER --> INGEST_SVC
       INGEST_SVC --> NVD
       INGEST_SVC --> MODELS
       SCHEDULER --> WORKER

       APP_SVC --> CACHE_LAYER
       CVE_SVC --> CACHE_LAYER

Container Orchestration Architecture
------------------------------------

Docker Compose Service Definition
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Core Services**:

.. code-block:: yaml

   # docker-compose.yml structure
   services:
     # Backend API
     api:
       build:
         context: .
         dockerfile: Dockerfile.api
       environment:
         - DATABASE_URL=postgresql+asyncpg://cve_user:password@postgres:5432/cve_feed_db
         - REDIS_URL=redis://:password@redis:6379/0
       labels:
         - "traefik.enable=true"
         - "traefik.http.routers.cve-api.rule=Host(`api.feedme.localhost`)"

     # Frontend Application
     frontend:
       build:
         context: ./frontend
         dockerfile: Dockerfile
       environment:
         - NEXT_PUBLIC_API_URL=https://api.feedme.localhost
       labels:
         - "traefik.enable=true"
         - "traefik.http.routers.cve-app.rule=Host(`app.feedme.localhost`)"

     # Database
     postgres:
       image: postgres:15-alpine
       environment:
         - POSTGRES_DB=cve_feed_db
         - POSTGRES_USER=cve_user
         - POSTGRES_PASSWORD=password
       volumes:
         - postgres_data:/var/lib/postgresql/data

**Service Dependencies**:

.. mermaid::

   graph TD
       A[API Service] --> B[PostgreSQL]
       A --> C[Redis]
       D[Frontend] --> A
       E[Admin] --> A
       F[Worker] --> B
       F --> C
       G[Prometheus] --> A
       H[Grafana] --> G

       subgraph "Health Checks"
           I[API Health] --> A
           J[DB Health] --> B
           K[Cache Health] --> C
       end

Container Deployment Flow
~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Dev as Developer
       participant Docker as Docker Engine
       participant Traefik as Traefik Router
       participant Services as Service Containers

       Dev->>Docker: docker-compose up --build
       Docker->>Docker: Build API Container
       Docker->>Docker: Build Frontend Container
       Docker->>Docker: Pull Database Images

       Docker->>Services: Start PostgreSQL
       Docker->>Services: Start Redis
       Docker->>Services: Start API Service
       Docker->>Services: Start Frontend
       Docker->>Services: Start Worker

       Services->>Traefik: Register Service Labels
       Traefik->>Traefik: Configure Routes
       Traefik->>Traefik: Setup Load Balancing

       Note over Traefik: Services available at *.feedme.localhost

       Dev->>Traefik: Access https://app.feedme.localhost
       Traefik->>Services: Route to Frontend Container
       Services-->>Dev: Application Response

Layered Architecture
-------------------

API Layer (Containerized)
~~~~~~~~~~~~~~~~~~~~~~~~~

**Responsibilities**:
* HTTP request/response handling in Docker container
* Input validation and serialization
* Authentication and authorization
* Error handling and formatting
* API documentation generation
* Health check endpoints for container orchestration

**Container Structure**:

.. code-block:: dockerfile

   # Multi-stage build for API container
   FROM python:3.11-slim as builder
   WORKDIR /app
   COPY requirements.txt .
   RUN pip install --no-cache-dir -r requirements.txt

   FROM python:3.11-slim as production
   WORKDIR /app
   COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
   COPY src/ ./src/
   EXPOSE 8000
   CMD ["uvicorn", "src.cve_feed_service.main:app", "--host", "0.0.0.0", "--port", "8000"]

**Key Components**:

.. code-block:: python

   # FastAPI router structure in container
   src/cve_feed_service/api/v1/
   ├── router.py              # Main API router
   ├── endpoints/
   │   ├── applications.py    # Application endpoints
   │   ├── components.py      # Component endpoints
   │   ├── cves.py           # CVE endpoints
   │   ├── auth.py           # Authentication endpoints
   │   └── health.py         # Container health checks
   └── dependencies.py       # Shared dependencies

**Design Patterns**:
* **Dependency Injection**: Services injected into endpoints
* **Request/Response Models**: Pydantic schemas for validation
* **Middleware Pattern**: Cross-cutting concerns (auth, logging, CORS)
* **Health Check Pattern**: Container readiness and liveness probes

Business Logic Layer (Distributed Services)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Responsibilities**:
* Core business logic implementation across containers
* Data validation and transformation
* Business rule enforcement
* Service orchestration between containers
* Transaction management with distributed data
* Inter-service communication

**Distributed Service Architecture**:

.. mermaid::

   classDiagram
       class ApplicationService {
           -db: AsyncSession
           -cache: CacheService
           -metrics: MetricsService
           +create_application(data) Application
           +get_application(id) Application
           +list_applications(filters) List[Application]
           +update_application(id, data) Application
           +delete_application(id) bool
           +get_application_metrics(id) Metrics
       }

       class ComponentService {
           -db: AsyncSession
           -cache: CacheService
           +create_component(app_id, data) Component
           +get_component(id) Component
           +list_components(app_id) List[Component]
           +create_cpe_mapping(comp_id, data) CPEMapping
           +bulk_import_components(data) List[Component]
       }

       class CVEService {
           -db: AsyncSession
           -cache: CacheService
           -worker_queue: WorkerQueue
           +get_cve_by_id(cve_id) CVE
           +list_cves(filters) List[CVE]
           +get_tailored_feed(app_id) List[CVE]
           +schedule_cve_sync() TaskResult
       }

       class WorkerService {
           -db: AsyncSession
           -nvd_client: NVDClient
           -metrics: MetricsService
           +sync_cve_data() SyncResult
           +process_cve_batch(batch) ProcessResult
           +update_metrics() MetricsResult
       }

       class MetricsService {
           -prometheus: PrometheusClient
           +record_api_request(endpoint, duration)
           +record_database_query(query, duration)
           +record_cache_hit_rate(service, rate)
           +export_metrics() MetricsData
       }

       ApplicationService --> ComponentService
       CVEService --> ComponentService
       CVEService --> WorkerService
       ApplicationService --> MetricsService
       CVEService --> MetricsService
       WorkerService --> MetricsService

**Service Communication Patterns**:

.. mermaid::

   sequenceDiagram
       participant API as API Container
       participant Worker as Worker Container
       participant DB as PostgreSQL
       participant Cache as Redis
       participant Metrics as Prometheus

       API->>Cache: Check Application Cache
       Cache-->>API: Cache Miss
       API->>DB: Query Application Data
       DB-->>API: Application Result
       API->>Cache: Store in Cache
       API->>Metrics: Record Query Time

       Note over API, Worker: Background CVE Sync
       API->>Worker: Schedule CVE Sync
       Worker->>DB: Update CVE Data
       Worker->>Metrics: Record Sync Metrics
       Worker-->>API: Sync Complete

**Service Patterns**:
* **Service Layer Pattern**: Encapsulates business logic
* **Unit of Work Pattern**: Transaction management
* **Repository Pattern**: Data access abstraction
* **Circuit Breaker Pattern**: Fault tolerance between services
* **Saga Pattern**: Distributed transaction management
* **Event Sourcing**: Audit trail and state reconstruction

Data Access Layer
~~~~~~~~~~~~~~~~

**Responsibilities**:
* Database schema definition
* ORM mapping and relationships
* Query optimization
* Data integrity constraints
* Migration management

**Model Architecture**:

.. mermaid::

   erDiagram
       User ||--o{ APIKey : has
       User ||--o{ Application : owns
       Application ||--o{ Component : contains
       Component ||--o{ CPEMapping : maps_to
       CVE ||--o{ CVECPEApplicability : applies_to
       CPEMapping ||--o{ CVECPEApplicability : matches
       
       User {
           uuid id PK
           string username UK
           string email UK
           string hashed_password
           string role
           boolean is_active
           datetime created_at
           datetime updated_at
           datetime deleted_at
       }
       
       Application {
           uuid id PK
           string name
           string description
           string environment
           string criticality
           datetime created_at
           datetime updated_at
           datetime deleted_at
       }
       
       Component {
           uuid id PK
           uuid application_id FK
           string name
           string version
           string vendor
           string component_type
           datetime created_at
           datetime updated_at
           datetime deleted_at
       }

**Data Patterns**:
* **Active Record Pattern**: Models with behavior
* **Soft Delete Pattern**: Logical deletion with timestamps
* **Audit Trail Pattern**: Created/updated timestamps
* **UUID Pattern**: Globally unique identifiers

Infrastructure Layer
~~~~~~~~~~~~~~~~~~~

**Responsibilities**:
* Database connection management
* Caching implementation
* Logging and monitoring
* Configuration management
* External service integration

**Infrastructure Components**:

.. code-block:: python

   # Infrastructure setup
   src/cve_feed_service/core/
   ├── config.py          # Configuration management
   ├── database.py        # Database setup
   ├── dependencies.py    # DI container
   ├── logging.py         # Structured logging
   └── cache.py          # Caching layer

Data Flow Architecture
---------------------

Request Processing Flow
~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Client
       participant Router
       participant Auth
       participant Service
       participant Model
       participant Database
       
       Client->>Router: HTTP Request
       Router->>Auth: Validate Authentication
       Auth-->>Router: User Context
       Router->>Service: Business Logic Call
       Service->>Model: Data Operation
       Model->>Database: SQL Query
       Database-->>Model: Query Result
       Model-->>Service: Domain Object
       Service-->>Router: Response Data
       Router-->>Client: HTTP Response

CVE Ingestion Flow
~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant CLI
       participant IngestionService
       participant NVDClient
       participant Database
       participant NVD_API
       
       CLI->>IngestionService: Start Ingestion
       IngestionService->>NVDClient: Get CVE Data
       NVDClient->>NVD_API: API Request
       NVD_API-->>NVDClient: CVE JSON
       NVDClient-->>IngestionService: Parsed CVEs
       
       loop For each CVE batch
           IngestionService->>Database: Bulk Insert/Update
           IngestionService->>Database: Process CPE Applicability
       end
       
       IngestionService-->>CLI: Ingestion Complete

Vulnerability Feed Generation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   flowchart TD
       A[API Request] --> B[Get Application]
       B --> C[Load Components]
       C --> D[Extract CPE Mappings]
       D --> E[Query CVE Database]
       E --> F[Join CPE Applicability]
       F --> G[Apply Filters]
       G --> H[Paginate Results]
       H --> I[Return CVE Feed]
       
       subgraph "Optimization"
           J[Index on CPE Strings]
           K[Eager Loading]
           L[Query Caching]
       end
       
       E --> J
       C --> K
       G --> L

Design Patterns
--------------

Dependency Injection
~~~~~~~~~~~~~~~~~~~

**Implementation**:

.. code-block:: python

   # Dependency injection setup
   from fastapi import Depends
   from sqlalchemy.ext.asyncio import AsyncSession
   
   from ..core.database import get_db
   from ..core.auth import get_current_user
   
   
   async def get_application_service(
       db: AsyncSession = Depends(get_db)
   ) -> ApplicationService:
       return ApplicationService(db)
   
   
   @router.post("/applications")
   async def create_application(
       data: ApplicationCreate,
       current_user: User = Depends(get_current_user),
       app_service: ApplicationService = Depends(get_application_service)
   ):
       return await app_service.create_application(data)

**Benefits**:
* Loose coupling between components
* Easy testing with mock dependencies
* Clear dependency relationships
* Flexible configuration

Repository Pattern
~~~~~~~~~~~~~~~~~

**Implementation**:

.. code-block:: python

   # Abstract repository
   from abc import ABC, abstractmethod
   from typing import List, Optional
   from uuid import UUID
   
   
   class ApplicationRepository(ABC):
       @abstractmethod
       async def create(self, application: Application) -> Application:
           pass
       
       @abstractmethod
       async def get_by_id(self, id: UUID) -> Optional[Application]:
           pass
       
       @abstractmethod
       async def list(self, filters: dict) -> List[Application]:
           pass
   
   
   # SQLAlchemy implementation
   class SQLApplicationRepository(ApplicationRepository):
       def __init__(self, db: AsyncSession):
           self.db = db
       
       async def create(self, application: Application) -> Application:
           self.db.add(application)
           await self.db.commit()
           await self.db.refresh(application)
           return application

Unit of Work Pattern
~~~~~~~~~~~~~~~~~~~

**Implementation**:

.. code-block:: python

   # Unit of Work for transaction management
   class UnitOfWork:
       def __init__(self, db: AsyncSession):
           self.db = db
           self._repositories = {}
       
       async def __aenter__(self):
           return self
       
       async def __aexit__(self, exc_type, exc_val, exc_tb):
           if exc_type:
               await self.rollback()
           else:
               await self.commit()
       
       async def commit(self):
           await self.db.commit()
       
       async def rollback(self):
           await self.db.rollback()
       
       @property
       def applications(self) -> ApplicationRepository:
           if 'applications' not in self._repositories:
               self._repositories['applications'] = SQLApplicationRepository(self.db)
           return self._repositories['applications']

Service Layer Pattern
~~~~~~~~~~~~~~~~~~~~

**Implementation**:

.. code-block:: python

   # Service encapsulates business logic
   class ApplicationService:
       def __init__(self, db: AsyncSession):
           self.db = db
       
       async def create_application(self, data: ApplicationCreate) -> Application:
           # Business logic: validate uniqueness
           existing = await self._get_by_name_and_env(data.name, data.environment)
           if existing:
               raise ValueError(f"Application '{data.name}' already exists")
           
           # Create application
           application = Application(**data.model_dump())
           self.db.add(application)
           await self.db.commit()
           await self.db.refresh(application)
           
           # Business logic: log creation
           logger.info("Application created", application_id=application.id)
           
           return application

Performance Architecture
------------------------

Database Optimization
~~~~~~~~~~~~~~~~~~~~~

**Indexing Strategy**:

.. code-block:: sql

   -- Application indexes
   CREATE INDEX idx_applications_name_env ON applications(name, environment);
   CREATE INDEX idx_applications_created_at ON applications(created_at);
   
   -- Component indexes
   CREATE INDEX idx_components_app_id ON components(application_id);
   CREATE INDEX idx_components_name_version ON components(name, version);
   
   -- CVE indexes
   CREATE INDEX idx_cves_severity ON cves(cvss_v3_severity);
   CREATE INDEX idx_cves_published_date ON cves(published_date);
   
   -- CPE applicability indexes
   CREATE INDEX idx_cpe_applicability_cpe_string ON cve_cpe_applicability(cpe_string);

**Query Optimization**:

.. code-block:: python

   # Eager loading to avoid N+1 queries
   from sqlalchemy.orm import selectinload
   
   async def get_application_with_components(self, app_id: UUID):
       query = select(Application).where(Application.id == app_id).options(
           selectinload(Application.components).selectinload(Component.cpe_mappings)
       )
       result = await self.db.execute(query)
       return result.scalar_one_or_none()

Caching Strategy
~~~~~~~~~~~~~~~

**Multi-Level Caching**:

.. mermaid::

   graph TD
       A[API Request] --> B{Application Cache}
       B -->|Hit| C[Return Cached Data]
       B -->|Miss| D[Query Database]
       D --> E[Cache Result]
       E --> F[Return Data]
       
       G[CVE Feed Request] --> H{Feed Cache}
       H -->|Hit| I[Return Cached Feed]
       H -->|Miss| J[Generate Feed]
       J --> K[Cache Feed]
       K --> L[Return Feed]

**Cache Implementation**:

.. code-block:: python

   # Redis caching layer
   import redis.asyncio as redis
   from typing import Optional
   
   
   class CacheService:
       def __init__(self, redis_client: redis.Redis):
           self.redis = redis_client
       
       async def get(self, key: str) -> Optional[str]:
           return await self.redis.get(key)
       
       async def set(self, key: str, value: str, ttl: int = 3600):
           await self.redis.setex(key, ttl, value)
       
       async def delete(self, key: str):
           await self.redis.delete(key)

Security Architecture
--------------------

Authentication Flow
~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Client
       participant API
       participant AuthService
       participant Database
       participant JWT
       
       Client->>API: Login Request
       API->>AuthService: Validate Credentials
       AuthService->>Database: Check User
       Database-->>AuthService: User Data
       AuthService->>JWT: Generate Token
       JWT-->>AuthService: JWT Token
       AuthService-->>API: Token Response
       API-->>Client: JWT Token
       
       Note over Client: Store token securely
       
       Client->>API: API Request + JWT
       API->>JWT: Validate Token
       JWT-->>API: User Claims
       API->>API: Process Request
       API-->>Client: Response

Authorization Model
~~~~~~~~~~~~~~~~~~

**Role-Based Access Control**:

.. code-block:: python

   # Permission decorators
   from functools import wraps
   from fastapi import HTTPException, status
   
   
   def require_role(required_role: str):
       def decorator(func):
           @wraps(func)
           async def wrapper(*args, current_user: User, **kwargs):
               if current_user.role != required_role:
                   raise HTTPException(
                       status_code=status.HTTP_403_FORBIDDEN,
                       detail="Insufficient permissions"
                   )
               return await func(*args, current_user=current_user, **kwargs)
           return wrapper
       return decorator
   
   
   @require_role("it_admin")
   async def create_user(user_data: UserCreate, current_user: User):
       # Only IT admins can create users
       pass

Error Handling Architecture
--------------------------

Exception Hierarchy
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Custom exception hierarchy
   class CVEFeedServiceException(Exception):
       """Base exception for CVE Feed Service."""
       pass
   
   
   class ValidationError(CVEFeedServiceException):
       """Validation error."""
       pass
   
   
   class NotFoundError(CVEFeedServiceException):
       """Resource not found error."""
       pass
   
   
   class ConflictError(CVEFeedServiceException):
       """Resource conflict error."""
       pass

Global Error Handler
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # FastAPI exception handlers
   from fastapi import FastAPI, Request
   from fastapi.responses import JSONResponse
   
   
   @app.exception_handler(ValidationError)
   async def validation_error_handler(request: Request, exc: ValidationError):
       return JSONResponse(
           status_code=422,
           content={
               "detail": str(exc),
               "type": "validation_error"
           }
       )
   
   
   @app.exception_handler(NotFoundError)
   async def not_found_error_handler(request: Request, exc: NotFoundError):
       return JSONResponse(
           status_code=404,
           content={
               "detail": str(exc),
               "type": "not_found_error"
           }
       )

Monitoring and Observability Architecture
------------------------------------------

The CVE Feed Service implements comprehensive monitoring and observability through containerized monitoring stack with Prometheus and Grafana.

Monitoring Stack Architecture
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Application Services"
           API[API Service<br/>Metrics Endpoint]
           FE[Frontend Service<br/>Performance Metrics]
           WK[Worker Service<br/>Job Metrics]
           DB[(PostgreSQL<br/>Database Metrics)]
           RD[(Redis<br/>Cache Metrics)]
       end

       subgraph "Monitoring Infrastructure"
           PR[Prometheus<br/>metrics.feedme.localhost<br/>Metrics Collection]
           GR[Grafana<br/>dashboard.feedme.localhost<br/>Visualization]
           AL[Alertmanager<br/>Alert Processing]
       end

       subgraph "Log Aggregation"
           ELK[ELK Stack<br/>Log Processing]
           FLUENTD[Fluentd<br/>Log Collection]
       end

       API --> PR
       FE --> PR
       WK --> PR
       DB --> PR
       RD --> PR

       PR --> GR
       PR --> AL

       API --> FLUENTD
       WK --> FLUENTD
       FLUENTD --> ELK

       GR --> AL

**Prometheus Configuration**:

.. code-block:: yaml

   # prometheus.yml
   global:
     scrape_interval: 15s
     evaluation_interval: 15s

   scrape_configs:
     - job_name: 'cve-api'
       static_configs:
         - targets: ['api:8000']
       metrics_path: '/metrics'
       scrape_interval: 10s

     - job_name: 'postgres'
       static_configs:
         - targets: ['postgres-exporter:9187']

     - job_name: 'redis'
       static_configs:
         - targets: ['redis-exporter:9121']

**Grafana Dashboard Configuration**:

.. code-block:: yaml

   # grafana/dashboards/cve-service.json
   {
     "dashboard": {
       "title": "CVE Feed Service Monitoring",
       "panels": [
         {
           "title": "API Request Rate",
           "type": "graph",
           "targets": [
             {
               "expr": "rate(http_requests_total[5m])",
               "legendFormat": "{{method}} {{endpoint}}"
             }
           ]
         },
         {
           "title": "Database Connections",
           "type": "singlestat",
           "targets": [
             {
               "expr": "pg_stat_database_numbackends",
               "legendFormat": "Active Connections"
             }
           ]
         }
       ]
     }
   }

Structured Logging in Containers
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Structured logging setup for containerized services
   import structlog
   import logging
   import sys


   # Configure structured logging for containers
   def setup_logging():
       structlog.configure(
           processors=[
               structlog.stdlib.filter_by_level,
               structlog.stdlib.add_logger_name,
               structlog.stdlib.add_log_level,
               structlog.stdlib.PositionalArgumentsFormatter(),
               structlog.processors.TimeStamper(fmt="iso"),
               structlog.processors.StackInfoRenderer(),
               structlog.processors.format_exc_info,
               structlog.processors.UnicodeDecoder(),
               structlog.processors.JSONRenderer()
           ],
           context_class=dict,
           logger_factory=structlog.stdlib.LoggerFactory(),
           wrapper_class=structlog.stdlib.BoundLogger,
           cache_logger_on_first_use=True,
       )

       # Configure root logger for container output
       logging.basicConfig(
           format="%(message)s",
           stream=sys.stdout,
           level=logging.INFO,
       )


   # Usage in containerized services
   logger = structlog.get_logger(__name__)


   async def create_application(self, data: ApplicationCreate):
       logger.info(
           "Creating application",
           name=data.name,
           environment=data.environment,
           container_id=os.environ.get('HOSTNAME'),
           service_name='api'
       )

       try:
           application = await self._create_application(data)
           logger.info(
               "Application created successfully",
               application_id=application.id,
               duration_ms=duration,
               container_id=os.environ.get('HOSTNAME')
           )
           return application
       except Exception as e:
           logger.error(
               "Application creation failed",
               error=str(e),
               name=data.name,
               container_id=os.environ.get('HOSTNAME'),
               exc_info=True
           )
           raise

Distributed Metrics Collection
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Metrics collection across containers
   from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry
   import time


   # Container-specific metrics registry
   registry = CollectorRegistry()

   # Define distributed metrics
   request_count = Counter(
       'http_requests_total',
       'Total HTTP requests',
       ['method', 'endpoint', 'container', 'service'],
       registry=registry
   )

   request_duration = Histogram(
       'http_request_duration_seconds',
       'HTTP request duration',
       ['endpoint', 'container', 'service'],
       registry=registry
   )

   database_connections = Gauge(
       'database_connections_active',
       'Active database connections',
       ['container', 'service'],
       registry=registry
   )

   cache_hit_rate = Gauge(
       'cache_hit_rate_percent',
       'Cache hit rate percentage',
       ['cache_type', 'container', 'service'],
       registry=registry
   )


   # Middleware for distributed metrics collection
   @app.middleware("http")
   async def metrics_middleware(request: Request, call_next):
       start_time = time.time()
       container_id = os.environ.get('HOSTNAME', 'unknown')
       service_name = 'api'

       response = await call_next(request)

       duration = time.time() - start_time

       # Record metrics with container context
       request_count.labels(
           method=request.method,
           endpoint=request.url.path,
           container=container_id,
           service=service_name
       ).inc()

       request_duration.labels(
           endpoint=request.url.path,
           container=container_id,
           service=service_name
       ).observe(duration)

       return response


   # Health check endpoint for container orchestration
   @app.get("/health")
   async def health_check():
       return {
           "status": "healthy",
           "container_id": os.environ.get('HOSTNAME'),
           "service": "api",
           "timestamp": time.time(),
           "version": "1.0.0"
       }


   # Metrics endpoint for Prometheus scraping
   @app.get("/metrics")
   async def metrics():
       from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
       return Response(
           generate_latest(registry),
           media_type=CONTENT_TYPE_LATEST
       )

Scaling and Deployment Architecture
-----------------------------------

Horizontal Scaling Strategy
~~~~~~~~~~~~~~~~~~~~~~~~~~

The Docker-based architecture supports horizontal scaling through container replication and load balancing.

.. mermaid::

   graph TB
       subgraph "Load Balancer Layer"
           T[Traefik<br/>Load Balancer]
       end

       subgraph "API Service Cluster"
           API1[API Container 1<br/>api.feedme.localhost]
           API2[API Container 2<br/>api.feedme.localhost]
           API3[API Container 3<br/>api.feedme.localhost]
       end

       subgraph "Worker Service Cluster"
           W1[Worker Container 1]
           W2[Worker Container 2]
           W3[Worker Container 3]
       end

       subgraph "Frontend Service Cluster"
           FE1[Frontend Container 1<br/>app.feedme.localhost]
           FE2[Frontend Container 2<br/>app.feedme.localhost]
       end

       subgraph "Data Layer (Shared)"
           PG[(PostgreSQL<br/>Primary + Replicas)]
           RD[(Redis Cluster)]
       end

       T --> API1
       T --> API2
       T --> API3
       T --> FE1
       T --> FE2

       API1 --> PG
       API2 --> PG
       API3 --> PG
       API1 --> RD
       API2 --> RD
       API3 --> RD

       W1 --> PG
       W2 --> PG
       W3 --> PG
       W1 --> RD
       W2 --> RD
       W3 --> RD

**Docker Compose Scaling**:

.. code-block:: bash

   # Scale API service to 3 replicas
   docker-compose up --scale api=3

   # Scale worker service to 2 replicas
   docker-compose up --scale worker=2

   # Scale frontend service to 2 replicas
   docker-compose up --scale frontend=2

**Auto-scaling Configuration**:

.. code-block:: yaml

   # docker-compose.yml with resource limits
   services:
     api:
       deploy:
         replicas: 3
         resources:
           limits:
             cpus: '0.5'
             memory: 512M
           reservations:
             cpus: '0.25'
             memory: 256M
         restart_policy:
           condition: on-failure
           delay: 5s
           max_attempts: 3

Multi-Environment Deployment
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Development Environment"
           DEV_T[Traefik Dev]
           DEV_API[API Dev<br/>api-dev.feedme.localhost]
           DEV_FE[Frontend Dev<br/>app-dev.feedme.localhost]
           DEV_DB[(PostgreSQL Dev)]
       end

       subgraph "Staging Environment"
           STAGE_T[Traefik Staging]
           STAGE_API[API Staging<br/>api-staging.feedme.localhost]
           STAGE_FE[Frontend Staging<br/>app-staging.feedme.localhost]
           STAGE_DB[(PostgreSQL Staging)]
       end

       subgraph "Production Environment"
           PROD_T[Traefik Production]
           PROD_API[API Production<br/>api.feedme.localhost]
           PROD_FE[Frontend Production<br/>app.feedme.localhost]
           PROD_DB[(PostgreSQL Production<br/>High Availability)]
       end

       DEV_T --> DEV_API
       DEV_T --> DEV_FE
       DEV_API --> DEV_DB

       STAGE_T --> STAGE_API
       STAGE_T --> STAGE_FE
       STAGE_API --> STAGE_DB

       PROD_T --> PROD_API
       PROD_T --> PROD_FE
       PROD_API --> PROD_DB

**Environment-Specific Configuration**:

.. code-block:: yaml

   # docker-compose.override.yml (Development)
   services:
     api:
       environment:
         - DEBUG=true
         - LOG_LEVEL=DEBUG
       volumes:
         - ./src:/app/src:ro
       command: uvicorn src.cve_feed_service.main:app --reload --host 0.0.0.0

   # docker-compose.prod.yml (Production)
   services:
     api:
       environment:
         - DEBUG=false
         - LOG_LEVEL=INFO
       deploy:
         replicas: 3
         resources:
           limits:
             memory: 1G
             cpus: '1.0'

Deployment Pipeline Architecture
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Dev as Developer
       participant Git as Git Repository
       participant CI as CI/CD Pipeline
       participant Registry as Container Registry
       participant Deploy as Deployment System
       participant Prod as Production Environment

       Dev->>Git: Push Code Changes
       Git->>CI: Trigger Build Pipeline

       CI->>CI: Run Tests
       CI->>CI: Build Docker Images
       CI->>CI: Security Scanning
       CI->>Registry: Push Images

       CI->>Deploy: Trigger Deployment
       Deploy->>Registry: Pull Latest Images
       Deploy->>Prod: Rolling Update

       Prod->>Prod: Health Checks
       Prod->>Deploy: Deployment Status
       Deploy->>Dev: Deployment Complete

**CI/CD Pipeline Configuration**:

.. code-block:: yaml

   # .github/workflows/deploy.yml
   name: Deploy CVE Feed Service

   on:
     push:
       branches: [main]

   jobs:
     test:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3
         - name: Run Tests
           run: |
             docker-compose -f docker-compose.test.yml up --abort-on-container-exit

     build:
       needs: test
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3
         - name: Build Images
           run: |
             docker-compose build
             docker-compose push

     deploy:
       needs: build
       runs-on: ubuntu-latest
       steps:
         - name: Deploy to Production
           run: |
             docker-compose -f docker-compose.prod.yml up -d

Security Architecture
--------------------

Container Security
~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Security Layers"
           subgraph "Network Security"
               FW[Firewall Rules]
               TLS[TLS Termination]
               CORS[CORS Policies]
           end

           subgraph "Container Security"
               USER[Non-root Users]
               SCAN[Image Scanning]
               SECRETS[Secret Management]
           end

           subgraph "Application Security"
               AUTH[JWT Authentication]
               RBAC[Role-Based Access]
               VALID[Input Validation]
           end

           subgraph "Data Security"
               ENCRYPT[Data Encryption]
               BACKUP[Secure Backups]
               AUDIT[Audit Logging]
           end
       end

**Container Security Configuration**:

.. code-block:: dockerfile

   # Secure Dockerfile practices
   FROM python:3.11-slim as builder

   # Create non-root user
   RUN groupadd -r cveuser && useradd -r -g cveuser cveuser

   # Install dependencies as root
   COPY requirements.txt .
   RUN pip install --no-cache-dir -r requirements.txt

   FROM python:3.11-slim as production

   # Create non-root user in production image
   RUN groupadd -r cveuser && useradd -r -g cveuser cveuser

   # Copy dependencies and application
   COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
   COPY --chown=cveuser:cveuser src/ /app/src/

   # Switch to non-root user
   USER cveuser
   WORKDIR /app

   # Security headers and configuration
   ENV PYTHONPATH=/app
   ENV PYTHONDONTWRITEBYTECODE=1
   ENV PYTHONUNBUFFERED=1

   EXPOSE 8000
   CMD ["uvicorn", "src.cve_feed_service.main:app", "--host", "0.0.0.0", "--port", "8000"]

Next Steps
----------

* :doc:`testing` - Testing architecture and strategies including infrastructure testing
* :doc:`contributing` - Contributing guidelines and code standards
* :doc:`../services/index` - Detailed service documentation
* :doc:`../deployment/index` - Deployment architecture and strategies
* :doc:`../user-guide/index` - User guide for the new Docker infrastructure
* :doc:`react-setup` - React frontend setup and integration
