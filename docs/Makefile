# Enhanced Makefile for CVE Feed Service Documentation
#

# You can set these variables from the command line, and also
# from the environment for the first two.
SPHINXOPTS    ?= -W --keep-going
SPHINXBUILD  ?= sphinx-build
SOURCEDIR    = .
BUILDDIR     = _build
PYTHON       ?= python3
PIP          ?= pip

# Colors for output
RED=\033[0;31m
GREEN=\033[0;32m
YELLOW=\033[1;33m
BLUE=\033[0;34m
NC=\033[0m # No Color

# Put it first so that "make" without argument is like "make help".
help:
	@echo "$(BLUE)CVE Feed Service Documentation Build System$(NC)"
	@echo ""
	@echo "$(GREEN)Available targets:$(NC)"
	@echo "  $(YELLOW)html$(NC)        Build HTML documentation"
	@echo "  $(YELLOW)pdf$(NC)         Build PDF documentation"
	@echo "  $(YELLOW)epub$(NC)        Build EPUB documentation"
	@echo "  $(YELLOW)clean$(NC)       Clean build directory"
	@echo "  $(YELLOW)livehtml$(NC)    Build and serve with live reload"
	@echo "  $(YELLOW)install$(NC)     Install documentation dependencies"
	@echo "  $(YELLOW)check$(NC)       Check documentation for errors"
	@echo "  $(YELLOW)linkcheck$(NC)   Check external links"
	@echo "  $(YELLOW)spelling$(NC)    Check spelling (requires sphinxcontrib-spelling)"
	@echo "  $(YELLOW)coverage$(NC)    Check documentation coverage"
	@echo "  $(YELLOW)serve$(NC)       Serve built documentation locally"
	@echo ""
	@$(SPHINXBUILD) -M help "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

# Install documentation dependencies
install:
	@echo "$(BLUE)Installing documentation dependencies...$(NC)"
	$(PIP) install -r requirements.txt
	@echo "$(GREEN)Dependencies installed successfully!$(NC)"

# Build HTML documentation with enhanced options
html:
	@echo "$(BLUE)Building HTML documentation...$(NC)"
	@$(SPHINXBUILD) -b html "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS) $(O)
	@echo "$(GREEN)HTML documentation built successfully!$(NC)"
	@echo "Open $(BUILDDIR)/html/index.html in your browser"

# Build PDF documentation
pdf:
	@echo "$(BLUE)Building PDF documentation...$(NC)"
	@$(SPHINXBUILD) -b latex "$(SOURCEDIR)" "$(BUILDDIR)/latex" $(SPHINXOPTS) $(O)
	@cd "$(BUILDDIR)/latex" && make all-pdf
	@echo "$(GREEN)PDF documentation built successfully!$(NC)"
	@echo "PDF available at $(BUILDDIR)/latex/cvefeedservice.pdf"

# Build EPUB documentation
epub:
	@echo "$(BLUE)Building EPUB documentation...$(NC)"
	@$(SPHINXBUILD) -b epub "$(SOURCEDIR)" "$(BUILDDIR)/epub" $(SPHINXOPTS) $(O)
	@echo "$(GREEN)EPUB documentation built successfully!$(NC)"

# Clean build directory
clean:
	@echo "$(YELLOW)Cleaning build directory...$(NC)"
	@rm -rf "$(BUILDDIR)"
	@echo "$(GREEN)Build directory cleaned!$(NC)"

# Live reload development server
livehtml: install
	@echo "$(BLUE)Starting live reload server...$(NC)"
	@echo "$(YELLOW)Documentation will be available at http://localhost:8000$(NC)"
	@echo "$(YELLOW)Press Ctrl+C to stop the server$(NC)"
	@$(PYTHON) -c "import livereload; server = livereload.Server(); server.watch('.', livereload.shell('make html')); server.serve(root='$(BUILDDIR)/html', port=8000)"

# Check documentation for errors and warnings
check:
	@echo "$(BLUE)Checking documentation for errors...$(NC)"
	@$(SPHINXBUILD) -b html -W --keep-going "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS) $(O)
	@echo "$(GREEN)Documentation check completed!$(NC)"

# Check external links
linkcheck:
	@echo "$(BLUE)Checking external links...$(NC)"
	@$(SPHINXBUILD) -b linkcheck "$(SOURCEDIR)" "$(BUILDDIR)/linkcheck" $(SPHINXOPTS) $(O)
	@echo "$(GREEN)Link check completed!$(NC)"

# Check spelling (requires sphinxcontrib-spelling)
spelling:
	@echo "$(BLUE)Checking spelling...$(NC)"
	@$(SPHINXBUILD) -b spelling "$(SOURCEDIR)" "$(BUILDDIR)/spelling" $(SPHINXOPTS) $(O)
	@echo "$(GREEN)Spelling check completed!$(NC)"

# Check documentation coverage
coverage:
	@echo "$(BLUE)Checking documentation coverage...$(NC)"
	@$(SPHINXBUILD) -b coverage "$(SOURCEDIR)" "$(BUILDDIR)/coverage" $(SPHINXOPTS) $(O)
	@echo "$(GREEN)Coverage check completed!$(NC)"
	@echo "Coverage report available at $(BUILDDIR)/coverage/python.txt"

# Serve built documentation locally
serve:
	@echo "$(BLUE)Serving documentation at http://localhost:8080$(NC)"
	@echo "$(YELLOW)Press Ctrl+C to stop the server$(NC)"
	@cd "$(BUILDDIR)/html" && $(PYTHON) -m http.server 8080

# Build all formats
all: html pdf epub
	@echo "$(GREEN)All documentation formats built successfully!$(NC)"

# Development workflow - clean, install, and build
dev: clean install html
	@echo "$(GREEN)Development build completed!$(NC)"

# Production build with all checks
production: clean install check linkcheck html pdf
	@echo "$(GREEN)Production build completed successfully!$(NC)"

# Quick build for development
quick:
	@echo "$(BLUE)Quick build (HTML only)...$(NC)"
	@$(SPHINXBUILD) -b html "$(SOURCEDIR)" "$(BUILDDIR)/html" -q $(SPHINXOPTS) $(O)
	@echo "$(GREEN)Quick build completed!$(NC)"

.PHONY: help install html pdf epub clean livehtml check linkcheck spelling coverage serve all dev production quick Makefile

# Catch-all target: route all unknown targets to Sphinx using the new
# "make mode" option.  $(O) is meant as a shortcut for $(SPHINXOPTS).
%: Makefile
	@$(SPHINXBUILD) -M $@ "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

# Custom targets
clean:
	rm -rf $(BUILDDIR)/*

livehtml:
	sphinx-autobuild -b html $(SOURCEDIR) $(BUILDDIR)/html

# Build and serve documentation locally
serve: html
	cd $(BUILDDIR)/html && python -m http.server 8080
