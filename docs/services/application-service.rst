Application Service
==================

The Application Service is responsible for managing application inventory within the CVE Feed Service. It provides comprehensive CRUD operations for applications and handles the business logic around application lifecycle management.

Overview
--------

**Module**: ``src.cve_feed_service.services.application_service``

**Primary Class**: ``ApplicationService``

**Purpose**: Manages application entities including creation, retrieval, updates, and soft deletion while enforcing business rules and maintaining data integrity.

**Key Features**:
* Application CRUD operations
* Name/environment uniqueness validation
* Soft deletion with cascade to components
* Filtering and pagination support
* Structured logging and error handling

Class Architecture
------------------

.. mermaid::

   classDiagram
       class ApplicationService {
           -db: AsyncSession
           +__init__(db: AsyncSession)
           +create_application(data: ApplicationCreate) Application
           +get_application(id: UUID, include_components: bool) Optional[Application]
           +list_applications(skip: int, limit: int, environment: str, criticality: str) List[Application]
           +update_application(id: UUID, data: ApplicationUpdate) Application
           +delete_application(id: UUID) bool
       }
       
       class Application {
           +id: UUID
           +name: str
           +description: Optional[str]
           +version: Optional[str]
           +owner: Optional[str]
           +environment: Optional[str]
           +criticality: Optional[str]
           +components: List[Component]
           +created_at: datetime
           +updated_at: datetime
           +deleted_at: Optional[datetime]
       }
       
       class ApplicationCreate {
           +name: str
           +description: Optional[str]
           +version: Optional[str]
           +owner: Optional[str]
           +environment: Optional[str]
           +criticality: Optional[str]
       }
       
       class ApplicationUpdate {
           +name: Optional[str]
           +description: Optional[str]
           +version: Optional[str]
           +owner: Optional[str]
           +environment: Optional[str]
           +criticality: Optional[str]
       }
       
       ApplicationService --> Application
       ApplicationService --> ApplicationCreate
       ApplicationService --> ApplicationUpdate

Core Methods
------------

create_application()
~~~~~~~~~~~~~~~~~~~~

Creates a new application with validation and uniqueness checks.

**Signature**:

.. code-block:: python

   async def create_application(
       self, 
       application_data: ApplicationCreate
   ) -> Application

**Business Logic**:

.. mermaid::

   flowchart TD
       A[Start] --> B[Validate Input Data]
       B --> C[Check Name/Environment Uniqueness]
       C --> D{Duplicate Found?}
       D -->|Yes| E[Raise ValueError]
       D -->|No| F[Create Application Entity]
       F --> G[Add to Database Session]
       G --> H[Commit Transaction]
       H --> I[Refresh Entity]
       I --> J[Log Success]
       J --> K[Return Application]
       
       E --> L[End with Error]
       K --> M[End]

**Example Usage**:

.. code-block:: python

   from schemas.application import ApplicationCreate
   
   app_data = ApplicationCreate(
       name="Customer Portal",
       environment="production",
       criticality="high",
       owner="Platform Team"
   )
   
   application = await service.create_application(app_data)

**Error Conditions**:
* ``ValueError``: Application with same name/environment already exists
* ``ValidationError``: Invalid input data
* ``DatabaseError``: Database transaction failure

get_application()
~~~~~~~~~~~~~~~~~

Retrieves a single application by ID with optional component loading.

**Signature**:

.. code-block:: python

   async def get_application(
       self, 
       application_id: UUID, 
       include_components: bool = False
   ) -> Optional[Application]

**Query Strategy**:

.. mermaid::

   graph TD
       A[Start Query] --> B[Base Query: Applications]
       B --> C[Filter by ID]
       C --> D[Filter by Not Deleted]
       D --> E{Include Components?}
       E -->|Yes| F[Add selectinload for Components]
       F --> G[Add selectinload for CPE Mappings]
       G --> H[Execute Query]
       E -->|No| H
       H --> I[Return Result]

**Performance Considerations**:
* Uses ``selectinload`` for efficient eager loading
* Avoids N+1 query problems when loading components
* Includes CPE mappings when components are requested

list_applications()
~~~~~~~~~~~~~~~~~~~

Lists applications with filtering and pagination support.

**Signature**:

.. code-block:: python

   async def list_applications(
       self,
       skip: int = 0,
       limit: int = 100,
       environment: Optional[str] = None,
       criticality: Optional[str] = None,
   ) -> List[Application]

**Filtering Logic**:

.. mermaid::

   flowchart TD
       A[Base Query] --> B[Filter: Not Deleted]
       B --> C{Environment Filter?}
       C -->|Yes| D[Add Environment Filter]
       C -->|No| E{Criticality Filter?}
       D --> E
       E -->|Yes| F[Add Criticality Filter]
       E -->|No| G[Apply Pagination]
       F --> G
       G --> H[Order by Created Date DESC]
       H --> I[Execute Query]
       I --> J[Return Results]

update_application()
~~~~~~~~~~~~~~~~~~~~

Updates an existing application with partial update support.

**Signature**:

.. code-block:: python

   async def update_application(
       self, 
       application_id: UUID, 
       application_data: ApplicationUpdate
   ) -> Application

**Update Process**:

.. mermaid::

   sequenceDiagram
       participant Service
       participant Database
       participant Validator
       
       Service->>Database: Get existing application
       Database-->>Service: Application entity
       
       Service->>Validator: Check name/environment conflicts
       Validator->>Database: Query for duplicates
       Database-->>Validator: Conflict check result
       Validator-->>Service: Validation result
       
       Service->>Service: Apply field updates
       Service->>Database: Commit changes
       Database-->>Service: Updated entity
       Service->>Service: Log update
       Service-->>Service: Return updated application

delete_application()
~~~~~~~~~~~~~~~~~~~~

Performs soft deletion of an application and cascades to components.

**Signature**:

.. code-block:: python

   async def delete_application(self, application_id: UUID) -> bool

**Deletion Cascade**:

.. mermaid::

   graph TD
       A[Get Application] --> B[Check if Exists]
       B --> C{Application Found?}
       C -->|No| D[Return False]
       C -->|Yes| E[Soft Delete Application]
       E --> F[Iterate Components]
       F --> G[Soft Delete Component]
       G --> H[Iterate CPE Mappings]
       H --> I[Soft Delete CPE Mapping]
       I --> J{More CPE Mappings?}
       J -->|Yes| H
       J -->|No| K{More Components?}
       K -->|Yes| F
       K -->|No| L[Commit Transaction]
       L --> M[Log Deletion]
       M --> N[Return True]

Business Rules
--------------

Uniqueness Constraints
~~~~~~~~~~~~~~~~~~~~~~

Applications must be unique by name within an environment:

.. code-block:: python

   # Valid: Same name, different environments
   app1 = Application(name="MyApp", environment="staging")
   app2 = Application(name="MyApp", environment="production")
   
   # Invalid: Same name and environment
   app3 = Application(name="MyApp", environment="staging")  # Conflict!

Validation Rules
~~~~~~~~~~~~~~~~

* **Name**: Required, 1-255 characters
* **Environment**: Optional, max 50 characters
* **Criticality**: Optional, max 20 characters
* **Version**: Optional, max 100 characters
* **Owner**: Optional, max 255 characters

Soft Deletion Policy
~~~~~~~~~~~~~~~~~~~~

When an application is deleted:

1. Application ``deleted_at`` timestamp is set
2. All associated components are soft deleted
3. All CPE mappings for those components are soft deleted
4. Historical data is preserved for audit trails

Error Handling
--------------

Exception Types
~~~~~~~~~~~~~~~

.. code-block:: python

   # Duplicate application
   raise ValueError(
       f"Application '{name}' already exists in environment '{env}'"
   )
   
   # Application not found (returns None instead of exception)
   application = await service.get_application(invalid_id)
   # application is None

Logging Strategy
~~~~~~~~~~~~~~~~

The service uses structured logging for all operations:

.. code-block:: python

   # Success logging
   logger.info(
       "Creating application", 
       name=application_data.name,
       environment=application_data.environment
   )
   
   # Error logging
   logger.error(
       "Failed to create application",
       name=application_data.name,
       error=str(e)
   )

Integration Patterns
--------------------

Dependency Injection
~~~~~~~~~~~~~~~~~~~~

The service is injected into API endpoints:

.. code-block:: python

   from fastapi import Depends
   from ..core.dependencies import get_db
   from ..services.application_service import ApplicationService
   
   async def get_application_service(
       db: AsyncSession = Depends(get_db)
   ) -> ApplicationService:
       return ApplicationService(db)
   
   @router.post("/applications")
   async def create_application(
       app_data: ApplicationCreate,
       service: ApplicationService = Depends(get_application_service)
   ):
       return await service.create_application(app_data)

Component Relationship
~~~~~~~~~~~~~~~~~~~~~~

Applications have a one-to-many relationship with components:

.. code-block:: python

   # Get application with all components
   app = await service.get_application(app_id, include_components=True)
   
   for component in app.components:
       print(f"Component: {component.name} v{component.version}")
       for cpe_mapping in component.cpe_mappings:
           print(f"  CPE: {cpe_mapping.cpe_string}")

Testing Considerations
----------------------

Unit Test Example
~~~~~~~~~~~~~~~~~

.. code-block:: python

   import pytest
   from unittest.mock import AsyncMock
   
   @pytest.fixture
   async def mock_db():
       return AsyncMock()
   
   @pytest.fixture
   async def application_service(mock_db):
       return ApplicationService(mock_db)
   
   async def test_create_application_success(application_service, mock_db):
       # Setup
       app_data = ApplicationCreate(name="Test App")
       mock_db.execute.return_value.scalar_one_or_none.return_value = None
       
       # Execute
       result = await application_service.create_application(app_data)
       
       # Verify
       assert result.name == "Test App"
       mock_db.add.assert_called_once()
       mock_db.commit.assert_called_once()

Performance Optimization
------------------------

Query Optimization
~~~~~~~~~~~~~~~~~~

* Uses appropriate database indexes on frequently queried fields
* Implements eager loading to avoid N+1 queries
* Supports pagination for large result sets

Memory Management
~~~~~~~~~~~~~~~~~

* Services are stateless and don't hold references to entities
* Database sessions are properly managed and closed
* Large result sets use streaming where appropriate

Next Steps
----------

* :doc:`component-service` - Managing application components
* :doc:`cve-service` - Querying vulnerabilities for applications
* :doc:`service-interactions` - How services work together
