Service Interactions
====================

This document describes how the various services in the CVE Feed Service Docker infrastructure interact with each other, including containerized service communication, Traefik routing, data flow patterns, and integration scenarios across the distributed architecture.

Overview
--------

The CVE Feed Service follows a modern microservices architecture with Docker containers, where services interact through well-defined interfaces, HTTP APIs, and shared data stores. Understanding these interactions is crucial for system maintenance, debugging, scaling, and extending functionality in the containerized environment.

**Interaction Types**:
* **Container-to-Container**: Direct communication between Docker containers
* **Traefik-Routed**: External access through Traefik service discovery
* **Database Connections**: Shared PostgreSQL database access
* **Cache Coordination**: Redis-based caching and session management
* **Background Processing**: Asynchronous worker task coordination
* **Monitoring Integration**: Metrics collection and health monitoring
* **Event-Driven**: Services that react to events or changes
* **Shared Resources**: Services that share database sessions or external APIs

Docker Infrastructure Service Dependency Graph
-----------------------------------------------

.. mermaid::

   graph TB
       subgraph "External Systems"
           NVD[NVD API<br/>External CVE Data]
           CLIENT[External Clients<br/>API Consumers]
       end

       subgraph "Traefik Service Discovery"
           T[Traefik Router<br/>Load Balancer<br/>Service Discovery]
       end

       subgraph "Frontend Services (Containers)"
           FE[Frontend Container<br/>app.feedme.localhost<br/>Next.js + React]
           AD[Admin Container<br/>admin.feedme.localhost<br/>React Dashboard]
           DOC[Documentation Container<br/>docs.feedme.localhost<br/>Sphinx]
       end

       subgraph "Backend Services (Containers)"
           API[API Container<br/>api.feedme.localhost<br/>FastAPI + Python]
           WK[Worker Container<br/>Background Processing<br/>CVE Sync + Tasks]
       end

       subgraph "Data Infrastructure (Containers)"
           PG[(PostgreSQL Container<br/>Primary Database)]
           RD[(Redis Container<br/>Cache + Sessions)]
       end

       subgraph "Monitoring Stack (Containers)"
           PR[Prometheus Container<br/>metrics.feedme.localhost<br/>Metrics Collection]
           GR[Grafana Container<br/>dashboard.feedme.localhost<br/>Visualization]
       end

       subgraph "Development Tools (Containers)"
           PGA[PgAdmin Container<br/>pgadmin.feedme.localhost<br/>DB Management]
           RC[Redis Commander<br/>redis.feedme.localhost<br/>Cache Management]
       end

       %% External connections
       CLIENT --> T

       %% Traefik routing
       T --> FE
       T --> AD
       T --> DOC
       T --> API
       T --> PR
       T --> GR
       T --> PGA
       T --> RC

       %% Frontend to backend
       FE --> API
       AD --> API

       %% Backend to data
       API --> PG
       API --> RD
       WK --> PG
       WK --> RD
       WK --> NVD

       %% Monitoring connections
       PR --> API
       PR --> FE
       PR --> PG
       PR --> RD
       GR --> PR

       %% Development tools
       PGA --> PG
       RC --> RD

       %% Service dependencies within API container
       subgraph "API Container Internal Services"
           AS[Application Service]
           CS[Component Service]
           CVS[CVE Service]
           CIS[CVE Ingestion Service]
           AUS[Auth Service]
           MS[Metrics Service]
       end

       API --> AS
       API --> CS
       API --> CVS
       API --> AUS
       API --> MS

       CVS -.-> AS
       CVS -.-> CS
       AS -.-> CS
       CIS --> CVS

Container-to-Container Interaction Patterns
--------------------------------------------

User Authentication Flow (Full Stack)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Complete authentication flow across the Docker infrastructure:

.. mermaid::

   sequenceDiagram
       participant User as User Browser
       participant Traefik as Traefik Router
       participant Frontend as Frontend Container
       participant API as API Container
       participant Auth as Auth Service
       participant DB as PostgreSQL Container
       participant Redis as Redis Container

       User->>Traefik: Access app.feedme.localhost
       Traefik->>Frontend: Route to Frontend Container
       Frontend-->>User: Login Page

       User->>Frontend: Submit Credentials
       Frontend->>Traefik: POST to api.feedme.localhost/auth/login
       Traefik->>API: Route to API Container

       API->>Auth: Validate Credentials
       Auth->>DB: Query User Table
       DB-->>Auth: User Record
       Auth->>Auth: Verify Password Hash
       Auth->>Redis: Store Session Data
       Auth-->>API: JWT Token + User Info

       API-->>Traefik: Authentication Response
       Traefik-->>Frontend: JWT Token
       Frontend->>Frontend: Store Token in Browser
       Frontend-->>User: Redirect to Dashboard

       Note over User, Redis: User is now authenticated across all services

Admin Dashboard Workflow
~~~~~~~~~~~~~~~~~~~~~~~~

Administrative operations across multiple containers:

.. mermaid::

   sequenceDiagram
       participant Admin as Admin User
       participant Traefik as Traefik Router
       participant AdminUI as Admin Container
       participant API as API Container
       participant AppSvc as Application Service
       participant CompSvc as Component Service
       participant DB as PostgreSQL Container
       participant Metrics as Prometheus Container

       Admin->>Traefik: Access admin.feedme.localhost
       Traefik->>AdminUI: Route to Admin Container
       AdminUI-->>Admin: Admin Dashboard

       Admin->>AdminUI: View System Metrics
       AdminUI->>Traefik: GET api.feedme.localhost/admin/metrics
       Traefik->>API: Route to API Container
       API->>Metrics: Query Prometheus Metrics
       Metrics-->>API: System Statistics
       API-->>AdminUI: Metrics Data
       AdminUI-->>Admin: Display Metrics Dashboard

       Admin->>AdminUI: Create New Application
       AdminUI->>Traefik: POST api.feedme.localhost/applications
       Traefik->>API: Route to API Container
       API->>AppSvc: Create Application
       AppSvc->>DB: Insert Application Record
       DB-->>AppSvc: Application Created
       AppSvc-->>API: Application Response
       API-->>AdminUI: Success Response
       AdminUI-->>Admin: Application Created Confirmation

CVE Feed Generation Workflow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Complete CVE feed generation across the distributed architecture:

.. mermaid::

   sequenceDiagram
       participant User as End User
       participant Traefik as Traefik Router
       participant Frontend as Frontend Container
       participant API as API Container
       participant CVESvc as CVE Service
       participant AppSvc as Application Service
       participant CompSvc as Component Service
       participant DB as PostgreSQL Container
       participant Redis as Redis Container

       User->>Traefik: Request CVE Feed for Application
       Traefik->>Frontend: Route to Frontend Container
       Frontend->>Traefik: GET api.feedme.localhost/cves/feed/{app_id}
       Traefik->>API: Route to API Container

       API->>Redis: Check Feed Cache
       Redis-->>API: Cache Miss

       API->>CVESvc: Generate Tailored Feed
       CVESvc->>AppSvc: Get Application Details
       AppSvc->>DB: Query Application + Components
       DB-->>AppSvc: Application with Components
       AppSvc-->>CVESvc: Application Data

       CVESvc->>CompSvc: Extract CPE Mappings
       CompSvc->>DB: Query CPE Mappings
       DB-->>CompSvc: CPE Data
       CompSvc-->>CVESvc: CPE Mappings

       CVESvc->>DB: Query CVEs by CPE Applicability
       DB-->>CVESvc: Matching CVEs
       CVESvc->>CVESvc: Apply Filters & Pagination
       CVESvc->>Redis: Cache Generated Feed
       CVESvc-->>API: Tailored CVE Feed

       API-->>Traefik: CVE Feed Response
       Traefik-->>Frontend: CVE Data
       Frontend-->>User: Display CVE Feed

Application-Component Relationship
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The Application Service and Component Service work together to manage inventory:

.. mermaid::

   sequenceDiagram
       participant API
       participant ApplicationService
       participant ComponentService
       participant Database
       
       Note over API: Delete Application Request
       API->>ApplicationService: delete_application(app_id)
       ApplicationService->>Database: Get application with components
       Database-->>ApplicationService: Application + components
       
       loop For each component
           ApplicationService->>ComponentService: delete_component(comp_id)
           ComponentService->>Database: Soft delete component + CPE mappings
       end
       
       ApplicationService->>Database: Soft delete application
       ApplicationService-->>API: Deletion complete

CVE Feed Generation
~~~~~~~~~~~~~~~~~~~

The CVE Service coordinates with Application and Component services to generate tailored feeds:

.. mermaid::

   sequenceDiagram
       participant API
       participant CVEService
       participant ApplicationService
       participant ComponentService
       participant Database
       
       API->>CVEService: get_tailored_cve_feed(app_id)
       CVEService->>ApplicationService: get_application(app_id, include_components=True)
       ApplicationService->>Database: Query application with components
       Database-->>ApplicationService: Application data
       ApplicationService-->>CVEService: Application with components
       
       CVEService->>CVEService: Extract CPE mappings
       CVEService->>Database: Query CVEs by CPE applicability
       Database-->>CVEService: Matching CVEs
       CVEService-->>API: Tailored CVE feed

Background CVE Processing Workflow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Automated CVE data synchronization using the Worker container:

.. mermaid::

   sequenceDiagram
       participant Scheduler as Task Scheduler
       participant Worker as Worker Container
       participant API as API Container
       participant NVD as NVD API
       participant DB as PostgreSQL Container
       participant Redis as Redis Container
       participant Prometheus as Prometheus Container

       Note over Scheduler: Daily CVE Sync Triggered
       Scheduler->>Worker: Start CVE Sync Task
       Worker->>Redis: Set Task Status: RUNNING
       Worker->>Prometheus: Record Task Start Metric

       Worker->>NVD: Request Latest CVE Data
       loop Paginated CVE Requests
           NVD-->>Worker: CVE Batch (JSON)
           Worker->>Worker: Parse & Validate CVE Data
           Worker->>DB: Bulk Insert/Update CVEs
           Worker->>Redis: Update Progress Counter
       end

       Worker->>DB: Process CPE Applicability Rules
       Worker->>DB: Update CVE Statistics
       Worker->>Redis: Clear Application Feed Caches
       Worker->>Redis: Set Task Status: COMPLETED
       Worker->>Prometheus: Record Task Completion Metrics

       Note over API: Next API requests get fresh CVE data

       alt Task Failure
           Worker->>Redis: Set Task Status: FAILED
           Worker->>Prometheus: Record Task Failure Metrics
           Worker->>Worker: Schedule Retry
       end

System Monitoring and Health Checks
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Comprehensive health monitoring across all containers:

.. mermaid::

   sequenceDiagram
       participant Monitor as Monitoring System
       participant Prometheus as Prometheus Container
       participant API as API Container
       participant Frontend as Frontend Container
       participant DB as PostgreSQL Container
       participant Redis as Redis Container
       participant Worker as Worker Container
       participant Grafana as Grafana Container

       loop Every 15 seconds
           Monitor->>Prometheus: Scrape Metrics

           Prometheus->>API: GET /health
           API->>DB: Test Database Connection
           API->>Redis: Test Cache Connection
           API-->>Prometheus: Health Status + Metrics

           Prometheus->>Frontend: GET /health
           Frontend-->>Prometheus: Frontend Health Status

           Prometheus->>DB: Query Database Metrics
           DB-->>Prometheus: Connection Pool, Query Stats

           Prometheus->>Redis: Query Cache Metrics
           Redis-->>Prometheus: Memory Usage, Hit Rates

           Prometheus->>Worker: GET /health
           Worker-->>Prometheus: Task Queue Status
       end

       Prometheus->>Grafana: Provide Metrics Data
       Grafana->>Grafana: Update Dashboards

       alt Service Unhealthy
           Prometheus->>Prometheus: Trigger Alert
           Prometheus->>Monitor: Send Alert Notification
       end

Container Scaling Workflow
~~~~~~~~~~~~~~~~~~~~~~~~~

Dynamic scaling based on load and metrics:

.. mermaid::

   sequenceDiagram
       participant LoadBalancer as Traefik Load Balancer
       participant Prometheus as Prometheus Container
       participant Docker as Docker Engine
       participant API1 as API Container 1
       participant API2 as API Container 2 (New)
       participant DB as PostgreSQL Container

       Note over Prometheus: High CPU/Memory Usage Detected
       Prometheus->>Docker: Scale API Service (Replicas: 2)
       Docker->>Docker: Create New API Container
       Docker->>API2: Start API Container 2

       API2->>DB: Initialize Database Connection Pool
       API2->>API2: Register Health Check Endpoint
       API2-->>Docker: Container Ready

       Docker->>LoadBalancer: Update Service Discovery
       LoadBalancer->>LoadBalancer: Add API2 to Load Balancer Pool

       Note over LoadBalancer: Traffic now distributed across API1 and API2

       LoadBalancer->>API1: Route Request 1
       LoadBalancer->>API2: Route Request 2

       API1-->>LoadBalancer: Response 1
       API2-->>LoadBalancer: Response 2

       Note over Prometheus: Load decreased, scale down when appropriate

Cross-Service Data Flow
-----------------------

Application Lifecycle
~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   flowchart TD
       A[Create Application] --> B[Application Service]
       B --> C[Store in Database]
       C --> D[Add Components]
       D --> E[Component Service]
       E --> F[Create CPE Mappings]
       F --> G[Component Service]
       G --> H[Query Vulnerabilities]
       H --> I[CVE Service]
       I --> J[Generate Tailored Feed]

**Data Flow Steps**:

1. **Application Creation**: Application Service validates and stores application metadata
2. **Component Addition**: Component Service manages software inventory within applications
3. **CPE Mapping**: Component Service links components to standardized vulnerability identifiers
4. **Vulnerability Correlation**: CVE Service uses CPE mappings to find relevant vulnerabilities
5. **Feed Generation**: CVE Service provides tailored vulnerability information

Component Update Cascade
~~~~~~~~~~~~~~~~~~~~~~~~

When components are updated, it affects vulnerability feeds:

.. mermaid::

   graph TD
       A[Component Version Update] --> B[Component Service]
       B --> C[Update Component Record]
       C --> D[Check CPE Mapping Validity]
       D --> E{CPE Needs Update?}
       E -->|Yes| F[Update CPE Mapping]
       E -->|No| G[CVE Feed Automatically Updated]
       F --> G
       G --> H[Next API Request Gets New Results]

Shared Resource Management
--------------------------

Database Session Sharing
~~~~~~~~~~~~~~~~~~~~~~~~

All services share database sessions through dependency injection:

.. code-block:: python

   # Dependency injection pattern
   async def get_application_service(
       db: AsyncSession = Depends(get_db)
   ) -> ApplicationService:
       return ApplicationService(db)
   
   async def get_component_service(
       db: AsyncSession = Depends(get_db)
   ) -> ComponentService:
       return ComponentService(db)
   
   # Services share the same database session within a request
   @router.delete("/applications/{app_id}")
   async def delete_application(
       app_id: UUID,
       app_service: ApplicationService = Depends(get_application_service),
       comp_service: ComponentService = Depends(get_component_service)
   ):
       # Both services use the same database session
       # Ensuring transactional consistency
       return await app_service.delete_application(app_id)

Configuration Sharing
~~~~~~~~~~~~~~~~~~~~~

Services share configuration through a centralized settings object:

.. code-block:: python

   from ..core.config import get_settings
   
   class NVDAPIClient:
       def __init__(self):
           settings = get_settings()
           self.api_key = settings.nvd_api_key
           self.rate_limit = settings.nvd_rate_limit_per_minute
   
   class CVEIngestionService:
       def __init__(self, db: AsyncSession):
           self.db = db
           settings = get_settings()
           self.batch_size = settings.cve_batch_size

Error Handling and Recovery Patterns
------------------------------------

Distributed Error Propagation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Error handling across multiple containers with proper logging and recovery:

.. mermaid::

   sequenceDiagram
       participant User as User Browser
       participant Traefik as Traefik Router
       participant Frontend as Frontend Container
       participant API as API Container
       participant AppSvc as Application Service
       participant DB as PostgreSQL Container
       participant Redis as Redis Container
       participant Prometheus as Prometheus Container

       User->>Traefik: Request Application Data
       Traefik->>Frontend: Route to Frontend
       Frontend->>Traefik: API Call to api.feedme.localhost
       Traefik->>API: Route to API Container

       API->>AppSvc: Get Application
       AppSvc->>DB: Query Application

       alt Database Connection Error
           DB-->>AppSvc: Connection Timeout
           AppSvc->>AppSvc: Log Error with Trace ID
           AppSvc->>Prometheus: Record Database Error Metric
           AppSvc->>Redis: Check Application Cache

           alt Cache Hit
               Redis-->>AppSvc: Cached Application Data
               AppSvc-->>API: Stale but Valid Data
               API-->>Frontend: Response with Cache Warning
               Frontend-->>User: Display Data with Warning
           else Cache Miss
               AppSvc-->>API: Database Unavailable Error
               API->>Prometheus: Record API Error Metric
               API-->>Frontend: 503 Service Unavailable
               Frontend-->>User: Error Page with Retry Option
           end
       else Database Success
           DB-->>AppSvc: Application Data
           AppSvc->>Redis: Update Cache
           AppSvc-->>API: Fresh Application Data
           API-->>Frontend: Success Response
           Frontend-->>User: Display Application Data
       end

Container Failure Recovery
~~~~~~~~~~~~~~~~~~~~~~~~~

Automatic recovery when containers fail:

.. mermaid::

   sequenceDiagram
       participant Monitor as Health Monitor
       participant Docker as Docker Engine
       participant Traefik as Traefik Router
       participant API1 as API Container 1 (Failed)
       participant API2 as API Container 2 (Healthy)
       participant DB as PostgreSQL Container

       Note over API1: Container becomes unhealthy
       Monitor->>API1: Health Check Request
       API1-->>Monitor: No Response (Container Down)

       Monitor->>Docker: Container API1 is unhealthy
       Docker->>Docker: Mark API1 as failed
       Docker->>Traefik: Remove API1 from load balancer

       Traefik->>Traefik: Update routing rules
       Note over Traefik: All traffic now routes to API2

       Docker->>Docker: Restart API1 container
       Docker->>API1: Start new API1 instance
       API1->>DB: Initialize database connections
       API1->>API1: Run health checks
       API1-->>Docker: Container healthy

       Docker->>Traefik: Add API1 back to load balancer
       Traefik->>Traefik: Distribute traffic across API1 and API2

       Note over Monitor: System fully recovered

Circuit Breaker Pattern
~~~~~~~~~~~~~~~~~~~~~~

Preventing cascade failures between services:

.. mermaid::

   sequenceDiagram
       participant API as API Container
       participant CircuitBreaker as Circuit Breaker
       participant ExtService as External Service (NVD)
       participant Cache as Redis Container
       participant Metrics as Prometheus Container

       loop Normal Operation
           API->>CircuitBreaker: Call External Service
           CircuitBreaker->>ExtService: Forward Request
           ExtService-->>CircuitBreaker: Success Response
           CircuitBreaker-->>API: Success Response
           CircuitBreaker->>Metrics: Record Success
       end

       Note over ExtService: External service starts failing

       loop Failure Detection
           API->>CircuitBreaker: Call External Service
           CircuitBreaker->>ExtService: Forward Request
           ExtService-->>CircuitBreaker: Timeout/Error
           CircuitBreaker->>CircuitBreaker: Increment Failure Count
           CircuitBreaker->>Metrics: Record Failure
           CircuitBreaker-->>API: Error Response
       end

       Note over CircuitBreaker: Failure threshold reached
       CircuitBreaker->>CircuitBreaker: Open Circuit

       loop Circuit Open
           API->>CircuitBreaker: Call External Service
           CircuitBreaker->>Cache: Try Cache Fallback
           Cache-->>CircuitBreaker: Cached Data (if available)
           CircuitBreaker-->>API: Cached Response or Fast Fail
           CircuitBreaker->>Metrics: Record Circuit Open
       end

       Note over CircuitBreaker: After timeout period
       CircuitBreaker->>CircuitBreaker: Half-Open Circuit
       API->>CircuitBreaker: Test Request
       CircuitBreaker->>ExtService: Single Test Request
       ExtService-->>CircuitBreaker: Success Response
       CircuitBreaker->>CircuitBreaker: Close Circuit
       CircuitBreaker->>Metrics: Record Recovery

**Error Handling Strategy**:

.. code-block:: python

   class ApplicationService:
       async def delete_application(self, app_id: UUID) -> bool:
           try:
               # Get application with components
               app = await self.get_application(app_id, include_components=True)
               if not app:
                   return False
               
               # Delete components through Component Service
               for component in app.components:
                   success = await component_service.delete_component(component.id)
                   if not success:
                       logger.warning("Failed to delete component", component_id=component.id)
               
               # Delete application
               app.soft_delete()
               await self.db.commit()
               return True
               
           except Exception as e:
               logger.error("Application deletion failed", app_id=app_id, error=str(e))
               await self.db.rollback()
               raise

Transaction Management
~~~~~~~~~~~~~~~~~~~~~~

Services coordinate database transactions for consistency:

.. code-block:: python

   async def complex_operation_with_multiple_services():
       async with database.transaction():
           try:
               # All operations within the same transaction
               app = await application_service.create_application(app_data)
               component = await component_service.create_component(app.id, comp_data)
               mapping = await component_service.create_cpe_mapping(component.id, cpe_data)
               
               # Transaction commits automatically if no exceptions
               return app
               
           except Exception:
               # Transaction rolls back automatically on exception
               raise

Performance Optimization Patterns
---------------------------------

Eager Loading Coordination
~~~~~~~~~~~~~~~~~~~~~~~~~~

Services coordinate to minimize database queries:

.. code-block:: python

   # Application Service loads components and CPE mappings together
   async def get_application(self, app_id: UUID, include_components: bool = False):
       query = select(Application).where(Application.id == app_id)
       
       if include_components:
           # Eager load components and their CPE mappings
           query = query.options(
               selectinload(Application.components).selectinload(
                   Component.cpe_mappings
               )
           )
       
       result = await self.db.execute(query)
       return result.scalar_one_or_none()

Caching Coordination
~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[API Request] --> B{Cache Hit?}
       B -->|Yes| C[Return Cached Data]
       B -->|No| D[Query Services]
       D --> E[Application Service]
       D --> F[Component Service]
       D --> G[CVE Service]
       E --> H[Combine Results]
       F --> H
       G --> H
       H --> I[Cache Results]
       I --> J[Return to Client]

Testing Service Interactions
----------------------------

Integration Test Patterns
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def test_application_deletion_cascade():
       """Test that deleting an application cascades to components and CPE mappings."""
       # Setup: Create application with components and CPE mappings
       app = await application_service.create_application(test_app_data)
       component = await component_service.create_component(app.id, test_comp_data)
       cpe_mapping = await component_service.create_cpe_mapping(component.id, test_cpe_data)
       
       # Execute: Delete application
       result = await application_service.delete_application(app.id)
       assert result is True
       
       # Verify: All related entities are soft deleted
       deleted_app = await application_service.get_application(app.id)
       assert deleted_app is None
       
       deleted_component = await component_service.get_component(component.id)
       assert deleted_component is None

Mock Service Dependencies
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @pytest.fixture
   async def mock_component_service():
       mock_service = AsyncMock(spec=ComponentService)
       mock_service.delete_component.return_value = True
       return mock_service
   
   async def test_application_service_with_mock_dependencies(mock_component_service):
       """Test application service with mocked component service."""
       # Inject mock dependency
       app_service = ApplicationService(mock_db)
       app_service.component_service = mock_component_service
       
       # Test business logic without component service complexity
       result = await app_service.delete_application(test_app_id)
       
       # Verify interactions
       mock_component_service.delete_component.assert_called()

Monitoring Service Interactions
-------------------------------

Distributed Tracing
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import structlog
   from uuid import uuid4
   
   async def create_application_with_components(app_data, components_data):
       trace_id = str(uuid4())
       logger = structlog.get_logger().bind(trace_id=trace_id)
       
       logger.info("Starting application creation workflow")
       
       try:
           # Create application
           logger.info("Creating application")
           app = await application_service.create_application(app_data)
           
           # Create components
           for comp_data in components_data:
               logger.info("Creating component", component_name=comp_data.name)
               component = await component_service.create_component(app.id, comp_data)
           
           logger.info("Application creation workflow completed", app_id=app.id)
           return app
           
       except Exception as e:
           logger.error("Application creation workflow failed", error=str(e))
           raise

Performance Metrics
~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[Service Interaction Metrics] --> B[Request Duration]
       A --> C[Cross-Service Call Count]
       A --> D[Database Query Count]
       A --> E[Error Rates]
       
       B --> F[Total Request Time]
       B --> G[Service-to-Service Time]
       C --> H[Service Dependency Calls]
       D --> I[N+1 Query Detection]
       E --> J[Service Error Correlation]

Best Practices
--------------

Service Design Principles
~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Loose Coupling**: Services should minimize direct dependencies
2. **High Cohesion**: Related functionality should be grouped together
3. **Clear Interfaces**: Service contracts should be well-defined
4. **Error Isolation**: Errors in one service shouldn't cascade unnecessarily
5. **Transaction Boundaries**: Understand where transactions begin and end

Integration Guidelines
~~~~~~~~~~~~~~~~~~~~~

1. **Use Dependency Injection**: Inject services rather than creating them directly
2. **Handle Errors Gracefully**: Don't let service errors propagate without context
3. **Log Service Interactions**: Include trace IDs for debugging
4. **Test Integration Points**: Focus testing on service boundaries
5. **Monitor Performance**: Track cross-service call patterns

Deployment and Integration Patterns
-----------------------------------

Blue-Green Deployment Workflow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Zero-downtime deployment using container orchestration:

.. mermaid::

   sequenceDiagram
       participant CI as CI/CD Pipeline
       participant Registry as Container Registry
       participant Docker as Docker Engine
       participant Traefik as Traefik Router
       participant BlueEnv as Blue Environment (Current)
       participant GreenEnv as Green Environment (New)
       participant DB as PostgreSQL Container

       CI->>Registry: Push New Container Images
       CI->>Docker: Deploy to Green Environment
       Docker->>GreenEnv: Start New Containers
       GreenEnv->>DB: Connect to Database
       GreenEnv->>GreenEnv: Run Health Checks
       GreenEnv-->>Docker: All Services Healthy

       Docker->>CI: Green Environment Ready
       CI->>Traefik: Switch Traffic to Green
       Traefik->>Traefik: Update Routing Rules

       Note over Traefik: Traffic now routes to Green Environment

       CI->>Docker: Monitor Green Environment
       Docker->>GreenEnv: Health Check
       GreenEnv-->>Docker: Healthy

       alt Deployment Success
           CI->>Docker: Shutdown Blue Environment
           Docker->>BlueEnv: Stop Old Containers
           Note over BlueEnv: Blue Environment Decommissioned
       else Deployment Failure
           CI->>Traefik: Rollback to Blue Environment
           Traefik->>Traefik: Restore Original Routing
           CI->>Docker: Shutdown Green Environment
           Note over BlueEnv: Rollback Complete
       end

Multi-Environment Service Communication
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Service interactions across development, staging, and production:

.. mermaid::

   graph TB
       subgraph "Development Environment"
           DEV_T[Traefik Dev<br/>*.dev.feedme.localhost]
           DEV_API[API Dev Container]
           DEV_FE[Frontend Dev Container]
           DEV_DB[(PostgreSQL Dev)]
           DEV_REDIS[(Redis Dev)]
       end

       subgraph "Staging Environment"
           STAGE_T[Traefik Staging<br/>*.staging.feedme.localhost]
           STAGE_API[API Staging Container]
           STAGE_FE[Frontend Staging Container]
           STAGE_DB[(PostgreSQL Staging)]
           STAGE_REDIS[(Redis Staging)]
       end

       subgraph "Production Environment"
           PROD_T[Traefik Production<br/>*.feedme.localhost]
           PROD_API[API Production Container]
           PROD_FE[Frontend Production Container]
           PROD_DB[(PostgreSQL Production)]
           PROD_REDIS[(Redis Production)]
       end

       subgraph "Shared Services"
           NVD[NVD API<br/>External CVE Data]
           MONITORING[Monitoring Stack<br/>Cross-Environment]
       end

       %% Environment connections
       DEV_T --> DEV_API
       DEV_T --> DEV_FE
       DEV_API --> DEV_DB
       DEV_API --> DEV_REDIS

       STAGE_T --> STAGE_API
       STAGE_T --> STAGE_FE
       STAGE_API --> STAGE_DB
       STAGE_API --> STAGE_REDIS

       PROD_T --> PROD_API
       PROD_T --> PROD_FE
       PROD_API --> PROD_DB
       PROD_API --> PROD_REDIS

       %% Shared service connections
       DEV_API --> NVD
       STAGE_API --> NVD
       PROD_API --> NVD

       DEV_API --> MONITORING
       STAGE_API --> MONITORING
       PROD_API --> MONITORING

API Integration Patterns
~~~~~~~~~~~~~~~~~~~~~~~

External system integration through the containerized API:

.. mermaid::

   sequenceDiagram
       participant ExtSystem as External System
       participant APIGateway as API Gateway
       participant Traefik as Traefik Router
       participant API as API Container
       participant Auth as Auth Service
       participant RateLimit as Rate Limiter
       participant CVESvc as CVE Service
       participant DB as PostgreSQL Container

       ExtSystem->>APIGateway: API Request with API Key
       APIGateway->>Traefik: Forward to api.feedme.localhost
       Traefik->>API: Route to API Container

       API->>Auth: Validate API Key
       Auth->>DB: Check API Key Validity
       DB-->>Auth: API Key Valid
       Auth-->>API: Authentication Success

       API->>RateLimit: Check Rate Limits
       RateLimit->>RateLimit: Validate Request Count
       RateLimit-->>API: Rate Limit OK

       API->>CVESvc: Process Business Logic
       CVESvc->>DB: Query CVE Data
       DB-->>CVESvc: CVE Results
       CVESvc-->>API: Processed Response

       API->>RateLimit: Update Request Counter
       API-->>Traefik: API Response
       Traefik-->>APIGateway: Forward Response
       APIGateway-->>ExtSystem: Final Response

Container Orchestration Best Practices
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Service Communication Guidelines**:

1. **Use Service Names**: Containers communicate using Docker service names
2. **Health Checks**: Implement comprehensive health check endpoints
3. **Graceful Shutdown**: Handle SIGTERM signals for clean container stops
4. **Resource Limits**: Set appropriate CPU and memory limits
5. **Environment Variables**: Use environment-based configuration
6. **Secrets Management**: Use Docker secrets for sensitive data
7. **Network Isolation**: Use Docker networks for service isolation
8. **Logging Standards**: Implement structured logging for container environments

**Monitoring and Observability**:

.. code-block:: python

   # Container-aware service implementation
   import os
   import signal
   import structlog
   from prometheus_client import Counter, Histogram

   # Container metadata
   CONTAINER_ID = os.environ.get('HOSTNAME', 'unknown')
   SERVICE_NAME = os.environ.get('SERVICE_NAME', 'api')

   # Metrics with container labels
   request_counter = Counter(
       'http_requests_total',
       'Total HTTP requests',
       ['method', 'endpoint', 'container', 'service']
   )

   # Structured logging with container context
   logger = structlog.get_logger().bind(
       container_id=CONTAINER_ID,
       service=SERVICE_NAME
   )

   # Graceful shutdown handler
   def signal_handler(signum, frame):
       logger.info("Received shutdown signal", signal=signum)
       # Cleanup resources
       # Close database connections
       # Finish processing current requests
       exit(0)

   signal.signal(signal.SIGTERM, signal_handler)

   # Health check endpoint
   @app.get("/health")
   async def health_check():
       return {
           "status": "healthy",
           "container_id": CONTAINER_ID,
           "service": SERVICE_NAME,
           "timestamp": time.time()
       }

Next Steps
----------

* :doc:`overview` - High-level service architecture
* :doc:`../development/index` - Development guidelines and Docker setup
* :doc:`../api-reference/index` - API endpoint documentation
* :doc:`../deployment/index` - Container deployment strategies
* :doc:`../user-guide/index` - User guide for the containerized infrastructure
* :doc:`../development/architecture` - Complete architecture documentation
