User Guide
==========

This comprehensive user guide covers all aspects of using the CVE Feed Service Docker infrastructure, from initial setup to advanced vulnerability management workflows using the Traefik serviceURLmanager and *.feedme.localhost domain access.

.. toctree::
   :maxdepth: 2

   docker-infrastructure-setup
   traefik-service-access
   authentication
   application-management
   component-management
   cpe-mapping
   vulnerability-feeds
   data-management
   workflows
   react-interface-flows
   troubleshooting-docker

Overview
--------

The CVE Feed Service is deployed as a comprehensive Docker infrastructure with Traefik service discovery, providing organizations with scalable vulnerability management through tailored CVE feeds based on actual application inventories. This guide will walk you through:

1. **Docker Infrastructure Setup**: Deploying the complete containerized environment
2. **Traefik Service Access**: Using *.feedme.localhost domains for service access
3. **Authentication**: Setting up users and managing access across services
4. **Application Management**: Registering and organizing your applications
5. **Component Management**: Tracking software components and dependencies
6. **CPE Mapping**: Linking components to vulnerability identifiers
7. **Vulnerability Feeds**: Consuming tailored CVE information
8. **Data Management**: Importing and maintaining CVE data
9. **Workflows**: Common use cases and best practices
10. **React Interface**: Using the modern web interface
11. **Troubleshooting**: Resolving common Docker and service issues

Getting Started Checklist
--------------------------

Before diving into the detailed guides, ensure you have:

☐ **Docker Infrastructure**
   - Docker and Docker Compose installed
   - CVE Feed Service containers deployed and running
   - Traefik router configured and healthy
   - All services accessible via *.feedme.localhost domains

☐ **Service Access**
   - Frontend accessible at https://app.feedme.localhost
   - Admin dashboard accessible at https://admin.feedme.localhost
   - API documentation accessible at https://api.feedme.localhost/docs
   - Monitoring dashboard accessible at https://dashboard.feedme.localhost

☐ **Authentication Setup**
   - Initial admin user created
   - User roles understood
   - JWT tokens working across services
   - API access configured

☐ **Initial Data**
   - CVE data imported from NVD via worker container
   - At least one application registered
   - Basic understanding of CPE format
   - Database and Redis containers healthy

☐ **Service Health**
   - All containers passing health checks
   - Prometheus metrics collection working
   - Grafana dashboards displaying data
   - Background worker processing CVE updates

Quick Start Workflow
--------------------

Here's a typical workflow to get your first tailored vulnerability feed using the Docker infrastructure:

1. **Access the web interface** at https://app.feedme.localhost
2. **Authenticate** through the React frontend or obtain API token
3. **Create an application** representing one of your systems
4. **Add components** that make up your application
5. **Map components to CPEs** for vulnerability correlation
6. **Request a tailored feed** to see relevant vulnerabilities

Example: Web Application Setup via Frontend
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Using the React web interface (recommended for new users):

1. **Access the Application**
   - Open https://app.feedme.localhost in your browser
   - Login with your credentials
   - Navigate to the Applications section

2. **Create Application**
   - Click "New Application"
   - Fill in application details:
     - Name: "E-commerce Website"
     - Environment: "Production"
     - Criticality: "High"
     - Description: "Main customer-facing e-commerce platform"

3. **Add Components**
   - Select your new application
   - Click "Add Component"
   - Add web server component:
     - Name: "nginx"
     - Version: "1.20.1"
     - Vendor: "nginx"
     - Type: "Web Server"

4. **Configure CPE Mapping**
   - Select the nginx component
   - Click "Add CPE Mapping"
   - Enter CPE string: ``cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*``
   - Set confidence: 100%

5. **View Vulnerability Feed**
   - Navigate to "Vulnerability Feeds"
   - Select your application
   - Filter by severity: "High"
   - Review tailored CVE list

Example: API Access via Traefik
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

For programmatic access using the containerized API:

.. code-block:: bash

   # 1. Login via Traefik-routed API
   curl -X POST "https://api.feedme.localhost/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username": "<EMAIL>", "password": "password123"}'

   # Save the token from response
   export TOKEN="your-jwt-token-here"

   # 2. Create application via API
   curl -X POST "https://api.feedme.localhost/api/v1/applications" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "E-commerce Website",
          "environment": "production",
          "criticality": "high",
          "description": "Main customer-facing e-commerce platform"
        }'

   # Save the application ID from response
   export APP_ID="application-uuid-here"

   # 3. Add web server component
   curl -X POST "https://api.feedme.localhost/api/v1/applications/$APP_ID/components" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "nginx",
          "version": "1.20.1",
          "vendor": "nginx",
          "component_type": "web_server"
        }'

   # Save the component ID from response
   export COMPONENT_ID="component-uuid-here"

   # 4. Add CPE mapping
   curl -X POST "https://api.feedme.localhost/api/v1/components/$COMPONENT_ID/cpe-mappings" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
          "confidence": 1.0,
          "mapping_source": "manual"
        }'

   # 5. Get tailored vulnerability feed
   curl "https://api.feedme.localhost/api/v1/cves/feed?application_id=$APP_ID&severity=HIGH" \
        -H "Authorization: Bearer $TOKEN"

This workflow demonstrates the core functionality using the Traefik-routed services and shows how applications, components, and CPE mappings work together to provide relevant vulnerability information.

Service Access Overview
~~~~~~~~~~~~~~~~~~~~~~~

The Docker infrastructure provides multiple access points:

.. mermaid::

   graph TB
       subgraph "User Access Points"
           USER[End User]
           ADMIN[Administrator]
           DEV[Developer/API User]
       end

       subgraph "Traefik Service Discovery"
           T[Traefik Router<br/>Service Discovery]
       end

       subgraph "Frontend Services"
           APP[Web Application<br/>app.feedme.localhost]
           ADMIN_UI[Admin Dashboard<br/>admin.feedme.localhost]
           DOCS[Documentation<br/>docs.feedme.localhost]
       end

       subgraph "Backend Services"
           API[REST API<br/>api.feedme.localhost]
           METRICS[Prometheus<br/>metrics.feedme.localhost]
           GRAFANA[Grafana<br/>dashboard.feedme.localhost]
       end

       USER --> T
       ADMIN --> T
       DEV --> T

       T --> APP
       T --> ADMIN_UI
       T --> DOCS
       T --> API
       T --> METRICS
       T --> GRAFANA

       APP --> API
       ADMIN_UI --> API

Navigation Tips
---------------

**For New Users:**
   Start with :doc:`authentication` to understand access control, then follow the guides in order.

**For Administrators:**
   Focus on :doc:`authentication` and :doc:`data-management` for system setup and maintenance.

**For Security Analysts:**
   Jump to :doc:`vulnerability-feeds` and :doc:`workflows` for day-to-day operations.

**For Developers:**
   Review the API examples in each section and check the API reference documentation.

Common Use Cases
----------------

**Vulnerability Assessment**
   Regular review of vulnerabilities affecting your application portfolio

**Incident Response**
   Quickly identify affected systems when new critical vulnerabilities are disclosed

**Compliance Reporting**
   Generate reports showing vulnerability management coverage and response

**DevSecOps Integration**
   Integrate vulnerability feeds into CI/CD pipelines and security automation

**Risk Prioritization**
   Combine vulnerability severity with business criticality for better decision making

Support Resources
-----------------

* **Interactive API Documentation**: Available at ``/api/v1/docs`` when the service is running
* **GitHub Repository**: Source code, issues, and discussions
* **Example Scripts**: Sample code for common operations
* **Community**: User discussions and shared workflows

Next Steps
----------

Choose your path based on your role and immediate needs:

* **System Administrator**: Start with :doc:`authentication` and :doc:`data-management`
* **Security Analyst**: Begin with :doc:`application-management` and :doc:`vulnerability-feeds`
* **Developer**: Review :doc:`authentication` for API access, then explore specific endpoints
