Workflows
=========

This guide covers common workflows and use cases for the CVE Feed Service, providing step-by-step procedures for typical vulnerability management scenarios.

Overview
--------

The CVE Feed Service supports various vulnerability management workflows:

* **Initial Setup**: Getting started with your first application
* **Daily Operations**: Regular vulnerability monitoring and assessment
* **Incident Response**: Responding to new critical vulnerabilities
* **Compliance Reporting**: Generating reports for audits and compliance
* **Application Lifecycle**: Managing applications through development to production

Initial Setup Workflow
----------------------

New Organization Onboarding
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Step 1: System Setup**

.. code-block:: bash

   # 1. Verify system is running
   curl http://localhost:8000/health

   # 2. Import initial CVE data (this may take several hours)
   cve-feed cve bulk-import --years 2

   # 3. Create initial admin user (if not done during installation)
   # This requires direct database access or manual user creation

**Step 2: User Management**

.. code-block:: bash

   # Login as admin
   curl -X POST "http://localhost:8000/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username": "admin", "password": "admin123"}'

   export ADMIN_TOKEN="your-admin-token"

   # Create security analyst users
   curl -X POST "http://localhost:8000/api/v1/auth/users" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "username": "security_analyst_1",
          "email": "<EMAIL>",
          "full_name": "Jane Smith",
          "password": "secure_password_123",
          "role": "security_analyst"
        }'

**Step 3: First Application Registration**

.. code-block:: bash

   # Login as security analyst
   curl -X POST "http://localhost:8000/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username": "security_analyst_1", "password": "secure_password_123"}'

   export ANALYST_TOKEN="your-analyst-token"

   # Create first application
   curl -X POST "http://localhost:8000/api/v1/applications" \
        -H "Authorization: Bearer $ANALYST_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "Customer Portal",
          "description": "Main customer-facing web application",
          "environment": "production",
          "criticality": "high",
          "owner": "Platform Team"
        }'

   export APP_ID="returned-application-id"

**Step 4: Component Inventory**

.. code-block:: bash

   # Add web server component
   curl -X POST "http://localhost:8000/api/v1/applications/$APP_ID/components" \
        -H "Authorization: Bearer $ANALYST_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "nginx",
          "version": "1.20.1",
          "vendor": "nginx",
          "component_type": "web_server",
          "description": "Main web server"
        }'

   export NGINX_COMPONENT_ID="returned-component-id"

   # Add database component
   curl -X POST "http://localhost:8000/api/v1/applications/$APP_ID/components" \
        -H "Authorization: Bearer $ANALYST_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "postgresql",
          "version": "13.8",
          "vendor": "postgresql",
          "component_type": "database",
          "description": "Primary application database"
        }'

   export DB_COMPONENT_ID="returned-component-id"

**Step 5: CPE Mapping**

.. code-block:: bash

   # Map nginx component to CPE
   curl -X POST "http://localhost:8000/api/v1/components/$NGINX_COMPONENT_ID/cpe-mappings" \
        -H "Authorization: Bearer $ANALYST_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
          "confidence": 1.0,
          "mapping_source": "official"
        }'

   # Map database component to CPE
   curl -X POST "http://localhost:8000/api/v1/components/$DB_COMPONENT_ID/cpe-mappings" \
        -H "Authorization: Bearer $ANALYST_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "cpe_string": "cpe:2.3:a:postgresql:postgresql:13.8:*:*:*:*:*:*:*",
          "confidence": 1.0,
          "mapping_source": "official"
        }'

**Step 6: Verify Vulnerability Feed**

.. code-block:: bash

   # Get tailored vulnerability feed
   curl -H "Authorization: Bearer $ANALYST_TOKEN" \
        "http://localhost:8000/api/v1/cves/feed?application_id=$APP_ID&severity=HIGH" \
        | jq '.cves[] | {cve_id: .cve_id, severity: .cvss_v3_severity, score: .cvss_v3_score}'

Daily Operations Workflow
-------------------------

Morning Vulnerability Review
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Step 1: Check for New Critical Vulnerabilities**

.. code-block:: bash

   # Get critical vulnerabilities from last 24 hours
   YESTERDAY=$(date -d "yesterday" -u +"%Y-%m-%dT%H:%M:%SZ")
   
   curl -H "Authorization: Bearer $ANALYST_TOKEN" \
        "http://localhost:8000/api/v1/cves/feed?severity=CRITICAL&published_after=$YESTERDAY" \
        | jq '{total: .total, cves: [.cves[] | {cve_id: .cve_id, score: .cvss_v3_score, description: .description[:100]}]}'

**Step 2: Review Application-Specific Vulnerabilities**

.. code-block:: bash

   # Check each critical application
   for APP_ID in $(curl -s -H "Authorization: Bearer $ANALYST_TOKEN" \
                        "http://localhost:8000/api/v1/applications?criticality=critical" \
                        | jq -r '.[].id'); do
     
     APP_NAME=$(curl -s -H "Authorization: Bearer $ANALYST_TOKEN" \
                     "http://localhost:8000/api/v1/applications/$APP_ID" \
                     | jq -r '.name')
     
     HIGH_CVES=$(curl -s -H "Authorization: Bearer $ANALYST_TOKEN" \
                      "http://localhost:8000/api/v1/cves/feed?application_id=$APP_ID&severity=HIGH" \
                      | jq '.total')
     
     echo "Application: $APP_NAME - High/Critical CVEs: $HIGH_CVES"
   done

**Step 3: Update CVE Data**

.. code-block:: bash

   # Run incremental update to get latest CVE data
   cve-feed cve incremental-update --hours 24

Weekly Vulnerability Assessment
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Step 1: Generate Portfolio Summary**

.. code-block:: bash

   #!/bin/bash
   # Weekly vulnerability assessment script

   echo "# Weekly Vulnerability Assessment Report"
   echo "Generated: $(date)"
   echo

   # Get all applications
   APPS=$(curl -s -H "Authorization: Bearer $ANALYST_TOKEN" \
               "http://localhost:8000/api/v1/applications")

   echo "## Application Vulnerability Summary"
   echo "| Application | Environment | Criticality | High CVEs | Critical CVEs |"
   echo "|-------------|-------------|-------------|-----------|---------------|"

   echo "$APPS" | jq -r '.[] | "\(.id)|\(.name)|\(.environment)|\(.criticality)"' | \
   while IFS='|' read -r APP_ID APP_NAME ENV CRIT; do
     HIGH_COUNT=$(curl -s -H "Authorization: Bearer $ANALYST_TOKEN" \
                       "http://localhost:8000/api/v1/cves/feed?application_id=$APP_ID&severity=HIGH" \
                       | jq '.total')
     CRITICAL_COUNT=$(curl -s -H "Authorization: Bearer $ANALYST_TOKEN" \
                           "http://localhost:8000/api/v1/cves/feed?application_id=$APP_ID&severity=CRITICAL" \
                           | jq '.total')
     
     echo "| $APP_NAME | $ENV | $CRIT | $HIGH_COUNT | $CRITICAL_COUNT |"
   done

**Step 2: Identify Trending Vulnerabilities**

.. code-block:: bash

   # Get vulnerabilities from last 7 days
   WEEK_AGO=$(date -d "7 days ago" -u +"%Y-%m-%dT%H:%M:%SZ")
   
   curl -H "Authorization: Bearer $ANALYST_TOKEN" \
        "http://localhost:8000/api/v1/cves/?severity=HIGH&published_after=$WEEK_AGO&limit=20" \
        | jq '.cves[] | {cve_id: .cve_id, severity: .cvss_v3_severity, published: .published_date, description: .description[:100]}'

Incident Response Workflow
--------------------------

Critical Vulnerability Response
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Step 1: Immediate Assessment**

When a critical vulnerability is announced (e.g., Log4Shell, Heartbleed):

.. code-block:: bash

   # 1. Import the specific CVE immediately
   cve-feed cve import-single CVE-2021-44228  # Log4Shell example

   # 2. Check which applications are affected
   curl -H "Authorization: Bearer $ANALYST_TOKEN" \
        "http://localhost:8000/api/v1/cves/CVE-2021-44228" \
        | jq '.cpe_applicability[] | .cpe_string'

   # 3. Get affected applications
   curl -H "Authorization: Bearer $ANALYST_TOKEN" \
        "http://localhost:8000/api/v1/cves/feed?severity=CRITICAL" \
        | jq '.cves[] | select(.cve_id=="CVE-2021-44228")'

**Step 2: Impact Analysis**

.. code-block:: bash

   # Create impact assessment script
   #!/bin/bash
   CVE_ID="CVE-2021-44228"
   
   echo "# Critical Vulnerability Impact Assessment"
   echo "CVE: $CVE_ID"
   echo "Assessment Time: $(date)"
   echo

   # Check all applications for this CVE
   ALL_APPS=$(curl -s -H "Authorization: Bearer $ANALYST_TOKEN" \
                   "http://localhost:8000/api/v1/applications")

   echo "## Affected Applications"
   echo "$ALL_APPS" | jq -r '.[].id' | while read APP_ID; do
     AFFECTED=$(curl -s -H "Authorization: Bearer $ANALYST_TOKEN" \
                     "http://localhost:8000/api/v1/cves/feed?application_id=$APP_ID" \
                     | jq ".cves[] | select(.cve_id==\"$CVE_ID\")")
     
     if [ "$AFFECTED" != "" ]; then
       APP_INFO=$(echo "$ALL_APPS" | jq ".[] | select(.id==\"$APP_ID\")")
       APP_NAME=$(echo "$APP_INFO" | jq -r '.name')
       ENVIRONMENT=$(echo "$APP_INFO" | jq -r '.environment')
       CRITICALITY=$(echo "$APP_INFO" | jq -r '.criticality')
       
       echo "⚠️  AFFECTED: $APP_NAME ($ENVIRONMENT, $CRITICALITY)"
       
       # Get affected components
       curl -s -H "Authorization: Bearer $ANALYST_TOKEN" \
            "http://localhost:8000/api/v1/applications/$APP_ID" \
            | jq -r '.components[] | "  - \(.name) \(.version)"'
     fi
   done

**Step 3: Communication and Tracking**

.. code-block:: bash

   # Generate executive summary
   echo "## Executive Summary"
   echo "- CVE: $CVE_ID"
   echo "- Severity: CRITICAL"
   echo "- Affected Applications: $(count_affected_apps)"
   echo "- Business Impact: $(assess_business_impact)"
   echo "- Recommended Actions: Immediate patching required"

Application Lifecycle Workflow
------------------------------

New Application Deployment
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Step 1: Pre-Deployment Registration**

.. code-block:: bash

   # Register application in staging first
   curl -X POST "http://localhost:8000/api/v1/applications" \
        -H "Authorization: Bearer $ANALYST_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "New Microservice",
          "description": "User authentication service",
          "environment": "staging",
          "criticality": "medium",
          "owner": "Auth Team"
        }'

   export STAGING_APP_ID="returned-app-id"

**Step 2: Component Discovery and Registration**

.. code-block:: bash

   # Add components based on deployment manifest
   # Example: Kubernetes deployment analysis
   
   # Web framework
   curl -X POST "http://localhost:8000/api/v1/applications/$STAGING_APP_ID/components" \
        -H "Authorization: Bearer $ANALYST_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "spring-boot",
          "version": "2.7.0",
          "vendor": "pivotal",
          "component_type": "framework"
        }'

   # Database driver
   curl -X POST "http://localhost:8000/api/v1/applications/$STAGING_APP_ID/components" \
        -H "Authorization: Bearer $ANALYST_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "postgresql-jdbc",
          "version": "42.3.6",
          "vendor": "postgresql",
          "component_type": "library"
        }'

**Step 3: Vulnerability Assessment**

.. code-block:: bash

   # Check for vulnerabilities before production deployment
   VULN_COUNT=$(curl -s -H "Authorization: Bearer $ANALYST_TOKEN" \
                     "http://localhost:8000/api/v1/cves/feed?application_id=$STAGING_APP_ID&severity=HIGH" \
                     | jq '.total')

   if [ "$VULN_COUNT" -gt 0 ]; then
     echo "⚠️  Warning: $VULN_COUNT high/critical vulnerabilities found"
     echo "Review required before production deployment"
     
     # Get vulnerability details
     curl -H "Authorization: Bearer $ANALYST_TOKEN" \
          "http://localhost:8000/api/v1/cves/feed?application_id=$STAGING_APP_ID&severity=HIGH" \
          | jq '.cves[] | {cve_id: .cve_id, severity: .cvss_v3_severity, description: .description[:100]}'
   else
     echo "✅ No high/critical vulnerabilities found"
   fi

Production Promotion
~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Create production version of application
   curl -X POST "http://localhost:8000/api/v1/applications" \
        -H "Authorization: Bearer $ANALYST_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "New Microservice",
          "description": "User authentication service - Production",
          "environment": "production",
          "criticality": "high",
          "owner": "Auth Team"
        }'

   export PROD_APP_ID="returned-app-id"

   # Copy components from staging (manual process or scripted)
   # Update criticality and monitoring as needed

Compliance Reporting Workflow
-----------------------------

Monthly Compliance Report
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   #!/bin/bash
   # Monthly compliance report generation

   REPORT_DATE=$(date +"%Y-%m")
   REPORT_FILE="compliance_report_$REPORT_DATE.md"

   cat > $REPORT_FILE << EOF
   # Vulnerability Management Compliance Report
   ## Period: $REPORT_DATE
   ## Generated: $(date)

   ### Executive Summary
   EOF

   # Application inventory summary
   TOTAL_APPS=$(curl -s -H "Authorization: Bearer $ANALYST_TOKEN" \
                     "http://localhost:8000/api/v1/applications" | jq length)
   
   PROD_APPS=$(curl -s -H "Authorization: Bearer $ANALYST_TOKEN" \
                    "http://localhost:8000/api/v1/applications?environment=production" | jq length)

   cat >> $REPORT_FILE << EOF
   - Total Applications: $TOTAL_APPS
   - Production Applications: $PROD_APPS
   - Applications with High/Critical Vulnerabilities: $(count_vulnerable_apps)

   ### Vulnerability Summary
   EOF

   # Vulnerability statistics
   for SEVERITY in CRITICAL HIGH MEDIUM LOW; do
     COUNT=$(curl -s -H "Authorization: Bearer $ANALYST_TOKEN" \
                  "http://localhost:8000/api/v1/cves/?severity=$SEVERITY" | jq '.total')
     echo "- $SEVERITY: $COUNT" >> $REPORT_FILE
   done

Audit Trail Report
~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Generate audit trail for compliance
   echo "# Audit Trail Report"
   echo "## Application Changes (Last 30 Days)"
   
   # Query database for recent changes
   psql -d cve_feed_dev -c "
     SELECT 
       'Application' as type,
       name,
       created_at,
       updated_at
     FROM applications 
     WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
        OR updated_at >= CURRENT_DATE - INTERVAL '30 days'
     ORDER BY updated_at DESC;
   "

Best Practices
--------------

Workflow Automation
~~~~~~~~~~~~~~~~~~~

**Scripting Guidelines:**
* Use consistent error handling
* Log all operations for audit trails
* Implement idempotent operations
* Include rollback procedures

**Integration Points:**
* CI/CD pipeline integration
* Monitoring system alerts
* Ticketing system integration
* Communication platform notifications

Documentation Standards
~~~~~~~~~~~~~~~~~~~~~~~

**Maintain Documentation:**
* Application inventory procedures
* CPE mapping guidelines
* Incident response playbooks
* Compliance reporting templates

**Version Control:**
* Store scripts in version control
* Document configuration changes
* Maintain change logs
* Review and update procedures regularly

Team Coordination
~~~~~~~~~~~~~~~~~

**Role Responsibilities:**
* IT Admins: System maintenance, user management
* Security Analysts: Vulnerability assessment, reporting
* Development Teams: Component updates, remediation

**Communication Protocols:**
* Regular vulnerability briefings
* Critical vulnerability alerts
* Monthly compliance reviews
* Quarterly process improvements
