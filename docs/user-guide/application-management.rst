Application Management
=====================

Application management is the foundation of the CVE Feed Service. Applications represent the software systems in your environment that you want to monitor for vulnerabilities. This guide covers creating, managing, and organizing your application inventory.

Overview
--------

Applications in the CVE Feed Service represent:

* **Software Systems**: Web applications, services, databases, etc.
* **Business Context**: Environment, criticality, ownership information
* **Component Inventory**: The software components that make up each application
* **Vulnerability Scope**: The basis for tailored vulnerability feeds

Application Properties
----------------------

Each application has the following properties:

Core Information
~~~~~~~~~~~~~~~~

* **Name**: Unique identifier within an environment
* **Description**: Detailed description of the application's purpose
* **Version**: Application version (optional)
* **Owner**: Person or team responsible for the application

Context Information
~~~~~~~~~~~~~~~~~~~

* **Environment**: Deployment environment (development, staging, production)
* **Criticality**: Business impact level (low, medium, high, critical)

Metadata
~~~~~~~~

* **Created/Updated Timestamps**: Automatic tracking of changes
* **Soft Delete Support**: Applications can be deactivated without losing history

Creating Applications
---------------------

Basic Application Creation
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/applications" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "Customer Portal",
          "description": "Customer-facing web application for account management",
          "environment": "production",
          "criticality": "high",
          "owner": "Platform Team"
        }'

**Response:**

.. code-block:: json

   {
     "id": "550e8400-e29b-41d4-a716-************",
     "name": "Customer Portal",
     "description": "Customer-facing web application for account management",
     "version": null,
     "owner": "Platform Team",
     "environment": "production",
     "criticality": "high",
     "created_at": "2025-01-18T10:00:00Z",
     "updated_at": "2025-01-18T10:00:00Z",
     "deleted_at": null
   }

Application with Version
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/applications" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "API Gateway",
          "description": "Central API gateway for microservices",
          "version": "2.1.0",
          "environment": "production",
          "criticality": "critical",
          "owner": "Infrastructure Team"
        }'

Minimal Application
~~~~~~~~~~~~~~~~~~~

Only name is required; other fields are optional:

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/applications" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "Development Tool"
        }'

Listing Applications
--------------------

Basic Listing
~~~~~~~~~~~~~

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications"

With Pagination
~~~~~~~~~~~~~~~

.. code-block:: bash

   # Get first 10 applications
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications?skip=0&limit=10"

   # Get next 10 applications
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications?skip=10&limit=10"

Filtered by Environment
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Production applications only
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications?environment=production"

   # Development applications
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications?environment=development"

Filtered by Criticality
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # High and critical applications
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications?criticality=high"

   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications?criticality=critical"

Combined Filters
~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Production applications with high criticality
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications?environment=production&criticality=high"

Retrieving Application Details
------------------------------

Get Application with Components
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications/{application_id}"

**Response includes full component inventory:**

.. code-block:: json

   {
     "id": "550e8400-e29b-41d4-a716-************",
     "name": "Customer Portal",
     "description": "Customer-facing web application",
     "environment": "production",
     "criticality": "high",
     "owner": "Platform Team",
     "created_at": "2025-01-18T10:00:00Z",
     "updated_at": "2025-01-18T10:00:00Z",
     "components": [
       {
         "id": "component-uuid-1",
         "name": "nginx",
         "version": "1.20.1",
         "vendor": "nginx",
         "component_type": "web_server",
         "cpe_mappings": [
           {
             "id": "mapping-uuid-1",
             "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
             "confidence": 1.0,
             "mapping_source": "manual"
           }
         ]
       }
     ]
   }

Get Application Without Components
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications/{application_id}?include_components=false"

Updating Applications
---------------------

Partial Updates
~~~~~~~~~~~~~~~

You can update any subset of application fields:

.. code-block:: bash

   # Update description only
   curl -X PATCH "http://localhost:8000/api/v1/applications/{application_id}" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "description": "Updated description with more details"
        }'

   # Update criticality and owner
   curl -X PATCH "http://localhost:8000/api/v1/applications/{application_id}" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "criticality": "critical",
          "owner": "Security Team"
        }'

Environment Migration
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Promote from staging to production
   curl -X PATCH "http://localhost:8000/api/v1/applications/{application_id}" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "environment": "production",
          "criticality": "high"
        }'

Version Updates
~~~~~~~~~~~~~~~

.. code-block:: bash

   # Update application version
   curl -X PATCH "http://localhost:8000/api/v1/applications/{application_id}" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "version": "2.2.0"
        }'

Deleting Applications
---------------------

Soft Delete
~~~~~~~~~~~

Applications are soft-deleted by default, preserving audit trails:

.. code-block:: bash

   curl -X DELETE "http://localhost:8000/api/v1/applications/{application_id}" \
        -H "Authorization: Bearer YOUR_TOKEN"

**Effects of Soft Delete:**
* Application is hidden from normal listings
* All associated components are also soft-deleted
* Historical data is preserved for auditing
* Can be restored if needed (requires database access)

Best Practices
--------------

Naming Conventions
~~~~~~~~~~~~~~~~~~

**Consistent Naming:**
* Use descriptive, consistent names
* Include environment in name if managing multiple environments
* Examples: "Customer Portal - Prod", "API Gateway - Staging"

**Avoid Conflicts:**
* Names must be unique within an environment
* Consider using prefixes for different teams or business units

Environment Management
~~~~~~~~~~~~~~~~~~~~~~

**Environment Values:**
* Use standard values: ``development``, ``staging``, ``production``
* Be consistent across your organization
* Consider additional environments like ``testing``, ``qa``

**Environment-Specific Considerations:**
* Production applications typically have higher criticality
* Development environments may have relaxed security requirements
* Staging should mirror production for accurate vulnerability assessment

Criticality Levels
~~~~~~~~~~~~~~~~~~~

**Criticality Guidelines:**
* ``critical``: Business-critical systems, customer-facing applications
* ``high``: Important systems with significant business impact
* ``medium``: Standard business applications
* ``low``: Development tools, internal utilities

**Business Impact Mapping:**
* Consider financial impact of downtime
* Evaluate customer impact
* Assess regulatory compliance requirements
* Factor in data sensitivity

Ownership and Responsibility
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Clear Ownership:**
* Assign specific teams or individuals
* Include contact information in descriptions
* Update ownership when teams change

**Documentation:**
* Use descriptions to capture important details
* Include links to documentation or repositories
* Note special configuration or deployment requirements

Common Workflows
----------------

New Application Onboarding
~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Create Application Record**
   
   .. code-block:: bash

      curl -X POST "http://localhost:8000/api/v1/applications" \
           -H "Authorization: Bearer YOUR_TOKEN" \
           -H "Content-Type: application/json" \
           -d '{
             "name": "New Service",
             "description": "Microservice for user authentication",
             "environment": "development",
             "criticality": "medium",
             "owner": "Auth Team"
           }'

2. **Add Components** (see :doc:`component-management`)
3. **Map CPEs** (see :doc:`cpe-mapping`)
4. **Verify Vulnerability Feed** (see :doc:`vulnerability-feeds`)

Application Portfolio Review
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Get all production applications
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications?environment=production" \
        | jq '.[] | {name: .name, criticality: .criticality, owner: .owner}'

Environment Promotion
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # 1. Get staging application details
   STAGING_APP=$(curl -H "Authorization: Bearer YOUR_TOKEN" \
                      "http://localhost:8000/api/v1/applications?environment=staging&name=MyApp")

   # 2. Create production version
   curl -X POST "http://localhost:8000/api/v1/applications" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "MyApp",
          "description": "Production deployment of MyApp",
          "environment": "production",
          "criticality": "high",
          "owner": "Platform Team"
        }'

   # 3. Copy components and CPE mappings (manual process)

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Duplicate Application Names**
   * Error: Application name already exists in environment
   * Solution: Use unique names or different environments

**Missing Required Fields**
   * Error: Validation error for required fields
   * Solution: Ensure 'name' field is provided

**Permission Denied**
   * Error: 403 Forbidden
   * Solution: Verify user has appropriate role (security_analyst or it_admin)

**Application Not Found**
   * Error: 404 Not Found
   * Solution: Verify application ID and check if it was soft-deleted

Validation Errors
~~~~~~~~~~~~~~~~~~

The API validates all input and returns detailed error messages:

.. code-block:: json

   {
     "detail": [
       {
         "loc": ["body", "name"],
         "msg": "field required",
         "type": "value_error.missing"
       }
     ]
   }

**Common Validation Issues:**
* Name too long (max 255 characters)
* Invalid environment value
* Invalid criticality level
