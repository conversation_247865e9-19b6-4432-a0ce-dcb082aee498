Data Management
===============

Data management in the CVE Feed Service involves importing, updating, and maintaining CVE data from the National Vulnerability Database (NVD). This guide covers the CLI tools and processes for keeping your vulnerability data current.

Overview
--------

The CVE Feed Service provides command-line tools for:

* **Bulk Import**: Initial import of historical CVE data
* **Incremental Updates**: Regular updates of new and modified CVEs
* **Single CVE Import**: Import specific CVEs for testing or immediate needs
* **Data Monitoring**: Checking import status and data quality

All data management operations use the NVD CVE API with proper rate limiting and error handling.

CLI Tools
---------

The main CLI entry point is:

.. code-block:: bash

   python -m src.cve_feed_service.cli.main

Or if installed as a package:

.. code-block:: bash

   cve-feed

Available Commands
~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Show help
   cve-feed --help

   # CVE data management commands
   cve-feed cve --help

   # Show version
   cve-feed version

Bulk CVE Import
---------------

Initial Data Import
~~~~~~~~~~~~~~~~~~~

For new installations, perform a bulk import to populate the database:

.. code-block:: bash

   # Import CVEs from the last 2 years (default)
   cve-feed cve bulk-import

   # Import from the last 1 year
   cve-feed cve bulk-import --years 1

   # Import from the last 5 years
   cve-feed cve bulk-import --years 5

**Import Process:**
1. Calculates date range based on years parameter
2. Fetches CVE data from NVD API in batches
3. Processes and enriches CVE data
4. Creates CPE applicability records
5. Commits data in configurable batch sizes

Force Reimport
~~~~~~~~~~~~~~

To reimport data even if CVEs already exist:

.. code-block:: bash

   # Force reimport (overwrites existing data)
   cve-feed cve bulk-import --years 2 --force

**Use Cases for Force Reimport:**
* Data corruption recovery
* Schema changes requiring data refresh
* Testing with clean data

Import Progress Monitoring
~~~~~~~~~~~~~~~~~~~~~~~~~~

The bulk import process provides detailed progress information:

.. code-block:: text

   Starting bulk CVE import, years=2
   Retrieved 45,234 CVEs from NVD API
   Processing CVE batch 1/453 (100 CVEs)
   Processing CVE batch 2/453 (100 CVEs)
   ...
   Bulk import completed, total_processed=45,234

**Performance Considerations:**
* Large imports can take several hours
* Rate limiting may slow the process
* Database performance affects processing speed
* Network connectivity impacts API calls

Incremental Updates
-------------------

Regular Updates
~~~~~~~~~~~~~~~

For ongoing operations, use incremental updates to get new and modified CVEs:

.. code-block:: bash

   # Update CVEs modified in the last 24 hours (default)
   cve-feed cve incremental-update

   # Update CVEs modified in the last 12 hours
   cve-feed cve incremental-update --hours 12

   # Update CVEs modified in the last week
   cve-feed cve incremental-update --hours 168

**Update Process:**
1. Queries NVD API for CVEs modified since the specified time
2. Updates existing CVE records or creates new ones
3. Updates CPE applicability information
4. Maintains audit trail of changes

Automated Updates
~~~~~~~~~~~~~~~~~

Set up automated updates using cron or systemd timers:

**Cron Example:**

.. code-block:: bash

   # Add to crontab (crontab -e)
   # Run incremental update every 4 hours
   0 */4 * * * /path/to/venv/bin/cve-feed cve incremental-update --hours 6

   # Daily update with longer lookback for reliability
   0 2 * * * /path/to/venv/bin/cve-feed cve incremental-update --hours 30

**Systemd Timer Example:**

.. code-block:: ini

   # /etc/systemd/system/cve-update.service
   [Unit]
   Description=CVE Feed Incremental Update
   After=network.target

   [Service]
   Type=oneshot
   User=cve-feed
   ExecStart=/usr/local/bin/cve-feed cve incremental-update --hours 6
   Environment=DATABASE_URL=postgresql+asyncpg://user:pass@localhost/cve_feed

   # /etc/systemd/system/cve-update.timer
   [Unit]
   Description=Run CVE update every 4 hours
   Requires=cve-update.service

   [Timer]
   OnCalendar=*-*-* 00,04,08,12,16,20:00:00
   Persistent=true

   [Install]
   WantedBy=timers.target

Single CVE Import
-----------------

Import Specific CVEs
~~~~~~~~~~~~~~~~~~~~

For testing or immediate needs, import individual CVEs:

.. code-block:: bash

   # Import a specific CVE
   cve-feed cve import-single CVE-2023-1234

   # Import multiple CVEs
   cve-feed cve import-single CVE-2023-1234
   cve-feed cve import-single CVE-2023-5678

**Use Cases:**
* Testing CPE mappings with known vulnerable components
* Immediate import of newly disclosed critical vulnerabilities
* Development and testing scenarios
* Verification of data processing logic

Error Handling
~~~~~~~~~~~~~~

The CLI provides detailed error information:

.. code-block:: bash

   # Example error output
   cve-feed cve import-single CVE-9999-0000
   # Error: CVE CVE-9999-0000 not found in NVD database

   # Check logs for detailed error information
   tail -f /var/log/cve-feed/import.log

NVD API Configuration
---------------------

Rate Limiting
~~~~~~~~~~~~~

The service respects NVD API rate limits:

**Without API Key:**
* 5 requests per 30 seconds
* 10,000 requests per day

**With API Key:**
* 50 requests per 30 seconds
* 100,000 requests per day

**Configuration:**

.. code-block:: bash

   # Set NVD API key in environment
   export NVD_API_KEY="your-api-key-here"

   # Or in .env file
   echo "NVD_API_KEY=your-api-key-here" >> .env

API Key Benefits
~~~~~~~~~~~~~~~~

Obtaining an NVD API key provides:

* **Higher Rate Limits**: 10x more requests per time period
* **Better Reliability**: Reduced chance of rate limit errors
* **Priority Access**: Better service during high-traffic periods

**How to Get an API Key:**
1. Visit https://nvd.nist.gov/developers/request-an-api-key
2. Complete the registration form
3. Receive API key via email
4. Configure in your environment

Request Timeout and Retry
~~~~~~~~~~~~~~~~~~~~~~~~~

The service includes robust error handling:

.. code-block:: python

   # Configuration options (in settings)
   NVD_REQUEST_TIMEOUT=30  # seconds
   NVD_RATE_LIMIT_PER_MINUTE=10  # requests per minute
   CVE_BATCH_SIZE=100  # CVEs processed per batch

**Retry Logic:**
* Exponential backoff for failed requests
* Maximum 3 retry attempts
* Automatic rate limit compliance
* Detailed error logging

Data Quality and Monitoring
---------------------------

Import Verification
~~~~~~~~~~~~~~~~~~~

After imports, verify data quality:

.. code-block:: bash

   # Check total CVE count
   psql -d cve_feed_dev -c "SELECT COUNT(*) FROM cves WHERE deleted_at IS NULL;"

   # Check recent imports
   psql -d cve_feed_dev -c "
     SELECT DATE(created_at) as import_date, COUNT(*) as cve_count 
     FROM cves 
     WHERE deleted_at IS NULL 
     GROUP BY DATE(created_at) 
     ORDER BY import_date DESC 
     LIMIT 10;
   "

   # Check CVE severity distribution
   psql -d cve_feed_dev -c "
     SELECT cvss_v3_severity, COUNT(*) 
     FROM cves 
     WHERE deleted_at IS NULL AND cvss_v3_severity IS NOT NULL
     GROUP BY cvss_v3_severity;
   "

Data Freshness Monitoring
~~~~~~~~~~~~~~~~~~~~~~~~~

Monitor how current your CVE data is:

.. code-block:: bash

   # Check most recent CVE publication date
   psql -d cve_feed_dev -c "
     SELECT MAX(published_date) as latest_cve_published,
            MAX(nvd_last_modified) as latest_import
     FROM cves 
     WHERE deleted_at IS NULL;
   "

   # Check for gaps in recent data
   psql -d cve_feed_dev -c "
     SELECT DATE(published_date) as pub_date, COUNT(*) as cve_count
     FROM cves 
     WHERE deleted_at IS NULL 
       AND published_date >= CURRENT_DATE - INTERVAL '30 days'
     GROUP BY DATE(published_date)
     ORDER BY pub_date DESC;
   "

Error Monitoring
~~~~~~~~~~~~~~~~

Monitor import errors and failures:

.. code-block:: bash

   # Check application logs
   tail -f /var/log/cve-feed/application.log | grep ERROR

   # Monitor database for data anomalies
   psql -d cve_feed_dev -c "
     SELECT COUNT(*) as cves_without_scores
     FROM cves 
     WHERE deleted_at IS NULL 
       AND cvss_v3_score IS NULL 
       AND cvss_v2_score IS NULL;
   "

Database Maintenance
--------------------

Regular Maintenance Tasks
~~~~~~~~~~~~~~~~~~~~~~~~~

**Vacuum and Analyze:**

.. code-block:: bash

   # Regular maintenance (run weekly)
   psql -d cve_feed_dev -c "VACUUM ANALYZE cves;"
   psql -d cve_feed_dev -c "VACUUM ANALYZE cve_cpe_applicability;"

**Index Maintenance:**

.. code-block:: bash

   # Check index usage
   psql -d cve_feed_dev -c "
     SELECT schemaname, tablename, indexname, idx_tup_read, idx_tup_fetch
     FROM pg_stat_user_indexes 
     WHERE schemaname = 'public'
     ORDER BY idx_tup_read DESC;
   "

**Storage Monitoring:**

.. code-block:: bash

   # Check table sizes
   psql -d cve_feed_dev -c "
     SELECT 
       schemaname,
       tablename,
       pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
     FROM pg_tables 
     WHERE schemaname = 'public'
     ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
   "

Backup and Recovery
~~~~~~~~~~~~~~~~~~~

**Database Backup:**

.. code-block:: bash

   # Create backup
   pg_dump cve_feed_dev > cve_feed_backup_$(date +%Y%m%d).sql

   # Compressed backup
   pg_dump cve_feed_dev | gzip > cve_feed_backup_$(date +%Y%m%d).sql.gz

**Restore from Backup:**

.. code-block:: bash

   # Restore from backup
   psql cve_feed_dev < cve_feed_backup_20250118.sql

   # Restore compressed backup
   gunzip -c cve_feed_backup_20250118.sql.gz | psql cve_feed_dev

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Rate Limit Errors**
   * Symptoms: "Rate limit exceeded" errors
   * Solutions: 
     - Obtain NVD API key
     - Reduce batch sizes
     - Increase delays between requests

**Network Timeouts**
   * Symptoms: Connection timeout errors
   * Solutions:
     - Check network connectivity
     - Increase timeout values
     - Verify NVD API availability

**Database Connection Issues**
   * Symptoms: Database connection errors
   * Solutions:
     - Verify database is running
     - Check connection string
     - Verify user permissions

**Memory Issues**
   * Symptoms: Out of memory errors during large imports
   * Solutions:
     - Reduce batch sizes
     - Increase available memory
     - Process in smaller time ranges

**Incomplete Imports**
   * Symptoms: Import stops before completion
   * Solutions:
     - Check logs for specific errors
     - Resume with incremental update
     - Verify disk space availability

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~

**Database Tuning:**

.. code-block:: sql

   -- Optimize for bulk imports
   SET maintenance_work_mem = '1GB';
   SET checkpoint_completion_target = 0.9;
   SET wal_buffers = '16MB';

**Import Optimization:**

.. code-block:: bash

   # Use smaller batch sizes for memory-constrained systems
   export CVE_BATCH_SIZE=50

   # Increase timeout for slow networks
   export NVD_REQUEST_TIMEOUT=60

**Monitoring Performance:**

.. code-block:: bash

   # Monitor import progress
   tail -f /var/log/cve-feed/application.log | grep "Processing CVE batch"

   # Monitor database activity
   psql -d cve_feed_dev -c "
     SELECT query, state, query_start 
     FROM pg_stat_activity 
     WHERE datname = 'cve_feed_dev';
   "

Best Practices
--------------

Import Scheduling
~~~~~~~~~~~~~~~~~

* **Initial Import**: Run during off-hours due to duration
* **Regular Updates**: Every 4-6 hours for current data
* **Critical Updates**: Monitor for emergency CVE releases
* **Maintenance Windows**: Schedule during low-usage periods

Data Validation
~~~~~~~~~~~~~~~

* **Verify Counts**: Check expected number of CVEs imported
* **Spot Check**: Manually verify a few recent high-profile CVEs
* **Automated Tests**: Include data validation in monitoring
* **Backup Before Major Imports**: Ensure recovery capability

Monitoring and Alerting
~~~~~~~~~~~~~~~~~~~~~~~

* **Import Success/Failure**: Alert on failed imports
* **Data Freshness**: Alert if data becomes stale
* **Performance**: Monitor import duration and database performance
* **Storage**: Monitor disk usage growth
