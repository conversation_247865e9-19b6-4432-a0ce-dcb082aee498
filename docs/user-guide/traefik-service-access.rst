Traefik Service Access
======================

This guide explains how to access and use the CVE Feed Service through <PERSON><PERSON>fi<PERSON>'s service discovery and *.feedme.localhost domain management.

Service Discovery Overview
--------------------------

T<PERSON><PERSON><PERSON> acts as a reverse proxy and load balancer, automatically discovering services and routing traffic based on domain names and labels.

.. mermaid::

   graph TB
       subgraph "External Access"
           USER[Users]
           API_CLIENT[API Clients]
       end
       
       subgraph "Traefik Layer"
           T[Traefik Router<br/>Service Discovery<br/>Load Balancer]
       end
       
       subgraph "Service Containers"
           FE[Frontend<br/>app.feedme.localhost]
           AD[Admin<br/>admin.feedme.localhost]
           API[API<br/>api.feedme.localhost]
           DOC[Docs<br/>docs.feedme.localhost]
           MON[Monitoring<br/>dashboard.feedme.localhost]
       end
       
       USER --> T
       API_CLIENT --> T
       
       T --> FE
       T --> AD
       T --> API
       T --> DOC
       T --> MON

Available Services
-----------------

Frontend Services
~~~~~~~~~~~~~~~~~

**Main Web Application**
* **URL**: https://app.feedme.localhost
* **Purpose**: Primary user interface for vulnerability management
* **Features**: 
  - Application management
  - Component tracking
  - CVE feed viewing
  - User dashboard
  - Responsive design

**Administrative Dashboard**
* **URL**: https://admin.feedme.localhost
* **Purpose**: System administration and configuration
* **Features**:
  - User management
  - System metrics
  - Configuration settings
  - Audit logs
  - Bulk operations

**Documentation Site**
* **URL**: https://docs.feedme.localhost
* **Purpose**: Complete system documentation
* **Features**:
  - User guides
  - API reference
  - Architecture documentation
  - Troubleshooting guides

Backend Services
~~~~~~~~~~~~~~~

**REST API**
* **URL**: https://api.feedme.localhost
* **Purpose**: Programmatic access to all functionality
* **Endpoints**:
  - ``/api/v1/docs`` - Interactive API documentation
  - ``/api/v1/auth`` - Authentication endpoints
  - ``/api/v1/applications`` - Application management
  - ``/api/v1/cves`` - Vulnerability data
  - ``/health`` - Service health check

**Monitoring Services**
* **Prometheus**: https://metrics.feedme.localhost
* **Grafana**: https://dashboard.feedme.localhost

Service Access Patterns
-----------------------

Web Interface Access
~~~~~~~~~~~~~~~~~~~

**For End Users**:

1. **Access Main Application**
   
   .. code-block:: bash
   
      # Open in browser
      open https://app.feedme.localhost
   
2. **Login Process**
   
   - Navigate to login page
   - Enter credentials
   - Receive JWT token (stored in browser)
   - Access all authenticated features

3. **Navigation**
   
   - Dashboard: Overview of applications and vulnerabilities
   - Applications: Manage application inventory
   - Components: Track software components
   - Feeds: View tailored vulnerability feeds
   - Profile: User settings and preferences

**For Administrators**:

1. **Access Admin Dashboard**
   
   .. code-block:: bash
   
      # Open admin interface
      open https://admin.feedme.localhost
   
2. **Administrative Functions**
   
   - User management and role assignment
   - System configuration
   - Monitoring and metrics
   - Data import/export
   - System maintenance

API Access Patterns
~~~~~~~~~~~~~~~~~~

**Authentication**:

.. code-block:: bash

   # Login and get JWT token
   curl -X POST "https://api.feedme.localhost/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
          "username": "your-username",
          "password": "your-password"
        }'
   
   # Save token for subsequent requests
   export TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

**API Usage Examples**:

.. code-block:: bash

   # Get user profile
   curl -H "Authorization: Bearer $TOKEN" \
        "https://api.feedme.localhost/api/v1/auth/me"
   
   # List applications
   curl -H "Authorization: Bearer $TOKEN" \
        "https://api.feedme.localhost/api/v1/applications"
   
   # Get CVE feed for application
   curl -H "Authorization: Bearer $TOKEN" \
        "https://api.feedme.localhost/api/v1/cves/feed?application_id=APP_ID"

Service Health Monitoring
-------------------------

Health Check Endpoints
~~~~~~~~~~~~~~~~~~~~~~

All services provide health check endpoints for monitoring:

.. code-block:: bash

   # Check API service health
   curl https://api.feedme.localhost/health
   
   # Expected response:
   # {
   #   "status": "healthy",
   #   "version": "1.0.0",
   #   "timestamp": "2024-01-15T10:30:00Z"
   # }

**Service Status Monitoring**:

.. code-block:: bash

   # Check all service health
   for service in app admin api docs dashboard metrics; do
     echo "Checking $service.feedme.localhost..."
     curl -s -o /dev/null -w "%{http_code}" https://$service.feedme.localhost/health
     echo
   done

Load Balancing and Scaling
--------------------------

Traefik Configuration
~~~~~~~~~~~~~~~~~~~

Services are configured with Traefik labels for automatic discovery:

.. code-block:: yaml

   # Example service configuration
   api:
     labels:
       - "traefik.enable=true"
       - "traefik.http.routers.cve-api.rule=Host(`api.feedme.localhost`)"
       - "traefik.http.routers.cve-api.entrypoints=web"
       - "traefik.http.services.cve-api.loadbalancer.server.port=8000"
       - "traefik.http.routers.cve-api.middlewares=api-cors"

**Scaling Services**:

.. code-block:: bash

   # Scale API service to 3 replicas
   docker-compose up --scale api=3 -d
   
   # Traefik automatically load balances across replicas
   # No configuration changes needed

Service Discovery
~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Client
       participant Traefik
       participant Service1
       participant Service2
       
       Client->>Traefik: Request to api.feedme.localhost
       Traefik->>Traefik: Check available backends
       
       alt Load balance to Service1
           Traefik->>Service1: Forward request
           Service1-->>Traefik: Response
       else Load balance to Service2
           Traefik->>Service2: Forward request
           Service2-->>Traefik: Response
       end
       
       Traefik-->>Client: Return response

SSL/TLS Configuration
--------------------

Development Setup
~~~~~~~~~~~~~~~~

For development, Traefik can use self-signed certificates:

.. code-block:: yaml

   # traefik.yml configuration
   entryPoints:
     web:
       address: ":80"
     websecure:
       address: ":443"
   
   certificatesResolvers:
     letsencrypt:
       acme:
         email: <EMAIL>
         storage: acme.json
         httpChallenge:
           entryPoint: web

Production Setup
~~~~~~~~~~~~~~~

For production, configure proper SSL certificates:

.. code-block:: bash

   # Using Let's Encrypt
   # Configure in docker-compose.prod.yml
   
   # Or use custom certificates
   # Mount certificate files into Traefik container

Troubleshooting Service Access
-----------------------------

Common Issues
~~~~~~~~~~~~

**Service Not Accessible**:

.. code-block:: bash

   # Check Traefik routing
   curl http://localhost:8080/api/rawdata
   
   # Check service container status
   docker-compose ps
   
   # Check Traefik logs
   docker-compose logs traefik

**DNS Resolution Issues**:

.. code-block:: bash

   # Add to /etc/hosts (development)
   echo "127.0.0.1 app.feedme.localhost" >> /etc/hosts
   echo "127.0.0.1 admin.feedme.localhost" >> /etc/hosts
   echo "127.0.0.1 api.feedme.localhost" >> /etc/hosts
   echo "127.0.0.1 docs.feedme.localhost" >> /etc/hosts
   echo "127.0.0.1 dashboard.feedme.localhost" >> /etc/hosts
   echo "127.0.0.1 metrics.feedme.localhost" >> /etc/hosts

**SSL Certificate Issues**:

.. code-block:: bash

   # Check certificate status
   openssl s_client -connect api.feedme.localhost:443 -servername api.feedme.localhost
   
   # For development, accept self-signed certificates
   curl -k https://api.feedme.localhost/health

Service Integration Examples
---------------------------

Frontend to API Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~

The frontend automatically integrates with the API through Traefik:

.. code-block:: javascript

   // Frontend configuration
   const API_BASE_URL = 'https://api.feedme.localhost/api/v1';
   
   // API calls automatically routed through Traefik
   const response = await fetch(`${API_BASE_URL}/applications`, {
     headers: {
       'Authorization': `Bearer ${token}`,
       'Content-Type': 'application/json'
     }
   });

External System Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~

External systems can integrate through the API:

.. code-block:: python

   # Python integration example
   import requests
   
   class CVEFeedClient:
       def __init__(self, base_url="https://api.feedme.localhost/api/v1"):
           self.base_url = base_url
           self.token = None
       
       def login(self, username, password):
           response = requests.post(f"{self.base_url}/auth/login", json={
               "username": username,
               "password": password
           })
           self.token = response.json()["access_token"]
       
       def get_applications(self):
           headers = {"Authorization": f"Bearer {self.token}"}
           response = requests.get(f"{self.base_url}/applications", headers=headers)
           return response.json()

Next Steps
----------

* :doc:`docker-infrastructure-setup` - Initial infrastructure setup
* :doc:`authentication` - User authentication and API access
* :doc:`application-management` - Managing applications through the interface
* :doc:`troubleshooting-docker` - Advanced troubleshooting
