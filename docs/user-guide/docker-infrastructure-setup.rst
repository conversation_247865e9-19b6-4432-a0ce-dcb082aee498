Docker Infrastructure Setup
============================

This guide covers the complete setup and deployment of the CVE Feed Service Docker infrastructure with Traefik service discovery and *.feedme.localhost domain management.

Prerequisites
-------------

**System Requirements**:
* Docker Engine 20.10+ 
* Docker Compose 2.0+
* 4GB+ RAM available for containers
* 10GB+ disk space for data volumes
* Network access to NVD API (https://services.nvd.nist.gov)

**Network Requirements**:
* Ports 80, 443 available for Traefik
* Internal container networking enabled
* DNS resolution for *.feedme.localhost (or hosts file entries)

Installation Steps
------------------

1. <PERSON>lone and Setup
~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/your-org/cve-feed-service.git
   cd cve-feed-service
   
   # Copy environment configuration
   cp .env.example .env
   
   # Edit configuration as needed
   nano .env

2. Configure Environment
~~~~~~~~~~~~~~~~~~~~~~~

Edit the ``.env`` file to customize your deployment:

.. code-block:: bash

   # Database Configuration
   DATABASE_URL=postgresql+asyncpg://cve_user:cve_password_secure_2024@postgres:5432/cve_feed_db
   POSTGRES_DB=cve_feed_db
   POSTGRES_USER=cve_user
   POSTGRES_PASSWORD=cve_password_secure_2024
   
   # Redis Configuration
   REDIS_URL=redis://:redis_password_secure_2024@redis:6379/0
   REDIS_PASSWORD=redis_password_secure_2024
   
   # Application Security
   SECRET_KEY=your-super-secret-key-change-in-production-2024
   JWT_SECRET_KEY=your-jwt-secret-key-change-in-production-2024
   
   # Domain Configuration
   API_DOMAIN=api.feedme.localhost
   APP_DOMAIN=app.feedme.localhost
   ADMIN_DOMAIN=admin.feedme.localhost
   DOCS_DOMAIN=docs.feedme.localhost
   METRICS_DOMAIN=metrics.feedme.localhost
   DASHBOARD_DOMAIN=dashboard.feedme.localhost

3. Deploy Infrastructure
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Build and start all services
   make up-build
   
   # Or using docker-compose directly
   docker-compose up --build -d
   
   # Check service status
   make status
   
   # View logs
   make logs

4. Verify Deployment
~~~~~~~~~~~~~~~~~~~

Check that all services are healthy:

.. code-block:: bash

   # Check container health
   docker-compose ps
   
   # Test service endpoints
   curl -s https://api.feedme.localhost/health
   curl -s https://app.feedme.localhost/health
   
   # Check Traefik dashboard (if enabled)
   curl -s http://localhost:8080/api/rawdata

Service Architecture
-------------------

Container Overview
~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "Traefik (Host Network)"
           T[Traefik Router<br/>Port 80/443]
       end
       
       subgraph "Frontend Containers"
           FE[Frontend<br/>app.feedme.localhost<br/>Next.js + React]
           AD[Admin Dashboard<br/>admin.feedme.localhost<br/>React + Nginx]
           DOC[Documentation<br/>docs.feedme.localhost<br/>Sphinx + Nginx]
       end
       
       subgraph "Backend Containers"
           API[API Service<br/>api.feedme.localhost<br/>FastAPI + Python]
           WK[Background Worker<br/>CVE Processing<br/>Python + Celery]
       end
       
       subgraph "Data Containers"
           PG[(PostgreSQL<br/>Primary Database)]
           RD[(Redis<br/>Cache + Sessions)]
       end
       
       subgraph "Monitoring Containers"
           PR[Prometheus<br/>metrics.feedme.localhost<br/>Metrics Collection]
           GR[Grafana<br/>dashboard.feedme.localhost<br/>Visualization]
       end
       
       T --> FE
       T --> AD
       T --> DOC
       T --> API
       T --> PR
       T --> GR
       
       FE --> API
       AD --> API
       API --> PG
       API --> RD
       WK --> PG
       WK --> RD
       PR --> API
       GR --> PR

Domain Configuration
~~~~~~~~~~~~~~~~~~~

The infrastructure uses Traefik for service discovery with these domains:

**Frontend Services**:
* ``app.feedme.localhost`` - Main web application
* ``admin.feedme.localhost`` - Administrative dashboard
* ``docs.feedme.localhost`` - Documentation site

**Backend Services**:
* ``api.feedme.localhost`` - REST API endpoints
* ``metrics.feedme.localhost`` - Prometheus metrics
* ``dashboard.feedme.localhost`` - Grafana monitoring

**Development Tools** (development mode only):
* ``pgadmin.feedme.localhost`` - Database management
* ``redis.feedme.localhost`` - Redis management

Initial Configuration
--------------------

1. Database Initialization
~~~~~~~~~~~~~~~~~~~~~~~~~

The database is automatically initialized with:

.. code-block:: bash

   # Check database status
   make db-status
   
   # Run migrations (if needed)
   make db-migrate
   
   # Create initial admin user
   make create-admin-user

2. Import CVE Data
~~~~~~~~~~~~~~~~~

Import initial CVE data from NVD:

.. code-block:: bash

   # Import last 2 years of CVE data
   make import-cves YEARS=2
   
   # Or import specific year
   make import-cves YEAR=2023
   
   # Check import status
   make worker-status

3. Verify Services
~~~~~~~~~~~~~~~~~

Test all service endpoints:

.. code-block:: bash

   # Run health checks
   make health-check
   
   # Test API authentication
   curl -X POST "https://api.feedme.localhost/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username": "<EMAIL>", "password": "password123"}'
   
   # Access web interface
   open https://app.feedme.localhost

Development Mode
---------------

For development with hot reload and debugging tools:

.. code-block:: bash

   # Start in development mode
   make dev
   
   # Or with specific services
   docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d
   
   # Access development tools
   open https://pgadmin.feedme.localhost    # Database management
   open https://redis.feedme.localhost      # Redis management

Production Deployment
--------------------

For production deployment:

.. code-block:: bash

   # Use production configuration
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
   
   # Enable SSL/TLS (configure certificates)
   # Set production environment variables
   # Configure backup schedules
   # Set up monitoring alerts

Troubleshooting
--------------

Common Issues
~~~~~~~~~~~~

**Services not accessible**:

.. code-block:: bash

   # Check Traefik routing
   docker-compose logs traefik
   
   # Verify container health
   docker-compose ps
   
   # Check service logs
   docker-compose logs api
   docker-compose logs frontend

**Database connection issues**:

.. code-block:: bash

   # Check PostgreSQL status
   docker-compose logs postgres
   
   # Test database connection
   make db-test
   
   # Reset database (development only)
   make db-reset

**Performance issues**:

.. code-block:: bash

   # Check resource usage
   docker stats
   
   # View monitoring dashboard
   open https://dashboard.feedme.localhost
   
   # Check application metrics
   curl https://metrics.feedme.localhost/metrics

Next Steps
----------

After successful deployment:

1. **Configure Authentication**: Set up users and roles
2. **Import Applications**: Add your application inventory
3. **Set Up Monitoring**: Configure alerts and dashboards
4. **Schedule Maintenance**: Set up automated CVE updates
5. **Backup Configuration**: Configure database backups

See the following guides:
* :doc:`traefik-service-access` - Service access patterns
* :doc:`authentication` - User and API authentication
* :doc:`application-management` - Managing applications
* :doc:`troubleshooting-docker` - Advanced troubleshooting
