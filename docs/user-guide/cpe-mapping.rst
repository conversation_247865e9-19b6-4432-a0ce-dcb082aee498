CPE Mapping
===========

CPE (Common Platform Enumeration) mapping is the critical link between your application components and vulnerability data. This guide covers understanding CPE format, creating accurate mappings, and troubleshooting common issues.

Overview
--------

CPE mapping connects your components to the standardized identifiers used in CVE databases. Without accurate CPE mappings, the system cannot determine which vulnerabilities affect your applications.

**Key Concepts:**
* **CPE**: Standardized naming scheme for IT systems and software
* **Mapping**: Association between a component and its CPE identifier
* **Confidence**: How certain you are about the mapping accuracy
* **Source**: How the mapping was created (manual, automated, etc.)

CPE 2.3 Format
---------------

CPE 2.3 uses a structured format with 11 components:

.. code-block:: text

   cpe:2.3:part:vendor:product:version:update:edition:language:sw_edition:target_sw:target_hw:other

Component Breakdown
~~~~~~~~~~~~~~~~~~~

1. **cpe**: Always "cpe"
2. **2.3**: CPE version (always "2.3")
3. **part**: Component type
   - ``a`` = application
   - ``o`` = operating system  
   - ``h`` = hardware
4. **vendor**: Organization that makes the product
5. **product**: Name of the product
6. **version**: Version number
7. **update**: Update/patch level
8. **edition**: Product edition
9. **language**: Language
10. **sw_edition**: Software edition
11. **target_sw**: Target software
12. **target_hw**: Target hardware
13. **other**: Other information

**Wildcards:**
* ``*`` = Any value
* ``-`` = Not applicable/empty

CPE Examples
~~~~~~~~~~~~

**Web Server:**
.. code-block:: text

   cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*

**Database:**
.. code-block:: text

   cpe:2.3:a:postgresql:postgresql:13.8:*:*:*:*:*:*:*

**Operating System:**
.. code-block:: text

   cpe:2.3:o:canonical:ubuntu_linux:20.04:*:*:*:lts:*:*:*

**Library:**
.. code-block:: text

   cpe:2.3:a:fasterxml:jackson-databind:2.13.0:*:*:*:*:*:*:*

Creating CPE Mappings
---------------------

Basic CPE Mapping
~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/components/{component_id}/cpe-mappings" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
          "confidence": 1.0,
          "mapping_source": "manual"
        }'

**Response:**

.. code-block:: json

   {
     "id": "mapping-uuid",
     "component_id": "component-uuid",
     "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
     "confidence": 1.0,
     "mapping_source": "manual",
     "created_at": "2025-01-18T10:00:00Z",
     "updated_at": "2025-01-18T10:00:00Z",
     "deleted_at": null
   }

High Confidence Mapping
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/components/{component_id}/cpe-mappings" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "cpe_string": "cpe:2.3:a:postgresql:postgresql:13.8:*:*:*:*:*:*:*",
          "confidence": 1.0,
          "mapping_source": "official_documentation"
        }'

Uncertain Mapping
~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/components/{component_id}/cpe-mappings" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "cpe_string": "cpe:2.3:a:apache:struts:2.5.26:*:*:*:*:*:*:*",
          "confidence": 0.8,
          "mapping_source": "best_guess"
        }'

Minimal Mapping
~~~~~~~~~~~~~~~

Only CPE string is required:

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/components/{component_id}/cpe-mappings" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "cpe_string": "cpe:2.3:a:redis:redis:6.2.6:*:*:*:*:*:*:*"
        }'

Finding CPE Identifiers
-----------------------

Official CPE Dictionary
~~~~~~~~~~~~~~~~~~~~~~~

The NIST CPE Dictionary is the authoritative source:
* **URL**: https://nvd.nist.gov/products/cpe
* **Search**: Use product name and vendor
* **Verification**: Cross-reference with CVE data

NVD CVE Database
~~~~~~~~~~~~~~~~

Search existing CVEs to find CPE patterns:

.. code-block:: bash

   # Search for nginx CVEs to see CPE patterns
   curl "https://services.nvd.nist.gov/rest/json/cves/2.0?keywordSearch=nginx" \
        | jq '.vulnerabilities[].cve.configurations[].nodes[].cpeMatch[].criteria'

Vendor Documentation
~~~~~~~~~~~~~~~~~~~~

Many vendors provide CPE identifiers in their security advisories:
* Check vendor security pages
* Look for CVSS scores and CPE references
* Review vulnerability disclosure formats

Component-Specific Research
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**For Popular Software:**
1. Search the NVD database for the component name
2. Look at recent CVEs affecting the component
3. Extract CPE patterns from vulnerability data
4. Verify version format consistency

**For Custom/Internal Software:**
* May not have official CPE identifiers
* Consider creating custom CPE format
* Document your CPE conventions

Common CPE Patterns
-------------------

Web Servers
~~~~~~~~~~~

.. code-block:: text

   # Nginx
   cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*
   
   # Apache HTTP Server
   cpe:2.3:a:apache:http_server:2.4.41:*:*:*:*:*:*:*
   
   # Microsoft IIS
   cpe:2.3:a:microsoft:internet_information_services:10.0:*:*:*:*:*:*:*

Databases
~~~~~~~~~

.. code-block:: text

   # PostgreSQL
   cpe:2.3:a:postgresql:postgresql:13.8:*:*:*:*:*:*:*
   
   # MySQL
   cpe:2.3:a:oracle:mysql:8.0.28:*:*:*:*:*:*:*
   
   # MongoDB
   cpe:2.3:a:mongodb:mongodb:5.0.6:*:*:*:*:*:*:*

Programming Languages
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: text

   # Node.js
   cpe:2.3:a:nodejs:node.js:18.12.0:*:*:*:*:*:*:*
   
   # Python
   cpe:2.3:a:python:python:3.9.7:*:*:*:*:*:*:*
   
   # Java
   cpe:2.3:a:oracle:jre:1.8.0:update_321:*:*:*:*:*:*

Libraries and Frameworks
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: text

   # Spring Boot
   cpe:2.3:a:pivotal_software:spring_boot:2.7.0:*:*:*:*:*:*:*
   
   # Jackson Databind
   cpe:2.3:a:fasterxml:jackson-databind:2.13.0:*:*:*:*:*:*:*
   
   # jQuery
   cpe:2.3:a:jquery:jquery:3.6.0:*:*:*:*:*:*:*

Operating Systems
~~~~~~~~~~~~~~~~~

.. code-block:: text

   # Ubuntu
   cpe:2.3:o:canonical:ubuntu_linux:20.04:*:*:*:lts:*:*:*
   
   # CentOS
   cpe:2.3:o:centos:centos:8:*:*:*:*:*:*:*
   
   # Windows Server
   cpe:2.3:o:microsoft:windows_server_2019:*:*:*:*:*:*:*:*

Managing CPE Mappings
---------------------

Updating Mappings
~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Update confidence level
   curl -X PATCH "http://localhost:8000/api/v1/cpe-mappings/{mapping_id}" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "confidence": 0.9,
          "mapping_source": "verified_with_vendor"
        }'

   # Update CPE string
   curl -X PATCH "http://localhost:8000/api/v1/cpe-mappings/{mapping_id}" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "cpe_string": "cpe:2.3:a:nginx:nginx:1.22.0:*:*:*:*:*:*:*"
        }'

Deleting Mappings
~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -X DELETE "http://localhost:8000/api/v1/cpe-mappings/{mapping_id}" \
        -H "Authorization: Bearer YOUR_TOKEN"

Multiple Mappings per Component
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Components can have multiple CPE mappings:

.. code-block:: bash

   # Primary mapping
   curl -X POST "http://localhost:8000/api/v1/components/{component_id}/cpe-mappings" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "cpe_string": "cpe:2.3:a:apache:tomcat:9.0.56:*:*:*:*:*:*:*",
          "confidence": 1.0,
          "mapping_source": "official"
        }'

   # Alternative mapping for different naming convention
   curl -X POST "http://localhost:8000/api/v1/components/{component_id}/cpe-mappings" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "cpe_string": "cpe:2.3:a:apache:apache_tomcat:9.0.56:*:*:*:*:*:*:*",
          "confidence": 0.8,
          "mapping_source": "alternative_format"
        }'

Best Practices
--------------

Accuracy Guidelines
~~~~~~~~~~~~~~~~~~~

**Research Thoroughly:**
* Use official CPE dictionary when available
* Cross-reference with multiple CVE examples
* Verify vendor and product names
* Check version format consistency

**Confidence Levels:**
* ``1.0``: Official CPE from vendor or NIST
* ``0.9``: High confidence based on multiple sources
* ``0.8``: Good match but some uncertainty
* ``0.7``: Reasonable guess, needs verification
* ``0.5`` or lower: Uncertain, requires review

**Source Documentation:**
* ``official``: From vendor or NIST CPE dictionary
* ``nist_database``: Found in NVD CVE database
* ``vendor_advisory``: From vendor security advisory
* ``manual_research``: Manual research and verification
* ``automated_tool``: Generated by automated tool
* ``best_guess``: Educated guess, needs verification

Version Handling
~~~~~~~~~~~~~~~~

**Specific Versions:**
* Use exact version numbers when known
* Include patch levels when available
* Be consistent with version format

**Version Wildcards:**
* Use ``*`` for "any version" carefully
* Consider version ranges for broad matching
* Document wildcard usage decisions

**Version Updates:**
* Update CPE mappings when components are upgraded
* Maintain historical mappings for audit trails
* Consider automation for version updates

Quality Assurance
~~~~~~~~~~~~~~~~~

**Regular Reviews:**
* Audit CPE mappings periodically
* Verify mappings generate expected vulnerability matches
* Update confidence levels based on experience

**Testing Mappings:**
* Check if mappings return relevant CVEs
* Verify no false positives from incorrect mappings
* Test with known vulnerable versions

**Documentation:**
* Document CPE research process
* Maintain mapping rationale
* Share knowledge across team members

Common Workflows
----------------

New Component CPE Research
~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Identify Component Details**
   
   .. code-block:: bash

      # Get component information
      curl -H "Authorization: Bearer YOUR_TOKEN" \
           "http://localhost:8000/api/v1/components/{component_id}"

2. **Research CPE Identifier**
   
   * Search NIST CPE dictionary
   * Look for existing CVEs
   * Check vendor documentation

3. **Create Initial Mapping**
   
   .. code-block:: bash

      curl -X POST "http://localhost:8000/api/v1/components/{component_id}/cpe-mappings" \
           -H "Authorization: Bearer YOUR_TOKEN" \
           -H "Content-Type: application/json" \
           -d '{
             "cpe_string": "cpe:2.3:a:vendor:product:version:*:*:*:*:*:*:*",
             "confidence": 0.8,
             "mapping_source": "research"
           }'

4. **Verify Mapping**
   
   .. code-block:: bash

      # Check if mapping returns relevant CVEs
      curl -H "Authorization: Bearer YOUR_TOKEN" \
           "http://localhost:8000/api/v1/cves/feed?application_id={app_id}"

Component Version Update
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # 1. Update component version
   curl -X PATCH "http://localhost:8000/api/v1/components/{component_id}" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{"version": "1.22.0"}'

   # 2. Update CPE mapping
   curl -X PATCH "http://localhost:8000/api/v1/cpe-mappings/{mapping_id}" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "cpe_string": "cpe:2.3:a:nginx:nginx:1.22.0:*:*:*:*:*:*:*"
        }'

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Invalid CPE Format**
   * Error: CPE validation failed
   * Solution: Verify CPE 2.3 format with 13 components

**No Vulnerability Matches**
   * Issue: CPE mapping doesn't return expected CVEs
   * Solutions: 
     - Verify CPE format matches CVE database
     - Check vendor/product name variations
     - Research alternative CPE formats

**Too Many False Positives**
   * Issue: CPE mapping returns irrelevant vulnerabilities
   * Solutions:
     - Use more specific version information
     - Verify vendor and product names
     - Consider multiple specific mappings instead of wildcards

**Duplicate Mappings**
   * Error: CPE mapping already exists
   * Solution: Check existing mappings or update existing one

CPE Validation
~~~~~~~~~~~~~~

The system validates CPE format automatically:

.. code-block:: json

   {
     "detail": [
       {
         "loc": ["body", "cpe_string"],
         "msg": "Invalid CPE 2.3 format",
         "type": "value_error"
       }
     ]
   }

**Common Format Issues:**
* Wrong number of components (must be 13)
* Missing "cpe:2.3" prefix
* Invalid characters in components
* Incorrect separator usage
