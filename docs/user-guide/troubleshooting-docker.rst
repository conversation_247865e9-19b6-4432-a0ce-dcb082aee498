Troubleshooting Docker Infrastructure
=====================================

This guide covers common issues and solutions for the CVE Feed Service Docker infrastructure, including container problems, networking issues, and performance troubleshooting.

Common Container Issues
-----------------------

Container Won't Start
~~~~~~~~~~~~~~~~~~~~

**Symptoms**: Container exits immediately or fails to start

**Diagnosis**:

.. code-block:: bash

   # Check container status
   docker-compose ps
   
   # View container logs
   docker-compose logs <service-name>
   
   # Check for port conflicts
   docker-compose logs traefik
   
   # Inspect container configuration
   docker inspect <container-name>

**Common Solutions**:

.. code-block:: bash

   # Port already in use
   sudo lsof -i :80  # Check what's using port 80
   sudo lsof -i :443 # Check what's using port 443
   
   # Stop conflicting services
   sudo systemctl stop apache2  # Example: stop Apache
   sudo systemctl stop nginx    # Example: stop Nginx
   
   # Restart Docker service
   sudo systemctl restart docker
   
   # Rebuild containers
   docker-compose down
   docker-compose up --build -d

Database Connection Issues
~~~~~~~~~~~~~~~~~~~~~~~~~

**Symptoms**: API returns database connection errors

**Diagnosis**:

.. code-block:: bash

   # Check PostgreSQL container
   docker-compose logs postgres
   
   # Test database connection
   docker-compose exec postgres psql -U cve_user -d cve_feed_db -c "SELECT 1;"
   
   # Check database initialization
   docker-compose exec postgres psql -U cve_user -d cve_feed_db -c "\dt"

**Solutions**:

.. code-block:: bash

   # Reset database (development only)
   docker-compose down -v  # Removes volumes
   docker-compose up -d postgres
   
   # Wait for database to initialize
   sleep 30
   
   # Run migrations
   docker-compose exec api alembic upgrade head
   
   # Create initial data
   docker-compose exec api python -c "from scripts.create_sample_data import main; main()"

Service Health Check Failures
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Symptoms**: Services show as unhealthy in docker-compose ps

**Diagnosis**:

.. code-block:: bash

   # Check health check endpoint
   curl http://localhost:8000/health  # Direct container access
   curl https://api.feedme.localhost/health  # Through Traefik
   
   # Check container resource usage
   docker stats
   
   # View detailed health check logs
   docker inspect <container-name> | grep -A 10 "Health"

**Solutions**:

.. code-block:: bash

   # Increase health check timeout
   # In docker-compose.yml:
   healthcheck:
     test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
     interval: 30s
     timeout: 10s
     retries: 5
     start_period: 60s
   
   # Restart unhealthy containers
   docker-compose restart <service-name>

Networking Issues
----------------

Service Discovery Problems
~~~~~~~~~~~~~~~~~~~~~~~~~

**Symptoms**: Services can't communicate with each other

**Diagnosis**:

.. code-block:: bash

   # Check Docker networks
   docker network ls
   
   # Inspect CVE Feed network
   docker network inspect cve-feed-network
   
   # Test inter-container connectivity
   docker-compose exec api ping postgres
   docker-compose exec api ping redis

**Solutions**:

.. code-block:: bash

   # Recreate network
   docker-compose down
   docker network prune
   docker-compose up -d
   
   # Check service names in docker-compose.yml
   # Ensure services use correct hostnames

Traefik Routing Issues
~~~~~~~~~~~~~~~~~~~~~

**Symptoms**: Services not accessible via *.feedme.localhost domains

**Diagnosis**:

.. code-block:: bash

   # Check Traefik dashboard
   curl http://localhost:8080/api/rawdata
   
   # View Traefik logs
   docker-compose logs traefik
   
   # Check service labels
   docker inspect <container-name> | grep -A 20 "Labels"

**Solutions**:

.. code-block:: bash

   # Verify DNS resolution
   nslookup api.feedme.localhost
   
   # Add to /etc/hosts if needed
   echo "127.0.0.1 api.feedme.localhost" | sudo tee -a /etc/hosts
   echo "127.0.0.1 app.feedme.localhost" | sudo tee -a /etc/hosts
   
   # Restart Traefik
   docker-compose restart traefik
   
   # Check Traefik configuration
   docker-compose exec traefik cat /etc/traefik/traefik.yml

SSL/TLS Certificate Issues
~~~~~~~~~~~~~~~~~~~~~~~~~

**Symptoms**: HTTPS connections fail or show certificate warnings

**Diagnosis**:

.. code-block:: bash

   # Test SSL connection
   openssl s_client -connect api.feedme.localhost:443
   
   # Check certificate details
   curl -vI https://api.feedme.localhost/health

**Solutions**:

.. code-block:: bash

   # For development, accept self-signed certificates
   curl -k https://api.feedme.localhost/health
   
   # Or configure browser to accept self-signed certificates
   # Chrome: chrome://flags/#allow-insecure-localhost
   
   # For production, ensure proper certificates are configured
   # Check Let's Encrypt configuration in Traefik

Performance Issues
-----------------

High Memory Usage
~~~~~~~~~~~~~~~~

**Symptoms**: Containers consuming excessive memory

**Diagnosis**:

.. code-block:: bash

   # Monitor resource usage
   docker stats
   
   # Check individual container memory
   docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
   
   # View system memory
   free -h

**Solutions**:

.. code-block:: bash

   # Set memory limits in docker-compose.yml
   services:
     api:
       deploy:
         resources:
           limits:
             memory: 512M
           reservations:
             memory: 256M
   
   # Restart containers with limits
   docker-compose up -d

Slow Response Times
~~~~~~~~~~~~~~~~~~

**Symptoms**: API responses are slow, web interface is sluggish

**Diagnosis**:

.. code-block:: bash

   # Check database performance
   docker-compose exec postgres psql -U cve_user -d cve_feed_db -c "
     SELECT query, mean_exec_time, calls 
     FROM pg_stat_statements 
     ORDER BY mean_exec_time DESC 
     LIMIT 10;"
   
   # Monitor API response times
   curl -w "@curl-format.txt" -o /dev/null -s https://api.feedme.localhost/api/v1/applications
   
   # Check Redis cache hit rates
   docker-compose exec redis redis-cli info stats

**Solutions**:

.. code-block:: bash

   # Optimize database queries
   # Add indexes for frequently queried columns
   
   # Increase cache TTL
   # Configure Redis cache settings
   
   # Scale services horizontally
   docker-compose up --scale api=3 -d

Data and Volume Issues
---------------------

Volume Mount Problems
~~~~~~~~~~~~~~~~~~~~

**Symptoms**: Data not persisting between container restarts

**Diagnosis**:

.. code-block:: bash

   # Check volume mounts
   docker volume ls
   
   # Inspect volume details
   docker volume inspect cve-feed_postgres_data
   
   # Check mount points
   docker-compose exec postgres df -h

**Solutions**:

.. code-block:: bash

   # Recreate volumes (data will be lost)
   docker-compose down -v
   docker volume prune
   docker-compose up -d
   
   # Backup data before recreating
   docker-compose exec postgres pg_dump -U cve_user cve_feed_db > backup.sql

Disk Space Issues
~~~~~~~~~~~~~~~~

**Symptoms**: Containers fail due to insufficient disk space

**Diagnosis**:

.. code-block:: bash

   # Check disk usage
   df -h
   
   # Check Docker space usage
   docker system df
   
   # Check container logs size
   du -sh /var/lib/docker/containers/*/*-json.log

**Solutions**:

.. code-block:: bash

   # Clean up Docker resources
   docker system prune -a
   
   # Remove unused volumes
   docker volume prune
   
   # Configure log rotation
   # In docker-compose.yml:
   logging:
     driver: "json-file"
     options:
       max-size: "10m"
       max-file: "3"

Monitoring and Debugging
------------------------

Container Logs Analysis
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # View logs for all services
   docker-compose logs
   
   # Follow logs in real-time
   docker-compose logs -f api
   
   # View logs for specific time period
   docker-compose logs --since="2024-01-15T10:00:00" api
   
   # Search logs for errors
   docker-compose logs api 2>&1 | grep -i error

Performance Monitoring
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Monitor resource usage continuously
   watch docker stats
   
   # Check service health endpoints
   while true; do
     curl -s https://api.feedme.localhost/health | jq .
     sleep 5
   done
   
   # Monitor database connections
   docker-compose exec postgres psql -U cve_user -d cve_feed_db -c "
     SELECT count(*) as active_connections 
     FROM pg_stat_activity 
     WHERE state = 'active';"

Emergency Recovery Procedures
----------------------------

Complete System Reset
~~~~~~~~~~~~~~~~~~~~

**When to use**: System is completely broken and needs fresh start

.. code-block:: bash

   # Stop all services
   docker-compose down -v
   
   # Remove all containers and images
   docker system prune -a
   
   # Remove all volumes (DATA WILL BE LOST)
   docker volume prune
   
   # Rebuild from scratch
   docker-compose up --build -d
   
   # Restore data from backup
   # (if available)

Service-Specific Recovery
~~~~~~~~~~~~~~~~~~~~~~~~

**API Service Recovery**:

.. code-block:: bash

   # Restart API service only
   docker-compose restart api
   
   # Rebuild API container
   docker-compose up --build -d api
   
   # Check API logs
   docker-compose logs -f api

**Database Recovery**:

.. code-block:: bash

   # Restart database
   docker-compose restart postgres
   
   # Restore from backup
   docker-compose exec postgres psql -U cve_user -d cve_feed_db < backup.sql
   
   # Run migrations
   docker-compose exec api alembic upgrade head

Getting Help
-----------

**Log Collection for Support**:

.. code-block:: bash

   # Collect all logs
   docker-compose logs > cve-feed-logs.txt
   
   # Collect system information
   docker version > system-info.txt
   docker-compose version >> system-info.txt
   docker system df >> system-info.txt
   
   # Collect configuration
   docker-compose config > docker-compose-resolved.yml

**Useful Commands Reference**:

.. code-block:: bash

   # Quick health check
   make health-check
   
   # View all service status
   make status
   
   # Restart all services
   make restart
   
   # View logs
   make logs
   
   # Clean up resources
   make clean

Next Steps
----------

* :doc:`docker-infrastructure-setup` - Initial setup guide
* :doc:`traefik-service-access` - Service access patterns
* :doc:`../development/architecture` - Architecture documentation
* :doc:`../services/service-interactions` - Service interaction patterns
