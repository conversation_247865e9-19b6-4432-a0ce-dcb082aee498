API Reference
=============

The CVE Feed Service provides a comprehensive REST API for managing application inventories and accessing vulnerability data. This reference covers all available endpoints, request/response formats, and authentication requirements.

.. toctree::
   :maxdepth: 2

   authentication
   applications
   components
   vulnerabilities
   schemas
   errors

Overview
--------

**Base URL**: ``http://localhost:8000/api/v1``

**Authentication**: JWT Bearer tokens or API keys

**Content Type**: ``application/json``

**Response Format**: JSON

Quick Reference
---------------

Authentication Endpoints
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: text

   POST   /auth/login              # User login
   GET    /auth/me                 # Current user info
   POST   /auth/change-password    # Change password
   POST   /auth/users              # Create user (admin)
   GET    /auth/users              # List users (admin)
   PATCH  /auth/users/{id}         # Update user (admin)
   POST   /auth/api-keys           # Create API key
   GET    /auth/api-keys           # List API keys
   DELETE /auth/api-keys/{id}      # Delete API key

Application Management
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: text

   POST   /applications            # Create application
   GET    /applications            # List applications
   GET    /applications/{id}       # Get application details
   PATCH  /applications/{id}       # Update application
   DELETE /applications/{id}       # Delete application

Component Management
~~~~~~~~~~~~~~~~~~~~

.. code-block:: text

   POST   /applications/{id}/components     # Create component
   GET    /applications/{id}/components     # List app components
   GET    /components/{id}                  # Get component details
   PATCH  /components/{id}                  # Update component
   DELETE /components/{id}                  # Delete component

CPE Mapping
~~~~~~~~~~~

.. code-block:: text

   POST   /components/{id}/cpe-mappings     # Create CPE mapping
   PATCH  /cpe-mappings/{id}               # Update CPE mapping
   DELETE /cpe-mappings/{id}               # Delete CPE mapping

Vulnerability Feeds
~~~~~~~~~~~~~~~~~~~

.. code-block:: text

   GET    /cves/feed               # Tailored vulnerability feed
   GET    /cves/                   # General CVE listing
   GET    /cves/{cve_id}           # Specific CVE details

Interactive Documentation
-------------------------

When the service is running, interactive API documentation is available at:

* **Swagger UI**: ``http://localhost:8000/api/v1/docs``
* **ReDoc**: ``http://localhost:8000/api/v1/redoc``
* **OpenAPI JSON**: ``http://localhost:8000/api/v1/openapi.json``

Common Patterns
---------------

Authentication
~~~~~~~~~~~~~~

**JWT Token Authentication:**

.. code-block:: bash

   # Login
   curl -X POST "http://localhost:8000/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username": "user", "password": "pass"}'

   # Use token
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "http://localhost:8000/api/v1/applications"

**API Key Authentication:**

.. code-block:: bash

   curl -H "X-API-Key: YOUR_API_KEY" \
        "http://localhost:8000/api/v1/cves/feed"

Pagination
~~~~~~~~~~

Most list endpoints support pagination:

.. code-block:: bash

   # Default pagination
   curl "http://localhost:8000/api/v1/applications"

   # Custom pagination
   curl "http://localhost:8000/api/v1/applications?skip=20&limit=10"

**Response Format:**

.. code-block:: json

   {
     "items": [...],
     "total": 150,
     "limit": 10,
     "offset": 20,
     "has_more": true
   }

Filtering
~~~~~~~~~

Many endpoints support query parameter filtering:

.. code-block:: bash

   # Filter applications by environment
   curl "http://localhost:8000/api/v1/applications?environment=production"

   # Filter CVEs by severity
   curl "http://localhost:8000/api/v1/cves/feed?severity=HIGH"

   # Multiple filters
   curl "http://localhost:8000/api/v1/cves/?severity=CRITICAL&published_after=2025-01-01T00:00:00Z"

Error Handling
~~~~~~~~~~~~~~

The API returns standard HTTP status codes with detailed error messages:

.. code-block:: json

   {
     "detail": "Application with ID 12345 not found"
   }

**Common Status Codes:**
* ``200 OK``: Successful request
* ``201 Created``: Resource created successfully
* ``204 No Content``: Successful deletion
* ``400 Bad Request``: Invalid request data
* ``401 Unauthorized``: Authentication required
* ``403 Forbidden``: Insufficient permissions
* ``404 Not Found``: Resource not found
* ``422 Unprocessable Entity``: Validation error
* ``429 Too Many Requests``: Rate limit exceeded
* ``500 Internal Server Error``: Server error

Rate Limiting
~~~~~~~~~~~~~

The API implements rate limiting to ensure fair usage:

* **Authenticated Users**: 1000 requests per hour
* **API Keys**: 5000 requests per hour
* **Unauthenticated**: 100 requests per hour

Rate limit headers are included in responses:

.. code-block:: text

   X-RateLimit-Limit: 1000
   X-RateLimit-Remaining: 999
   X-RateLimit-Reset: 1642694400

Content Negotiation
~~~~~~~~~~~~~~~~~~~

The API supports JSON content type:

**Request Headers:**

.. code-block:: text

   Content-Type: application/json
   Accept: application/json

**Response Headers:**

.. code-block:: text

   Content-Type: application/json; charset=utf-8

Versioning
~~~~~~~~~~

The API uses URL path versioning:

* **Current Version**: ``/api/v1/``
* **Future Versions**: ``/api/v2/`` (when available)

Version information is included in responses:

.. code-block:: json

   {
     "api_version": "1.0",
     "service_version": "0.1.0"
   }

CORS Support
~~~~~~~~~~~~

The API supports Cross-Origin Resource Sharing (CORS) for web applications:

* **Allowed Origins**: Configurable (development: all origins)
* **Allowed Methods**: GET, POST, PATCH, DELETE, OPTIONS
* **Allowed Headers**: Authorization, Content-Type, X-API-Key

WebSocket Support
~~~~~~~~~~~~~~~~~

Currently not implemented. Future versions may include WebSocket endpoints for real-time vulnerability notifications.

SDK and Client Libraries
-------------------------

Official client libraries are planned for:

* **Python**: Native async/await support
* **JavaScript/TypeScript**: Browser and Node.js support
* **Go**: Concurrent request support
* **Java**: Spring Boot integration

Example client usage:

.. code-block:: python

   # Future Python SDK
   from cve_feed_client import CVEFeedClient

   client = CVEFeedClient(
       base_url="http://localhost:8000/api/v1",
       api_key="your-api-key"
   )

   # Get application vulnerabilities
   vulnerabilities = await client.get_application_vulnerabilities(
       application_id="app-uuid",
       severity="HIGH"
   )

Testing and Development
-----------------------

**Test Endpoints:**

The service includes endpoints for testing and development:

.. code-block:: bash

   # Health check
   curl "http://localhost:8000/health"

   # Readiness check
   curl "http://localhost:8000/readiness"

   # API documentation
   curl "http://localhost:8000/api/v1/openapi.json"

**Development Tools:**

* **API Documentation**: Swagger UI for interactive testing
* **Schema Validation**: Automatic request/response validation
* **Error Details**: Detailed error messages in development mode
* **Request Logging**: Comprehensive request/response logging

Migration Guide
---------------

When upgrading between versions:

1. **Check API Version**: Verify supported API version
2. **Review Changes**: Check changelog for breaking changes
3. **Update Clients**: Update client code for new features
4. **Test Integration**: Verify all endpoints work as expected

**Backward Compatibility:**

The API maintains backward compatibility within major versions:

* **v1.x**: All v1.x versions are backward compatible
* **v2.x**: May introduce breaking changes from v1.x

**Deprecation Policy:**

* **Notice Period**: 6 months minimum for deprecations
* **Migration Guide**: Provided for all breaking changes
* **Support Period**: Previous version supported for 12 months
