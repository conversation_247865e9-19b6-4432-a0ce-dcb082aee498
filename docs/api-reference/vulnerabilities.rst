Vulnerabilities API
===================

The vulnerabilities API provides access to CVE (Common Vulnerabilities and Exposures) data and tailored vulnerability feeds based on application component inventories.

Overview
--------

**Base Path**: ``/api/v1/cves``

**Authentication**: Required (JWT Bearer token or API key)

**Permissions**: 
* ``security_analyst``: Full access to vulnerability data
* ``it_admin``: Full access to vulnerability data

Vulnerability Data Flow
-----------------------

.. mermaid::

   graph TD
       A[NVD API] --> B[CVE Ingestion Service]
       B --> C[CVE Database]
       C --> D[CVE Service]
       D --> E[Tailored Feed API]
       
       F[Application Components] --> G[CPE Mappings]
       G --> D
       
       D --> H[General CVE API]
       D --> I[Specific CVE API]

Endpoints
---------

Tailored Vulnerability Feed
~~~~~~~~~~~~~~~~~~~~~~~~~~

GET /cves/feed
^^^^^^^^^^^^^^

Get a tailored vulnerability feed based on application components and their CPE mappings.

**Query Parameters**:
* ``application_id`` (UUID, required): Application ID to generate feed for
* ``severity`` (string, optional): Filter by CVSS severity (LOW, MEDIUM, HIGH, CRITICAL)
* ``limit`` (int, optional): Maximum records to return (default: 100, max: 1000)
* ``offset`` (int, optional): Number of records to skip (default: 0)

**Response** (200 OK):

.. code-block:: json

   {
     "cves": [
       {
         "id": "990e8400-e29b-41d4-a716-************",
         "cve_id": "CVE-2023-1234",
         "description": "nginx HTTP/2 implementation allows remote attackers to cause a denial of service",
         "published_date": "2023-03-15T10:00:00Z",
         "last_modified_date": "2023-03-16T14:30:00Z",
         "cvss_v3_score": 7.5,
         "cvss_v3_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H",
         "cvss_v3_severity": "HIGH",
         "cvss_v2_score": 7.8,
         "cwe_ids": ["CWE-400"],
         "references": [
           {
             "url": "https://nginx.org/en/security_advisories.html",
             "source": "vendor"
           },
           {
             "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-1234",
             "source": "nvd"
           }
         ]
       }
     ],
     "total": 15,
     "limit": 100,
     "offset": 0,
     "has_more": false,
     "application_id": "550e8400-e29b-41d4-a716-************"
   }

**Examples**:

.. code-block:: bash

   # Get all vulnerabilities for an application
   curl -H "X-API-Key: YOUR_API_KEY" \
        "http://localhost:8000/api/v1/cves/feed?application_id=550e8400-e29b-41d4-a716-************"
   
   # Get only HIGH and CRITICAL severity vulnerabilities
   curl -H "X-API-Key: YOUR_API_KEY" \
        "http://localhost:8000/api/v1/cves/feed?application_id=550e8400-e29b-41d4-a716-************&severity=HIGH"
   
   # Paginated results
   curl -H "X-API-Key: YOUR_API_KEY" \
        "http://localhost:8000/api/v1/cves/feed?application_id=550e8400-e29b-41d4-a716-************&limit=10&offset=20"

**Error Responses**:
* ``400 Bad Request``: Invalid application_id or parameters
* ``404 Not Found``: Application not found

General CVE Listing
~~~~~~~~~~~~~~~~~~

GET /cves/
^^^^^^^^^^

List all CVEs with optional filtering (not application-specific).

**Query Parameters**:
* ``severity`` (string, optional): Filter by CVSS severity
* ``published_after`` (datetime, optional): CVEs published after this date (ISO 8601)
* ``published_before`` (datetime, optional): CVEs published before this date (ISO 8601)
* ``limit`` (int, optional): Maximum records to return (default: 100, max: 1000)
* ``offset`` (int, optional): Number of records to skip (default: 0)

**Response** (200 OK):

.. code-block:: json

   {
     "cves": [
       {
         "id": "990e8400-e29b-41d4-a716-************",
         "cve_id": "CVE-2023-1234",
         "description": "nginx HTTP/2 implementation allows remote attackers to cause a denial of service",
         "published_date": "2023-03-15T10:00:00Z",
         "last_modified_date": "2023-03-16T14:30:00Z",
         "cvss_v3_score": 7.5,
         "cvss_v3_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H",
         "cvss_v3_severity": "HIGH",
         "cvss_v2_score": 7.8,
         "cwe_ids": ["CWE-400"],
         "references": [
           {
             "url": "https://nginx.org/en/security_advisories.html",
             "source": "vendor"
           }
         ]
       }
     ],
     "total": 1250,
     "limit": 100,
     "offset": 0,
     "has_more": true
   }

**Examples**:

.. code-block:: bash

   # List recent CRITICAL vulnerabilities
   curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        "http://localhost:8000/api/v1/cves/?severity=CRITICAL&published_after=2025-01-01T00:00:00Z"
   
   # List vulnerabilities from a date range
   curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        "http://localhost:8000/api/v1/cves/?published_after=2025-01-01T00:00:00Z&published_before=2025-01-31T23:59:59Z"

Specific CVE Details
~~~~~~~~~~~~~~~~~~~

GET /cves/{cve_id}
^^^^^^^^^^^^^^^^^^

Get detailed information about a specific CVE including CPE applicability.

**Path Parameters**:
* ``cve_id`` (string): CVE identifier (e.g., "CVE-2023-1234")

**Response** (200 OK):

.. code-block:: json

   {
     "id": "990e8400-e29b-41d4-a716-************",
     "cve_id": "CVE-2023-1234",
     "description": "nginx HTTP/2 implementation allows remote attackers to cause a denial of service via crafted HTTP/2 requests",
     "published_date": "2023-03-15T10:00:00Z",
     "last_modified_date": "2023-03-16T14:30:00Z",
     "cvss_v3_score": 7.5,
     "cvss_v3_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H",
     "cvss_v3_severity": "HIGH",
     "cvss_v2_score": 7.8,
     "cwe_ids": ["CWE-400", "CWE-770"],
     "references": [
       {
         "url": "https://nginx.org/en/security_advisories.html",
         "source": "vendor"
       },
       {
         "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-1234",
         "source": "nvd"
       },
       {
         "url": "https://github.com/nginx/nginx/commit/abc123",
         "source": "patch"
       }
     ],
     "cpe_applicability": [
       {
         "id": "aa0e8400-e29b-41d4-a716-446655440005",
         "cpe_string": "cpe:2.3:a:nginx:nginx:*:*:*:*:*:*:*:*",
         "version_start_including": "1.20.0",
         "version_end_including": "1.20.1",
         "version_start_excluding": null,
         "version_end_excluding": null,
         "vulnerable": true,
         "configuration_id": "config-1"
       },
       {
         "id": "bb0e8400-e29b-41d4-a716-446655440006",
         "cpe_string": "cpe:2.3:a:nginx:nginx:*:*:*:*:*:*:*:*",
         "version_start_including": "1.21.0",
         "version_end_excluding": "1.21.6",
         "version_start_excluding": null,
         "version_end_including": null,
         "vulnerable": true,
         "configuration_id": "config-2"
       }
     ]
   }

**Example**:

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        "http://localhost:8000/api/v1/cves/CVE-2023-1234"

**Error Responses**:
* ``404 Not Found``: CVE not found

Filtering and Search
-------------------

Severity Filtering
~~~~~~~~~~~~~~~~~

Filter vulnerabilities by CVSS v3.1 severity levels:

.. list-table:: Severity Levels
   :header-rows: 1
   :widths: 20 20 60

   * - Severity
     - Score Range
     - Description
   * - CRITICAL
     - 9.0 - 10.0
     - Critical vulnerabilities requiring immediate attention
   * - HIGH
     - 7.0 - 8.9
     - High-impact vulnerabilities requiring prompt remediation
   * - MEDIUM
     - 4.0 - 6.9
     - Medium-impact vulnerabilities for planned remediation
   * - LOW
     - 0.1 - 3.9
     - Low-impact vulnerabilities for routine maintenance

**Example**:

.. code-block:: bash

   # Get only CRITICAL vulnerabilities
   curl "http://localhost:8000/api/v1/cves/?severity=CRITICAL"

Date Range Filtering
~~~~~~~~~~~~~~~~~~~

Filter by publication or modification dates using ISO 8601 format:

.. code-block:: bash

   # CVEs published in the last 30 days
   curl "http://localhost:8000/api/v1/cves/?published_after=2025-01-01T00:00:00Z"
   
   # CVEs from a specific month
   curl "http://localhost:8000/api/v1/cves/?published_after=2025-01-01T00:00:00Z&published_before=2025-01-31T23:59:59Z"

Application-Specific Filtering
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The tailored feed automatically filters based on:

1. **Component CPE Mappings**: Only CVEs that affect components in the application
2. **Version Ranges**: Matches component versions against CVE applicability
3. **Confidence Levels**: Considers CPE mapping confidence in results

Response Format
--------------

CVE Object Structure
~~~~~~~~~~~~~~~~~~~

.. code-block:: json

   {
     "id": "UUID",
     "cve_id": "CVE-YYYY-NNNN",
     "description": "Vulnerability description",
     "published_date": "ISO 8601 datetime",
     "last_modified_date": "ISO 8601 datetime",
     "cvss_v3_score": "float (0.0-10.0)",
     "cvss_v3_vector": "CVSS v3.1 vector string",
     "cvss_v3_severity": "LOW|MEDIUM|HIGH|CRITICAL",
     "cvss_v2_score": "float (0.0-10.0) or null",
     "cwe_ids": ["CWE-XXX", ...],
     "references": [
       {
         "url": "reference URL",
         "source": "vendor|nvd|patch|advisory"
       }
     ]
   }

CPE Applicability Structure
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: json

   {
     "id": "UUID",
     "cpe_string": "CPE 2.3 string",
     "version_start_including": "version or null",
     "version_end_including": "version or null", 
     "version_start_excluding": "version or null",
     "version_end_excluding": "version or null",
     "vulnerable": "boolean",
     "configuration_id": "string identifier"
   }

Pagination Response
~~~~~~~~~~~~~~~~~~

.. code-block:: json

   {
     "cves": [...],
     "total": "total number of matching CVEs",
     "limit": "requested limit",
     "offset": "requested offset",
     "has_more": "boolean indicating more results available"
   }

Performance Considerations
-------------------------

Query Optimization
~~~~~~~~~~~~~~~~~

* Tailored feeds use optimized queries with proper indexing
* Large result sets are paginated automatically
* CPE matching uses efficient database joins
* Severity filtering uses indexed columns

Caching Strategy
~~~~~~~~~~~~~~~

* CVE data is relatively static and can be cached
* Tailored feeds can be cached per application
* Cache invalidation occurs when:
  - New CVEs are ingested
  - Application components are modified
  - CPE mappings are updated

Rate Limiting
~~~~~~~~~~~~

* Vulnerability endpoints have higher rate limits
* Tailored feeds: 100 requests per hour per application
* General CVE listing: 500 requests per hour
* Specific CVE details: 1000 requests per hour

Integration Patterns
-------------------

Automated Vulnerability Monitoring
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import asyncio
   import httpx
   
   async def monitor_application_vulnerabilities(app_id, api_key):
       """Monitor application for new vulnerabilities."""
       async with httpx.AsyncClient() as client:
           response = await client.get(
               f"http://localhost:8000/api/v1/cves/feed",
               params={"application_id": app_id, "severity": "HIGH"},
               headers={"X-API-Key": api_key}
           )
           
           if response.status_code == 200:
               data = response.json()
               
               if data["total"] > 0:
                   # Alert on new high-severity vulnerabilities
                   await send_vulnerability_alert(app_id, data["cves"])
               
               return data["cves"]

Vulnerability Reporting
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   def generate_vulnerability_report(app_id, api_key):
       """Generate vulnerability report for application."""
       
       # Get all vulnerabilities
       response = requests.get(
           f"http://localhost:8000/api/v1/cves/feed",
           params={"application_id": app_id},
           headers={"X-API-Key": api_key}
       )
       
       vulnerabilities = response.json()["cves"]
       
       # Group by severity
       by_severity = {}
       for cve in vulnerabilities:
           severity = cve["cvss_v3_severity"]
           if severity not in by_severity:
               by_severity[severity] = []
           by_severity[severity].append(cve)
       
       # Generate report
       report = {
           "application_id": app_id,
           "total_vulnerabilities": len(vulnerabilities),
           "by_severity": {
               severity: len(cves) 
               for severity, cves in by_severity.items()
           },
           "critical_cves": by_severity.get("CRITICAL", []),
           "generated_at": datetime.utcnow().isoformat()
       }
       
       return report

Continuous Monitoring
~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   async def continuous_vulnerability_monitoring():
       """Continuously monitor all applications for vulnerabilities."""
       
       # Get all applications
       apps_response = requests.get(
           "http://localhost:8000/api/v1/applications",
           headers={"X-API-Key": api_key}
       )
       
       applications = apps_response.json()
       
       for app in applications:
           # Check for new vulnerabilities
           vulns_response = requests.get(
               f"http://localhost:8000/api/v1/cves/feed",
               params={
                   "application_id": app["id"],
                   "severity": "CRITICAL"
               },
               headers={"X-API-Key": api_key}
           )
           
           critical_vulns = vulns_response.json()["cves"]
           
           if critical_vulns:
               await notify_security_team(app, critical_vulns)

Error Handling
-------------

Common error scenarios and responses:

**400 Bad Request**:
* Invalid application_id format
* Invalid date format in filters
* Invalid severity value

**404 Not Found**:
* Application not found for tailored feed
* CVE ID not found

**422 Unprocessable Entity**:
* Invalid query parameter combinations
* Date range validation errors

**429 Too Many Requests**:
* Rate limit exceeded
* Includes Retry-After header

Example error response:

.. code-block:: json

   {
     "detail": "Application with ID 550e8400-e29b-41d4-a716-************ not found",
     "type": "not_found_error"
   }

Next Steps
----------

* :doc:`schemas` - Complete schema reference
* :doc:`errors` - Detailed error handling guide
* :doc:`../user-guide/vulnerability-feeds` - User guide for vulnerability feeds
* :doc:`../services/cve-service` - CVE service implementation details
