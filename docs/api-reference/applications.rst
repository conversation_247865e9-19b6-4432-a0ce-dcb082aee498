Applications API
================

The applications API provides endpoints for managing application inventory. Applications represent software systems that contain components which may have vulnerabilities.

Overview
--------

**Base Path**: ``/api/v1/applications``

**Authentication**: Required (JWT Bearer token or API key)

**Permissions**: 
* ``security_analyst``: Full access to application management
* ``it_admin``: Full access to application management

Application Lifecycle
---------------------

.. mermaid::

   graph TD
       A[Create Application] --> B[Add Components]
       B --> C[Map CPEs]
       C --> D[Query Vulnerabilities]
       D --> E[Monitor & Update]
       E --> F[Archive/Delete]
       
       B --> G[Update Application]
       G --> B
       
       C --> H[Update Components]
       H --> C

Endpoints
---------

Create Application
~~~~~~~~~~~~~~~~~

POST /applications
^^^^^^^^^^^^^^^^^^

Create a new application in the inventory.

**Request Body**:

.. code-block:: json

   {
     "name": "Customer Portal",
     "description": "Main customer-facing web application",
     "version": "2.1.0",
     "owner": "Platform Team",
     "environment": "production",
     "criticality": "high"
   }

**Required Fields**:
* ``name`` (string): Application name (1-255 characters)

**Optional Fields**:
* ``description`` (string): Application description
* ``version`` (string): Application version
* ``owner`` (string): Team or person responsible
* ``environment`` (string): Deployment environment (dev, staging, production, etc.)
* ``criticality`` (string): Business criticality (low, medium, high, critical)

**Response** (201 Created):

.. code-block:: json

   {
     "id": "550e8400-e29b-41d4-a716-************",
     "name": "Customer Portal",
     "description": "Main customer-facing web application",
     "version": "2.1.0",
     "owner": "Platform Team",
     "environment": "production",
     "criticality": "high",
     "created_at": "2025-01-15T10:00:00Z",
     "updated_at": "2025-01-15T10:00:00Z"
   }

**Example**:

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/applications" \
        -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "Customer Portal",
          "description": "Main customer-facing web application",
          "environment": "production",
          "criticality": "high"
        }'

**Error Responses**:
* ``400 Bad Request``: Application name already exists in environment
* ``422 Unprocessable Entity``: Validation errors

List Applications
~~~~~~~~~~~~~~~~

GET /applications
^^^^^^^^^^^^^^^^^

List applications with optional filtering and pagination.

**Query Parameters**:
* ``skip`` (int, optional): Number of records to skip (default: 0)
* ``limit`` (int, optional): Maximum records to return (default: 100, max: 1000)
* ``environment`` (string, optional): Filter by environment
* ``criticality`` (string, optional): Filter by criticality level

**Response** (200 OK):

.. code-block:: json

   [
     {
       "id": "550e8400-e29b-41d4-a716-************",
       "name": "Customer Portal",
       "description": "Main customer-facing web application",
       "version": "2.1.0",
       "owner": "Platform Team",
       "environment": "production",
       "criticality": "high",
       "created_at": "2025-01-15T10:00:00Z",
       "updated_at": "2025-01-15T10:00:00Z"
     },
     {
       "id": "660e8400-e29b-41d4-a716-446655440001",
       "name": "Admin Dashboard",
       "description": "Internal administration interface",
       "version": "1.5.2",
       "owner": "Backend Team",
       "environment": "production",
       "criticality": "medium",
       "created_at": "2025-01-10T14:30:00Z",
       "updated_at": "2025-01-12T09:15:00Z"
     }
   ]

**Examples**:

.. code-block:: bash

   # List all applications
   curl -H "X-API-Key: YOUR_API_KEY" \
        "http://localhost:8000/api/v1/applications"
   
   # Filter by environment
   curl -H "X-API-Key: YOUR_API_KEY" \
        "http://localhost:8000/api/v1/applications?environment=production"
   
   # Filter by criticality with pagination
   curl -H "X-API-Key: YOUR_API_KEY" \
        "http://localhost:8000/api/v1/applications?criticality=high&limit=10&skip=0"
   
   # Multiple filters
   curl -H "X-API-Key: YOUR_API_KEY" \
        "http://localhost:8000/api/v1/applications?environment=production&criticality=high"

Get Application
~~~~~~~~~~~~~~

GET /applications/{application_id}
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Retrieve a specific application with optional component details.

**Path Parameters**:
* ``application_id`` (UUID): Application ID

**Query Parameters**:
* ``include_components`` (boolean, optional): Include application components (default: true)

**Response** (200 OK):

.. code-block:: json

   {
     "id": "550e8400-e29b-41d4-a716-************",
     "name": "Customer Portal",
     "description": "Main customer-facing web application",
     "version": "2.1.0",
     "owner": "Platform Team",
     "environment": "production",
     "criticality": "high",
     "created_at": "2025-01-15T10:00:00Z",
     "updated_at": "2025-01-15T10:00:00Z",
     "components": [
       {
         "id": "770e8400-e29b-41d4-a716-************",
         "name": "nginx",
         "version": "1.20.1",
         "vendor": "nginx",
         "component_type": "web_server",
         "description": "Web server and reverse proxy",
         "created_at": "2025-01-15T10:05:00Z",
         "updated_at": "2025-01-15T10:05:00Z",
         "cpe_mappings": [
           {
             "id": "880e8400-e29b-41d4-a716-************",
             "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
             "confidence": 1.0,
             "mapping_source": "official",
             "created_at": "2025-01-15T10:06:00Z"
           }
         ]
       }
     ]
   }

**Examples**:

.. code-block:: bash

   # Get application with components
   curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        "http://localhost:8000/api/v1/applications/550e8400-e29b-41d4-a716-************"
   
   # Get application without components
   curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        "http://localhost:8000/api/v1/applications/550e8400-e29b-41d4-a716-************?include_components=false"

**Error Responses**:
* ``404 Not Found``: Application not found

Update Application
~~~~~~~~~~~~~~~~~

PATCH /applications/{application_id}
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Update an existing application. All fields are optional.

**Path Parameters**:
* ``application_id`` (UUID): Application ID

**Request Body** (all fields optional):

.. code-block:: json

   {
     "description": "Updated description",
     "version": "2.2.0",
     "owner": "New Team",
     "environment": "staging",
     "criticality": "medium"
   }

**Response** (200 OK):

.. code-block:: json

   {
     "id": "550e8400-e29b-41d4-a716-************",
     "name": "Customer Portal",
     "description": "Updated description",
     "version": "2.2.0",
     "owner": "New Team",
     "environment": "staging",
     "criticality": "medium",
     "created_at": "2025-01-15T10:00:00Z",
     "updated_at": "2025-01-15T15:30:00Z"
   }

**Example**:

.. code-block:: bash

   curl -X PATCH "http://localhost:8000/api/v1/applications/550e8400-e29b-41d4-a716-************" \
        -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "version": "2.2.0",
          "criticality": "medium"
        }'

**Error Responses**:
* ``400 Bad Request``: Name conflict with existing application
* ``404 Not Found``: Application not found
* ``422 Unprocessable Entity``: Validation errors

Delete Application
~~~~~~~~~~~~~~~~~

DELETE /applications/{application_id}
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Soft delete an application and all its components.

**Path Parameters**:
* ``application_id`` (UUID): Application ID

**Response** (204 No Content): Empty response body

**Example**:

.. code-block:: bash

   curl -X DELETE "http://localhost:8000/api/v1/applications/550e8400-e29b-41d4-a716-************" \
        -H "Authorization: Bearer YOUR_JWT_TOKEN"

**Error Responses**:
* ``404 Not Found``: Application not found

.. warning::
   Deleting an application will also soft delete all associated components and CPE mappings. This action cannot be undone through the API.

Business Rules
-------------

Application Naming
~~~~~~~~~~~~~~~~~

* Application names must be unique within an environment
* Names are case-sensitive
* Maximum length: 255 characters
* Minimum length: 1 character

Environment Values
~~~~~~~~~~~~~~~~~

Common environment values (not enforced):
* ``development`` / ``dev``
* ``testing`` / ``test``
* ``staging`` / ``stage``
* ``production`` / ``prod``

Criticality Levels
~~~~~~~~~~~~~~~~~

Recommended criticality levels (not enforced):
* ``low``: Non-critical systems
* ``medium``: Important but not critical
* ``high``: Business-critical systems
* ``critical``: Mission-critical systems

Data Validation
~~~~~~~~~~~~~~

* All string fields are trimmed of whitespace
* Empty strings are converted to null
* Version strings support semantic versioning format
* Owner field accepts team names or email addresses

Filtering and Search
-------------------

Environment Filtering
~~~~~~~~~~~~~~~~~~~~

Filter applications by deployment environment:

.. code-block:: bash

   # Production applications only
   curl "http://localhost:8000/api/v1/applications?environment=production"

Criticality Filtering
~~~~~~~~~~~~~~~~~~~~

Filter applications by business criticality:

.. code-block:: bash

   # High and critical applications
   curl "http://localhost:8000/api/v1/applications?criticality=high"

Combined Filtering
~~~~~~~~~~~~~~~~~

Multiple filters can be combined:

.. code-block:: bash

   # High criticality production applications
   curl "http://localhost:8000/api/v1/applications?environment=production&criticality=high"

Pagination
~~~~~~~~~

Use skip and limit for pagination:

.. code-block:: bash

   # Second page of 20 applications
   curl "http://localhost:8000/api/v1/applications?skip=20&limit=20"

Integration Patterns
-------------------

Bulk Operations
~~~~~~~~~~~~~~

For bulk application creation, make multiple API calls:

.. code-block:: python

   import asyncio
   import httpx
   
   async def create_applications(applications, api_key):
       async with httpx.AsyncClient() as client:
           tasks = []
           for app_data in applications:
               task = client.post(
                   "http://localhost:8000/api/v1/applications",
                   json=app_data,
                   headers={"X-API-Key": api_key}
               )
               tasks.append(task)
           
           responses = await asyncio.gather(*tasks)
           return [r.json() for r in responses if r.status_code == 201]

Application Discovery
~~~~~~~~~~~~~~~~~~~

Integrate with infrastructure discovery tools:

.. code-block:: python

   # Example integration with infrastructure scanning
   discovered_apps = infrastructure_scanner.discover_applications()
   
   for app_info in discovered_apps:
       app_data = {
           "name": app_info.name,
           "environment": app_info.environment,
           "version": app_info.version,
           "owner": app_info.team
       }
       
       response = requests.post(
           "http://localhost:8000/api/v1/applications",
           json=app_data,
           headers={"X-API-Key": api_key}
       )

Monitoring Integration
~~~~~~~~~~~~~~~~~~~~

Track application changes for compliance:

.. code-block:: python

   # Monitor application updates
   def track_application_change(app_id, changes):
       audit_log.record({
           "event": "application_updated",
           "application_id": app_id,
           "changes": changes,
           "timestamp": datetime.utcnow(),
           "user": current_user.username
       })

Performance Considerations
-------------------------

* List operations support pagination to handle large inventories
* Include components only when needed to reduce response size
* Use filtering to reduce network traffic
* Consider caching application lists for read-heavy workloads

Next Steps
----------

* :doc:`components` - Managing application components
* :doc:`vulnerabilities` - Querying vulnerabilities for applications
* :doc:`schemas` - Complete schema reference
* :doc:`../user-guide/application-management` - User guide for application management
