/**
 * Login page E2E tests
 */

import { test, expect } from '@playwright/test';

test.describe('Login Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
  });

  test('displays login form correctly', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/CVE Feed Service/);
    
    // Check form elements
    await expect(page.locator('h1')).toContainText('Sign in to CVE Feed Service');
    await expect(page.locator('[data-testid=email-input]')).toBeVisible();
    await expect(page.locator('[data-testid=password-input]')).toBeVisible();
    await expect(page.locator('[data-testid=login-button]')).toBeVisible();
    await expect(page.locator('[data-testid=forgot-password-link]')).toBeVisible();
  });

  test('shows validation errors for empty fields', async ({ page }) => {
    // Try to submit without filling fields
    await page.click('[data-testid=login-button]');
    
    // Should show validation errors
    await expect(page.locator('text=Email is required')).toBeVisible();
    await expect(page.locator('text=Password is required')).toBeVisible();
  });

  test('shows validation error for invalid email', async ({ page }) => {
    // Enter invalid email
    await page.fill('[data-testid=email-input]', 'invalid-email');
    await page.fill('[data-testid=password-input]', 'password123');
    
    // Try to submit
    await page.click('[data-testid=login-button]');
    
    // Should show email validation error
    await expect(page.locator('text=Please enter a valid email address')).toBeVisible();
  });

  test('handles login form interaction', async ({ page }) => {
    // Fill in the form
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=password-input]', 'password123');
    
    // Check remember me
    await page.check('[data-testid=remember-me-toggle]');
    
    // Verify form is filled
    await expect(page.locator('[data-testid=email-input]')).toHaveValue('<EMAIL>');
    await expect(page.locator('[data-testid=password-input]')).toHaveValue('password123');
    await expect(page.locator('[data-testid=remember-me-toggle]')).toBeChecked();
  });

  test('password field toggles visibility', async ({ page }) => {
    const passwordField = page.locator('[data-testid=password-input]');
    
    // Initially should be password type
    await expect(passwordField).toHaveAttribute('type', 'password');
    
    // Fill password
    await passwordField.fill('mypassword');
    
    // Click show/hide button
    await page.click('button[aria-label*="password"]');
    
    // Should now be text type
    await expect(passwordField).toHaveAttribute('type', 'text');
    
    // Click again to hide
    await page.click('button[aria-label*="password"]');
    
    // Should be password type again
    await expect(passwordField).toHaveAttribute('type', 'password');
  });

  test('forgot password link works', async ({ page }) => {
    await page.click('[data-testid=forgot-password-link]');
    
    // Should navigate to forgot password page
    await expect(page).toHaveURL('/forgot-password');
  });

  test('sign up link works', async ({ page }) => {
    await page.click('text=Sign up here');
    
    // Should navigate to registration page
    await expect(page).toHaveURL('/register');
  });

  test('form is accessible', async ({ page }) => {
    // Check for proper labels
    await expect(page.locator('label[for*="email"]')).toBeVisible();
    await expect(page.locator('label[for*="password"]')).toBeVisible();
    
    // Check tab navigation
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid=email-input]')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid=password-input]')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid=remember-me-toggle]')).toBeFocused();
  });

  test('responsive design works on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Form should still be visible and functional
    await expect(page.locator('[data-testid=email-input]')).toBeVisible();
    await expect(page.locator('[data-testid=password-input]')).toBeVisible();
    await expect(page.locator('[data-testid=login-button]')).toBeVisible();
    
    // Form should be properly sized
    const loginButton = page.locator('[data-testid=login-button]');
    const buttonBox = await loginButton.boundingBox();
    expect(buttonBox?.width).toBeGreaterThan(200); // Should be wide enough for touch
  });
});
