/**
 * CVE API using RTK Query
 */

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../index';
import { config } from '../../config/environment';

export const cveApi = createApi({
  reducerPath: 'cveApi',
  baseQuery: fetchBaseQuery({
    baseUrl: `${config.apiBaseUrl}/cves`,
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['CVE'],
  endpoints: (builder) => ({
    getCVEFeed: builder.query<any, any>({
      query: (params) => ({
        url: '/feed',
        params,
      }),
      providesTags: ['CVE'],
    }),
  }),
});

export const { useGetCVEFeedQuery } = cveApi;
