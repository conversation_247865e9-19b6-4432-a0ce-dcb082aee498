/**
 * Authentication API using RTK Query
 */

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../index';
import { config } from '../../config/environment';
import type {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  TokenRefreshRequest,
  TokenRefreshResponse,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  ResetPasswordRequest,
  ResetPasswordResponse,
  VerifyEmailRequest,
  VerifyEmailResponse,
  User,
  ApiKey,
  CreateApiKeyRequest,
  CreateApiKeyResponse,
} from '../../types/auth';

export const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery: fetchBaseQuery({
    baseUrl: `${config.apiBaseUrl}/auth`,
    prepareHeaders: (headers, { getState }) => {
      // Add auth token to requests
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      
      // Add common headers
      headers.set('content-type', 'application/json');
      headers.set('accept', 'application/json');
      
      return headers;
    },
  }),
  tagTypes: ['User', 'ApiKey', 'Session'],
  endpoints: (builder) => ({
    // Authentication endpoints
    login: builder.mutation<LoginResponse, LoginRequest>({
      query: (credentials) => ({
        url: '/login',
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['User', 'Session'],
    }),
    
    logout: builder.mutation<void, void>({
      query: () => ({
        url: '/logout',
        method: 'POST',
      }),
      invalidatesTags: ['User', 'Session', 'ApiKey'],
    }),
    
    refreshToken: builder.mutation<TokenRefreshResponse, TokenRefreshRequest>({
      query: (refreshData) => ({
        url: '/refresh',
        method: 'POST',
        body: refreshData,
      }),
    }),
    
    // Registration endpoints
    register: builder.mutation<RegisterResponse, RegisterRequest>({
      query: (userData) => ({
        url: '/register',
        method: 'POST',
        body: userData,
      }),
    }),
    
    verifyEmail: builder.mutation<VerifyEmailResponse, VerifyEmailRequest>({
      query: (verificationData) => ({
        url: '/verify-email',
        method: 'POST',
        body: verificationData,
      }),
    }),
    
    resendVerification: builder.mutation<{ message: string }, { email: string }>({
      query: (emailData) => ({
        url: '/resend-verification',
        method: 'POST',
        body: emailData,
      }),
    }),
    
    // Password reset endpoints
    forgotPassword: builder.mutation<ForgotPasswordResponse, ForgotPasswordRequest>({
      query: (emailData) => ({
        url: '/forgot-password',
        method: 'POST',
        body: emailData,
      }),
    }),
    
    resetPassword: builder.mutation<ResetPasswordResponse, ResetPasswordRequest>({
      query: (resetData) => ({
        url: '/reset-password',
        method: 'POST',
        body: resetData,
      }),
    }),
    
    // User profile endpoints
    getCurrentUser: builder.query<User, void>({
      query: () => '/me',
      providesTags: ['User'],
    }),
    
    updateProfile: builder.mutation<User, Partial<User>>({
      query: (userData) => ({
        url: '/me',
        method: 'PATCH',
        body: userData,
      }),
      invalidatesTags: ['User'],
    }),
    
    changePassword: builder.mutation<{ message: string }, { current_password: string; new_password: string }>({
      query: (passwordData) => ({
        url: '/change-password',
        method: 'POST',
        body: passwordData,
      }),
    }),
    
    // API Key management endpoints
    getApiKeys: builder.query<ApiKey[], void>({
      query: () => '/api-keys',
      providesTags: ['ApiKey'],
    }),
    
    createApiKey: builder.mutation<CreateApiKeyResponse, CreateApiKeyRequest>({
      query: (keyData) => ({
        url: '/api-keys',
        method: 'POST',
        body: keyData,
      }),
      invalidatesTags: ['ApiKey'],
    }),
    
    revokeApiKey: builder.mutation<{ message: string }, string>({
      query: (keyId) => ({
        url: `/api-keys/${keyId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['ApiKey'],
    }),
    
    // Session management endpoints
    getSessions: builder.query<any[], void>({
      query: () => '/sessions',
      providesTags: ['Session'],
    }),
    
    revokeSession: builder.mutation<{ message: string }, string>({
      query: (sessionId) => ({
        url: `/sessions/${sessionId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Session'],
    }),
    
    // Username availability check
    checkUsernameAvailability: builder.query<{ available: boolean; suggestions?: string[] }, string>({
      query: (username) => `/check-username?username=${encodeURIComponent(username)}`,
    }),
    
    // Email availability check
    checkEmailAvailability: builder.query<{ available: boolean }, string>({
      query: (email) => `/check-email?email=${encodeURIComponent(email)}`,
    }),
  }),
});

export const {
  // Authentication hooks
  useLoginMutation,
  useLogoutMutation,
  useRefreshTokenMutation,
  
  // Registration hooks
  useRegisterMutation,
  useVerifyEmailMutation,
  useResendVerificationMutation,
  
  // Password reset hooks
  useForgotPasswordMutation,
  useResetPasswordMutation,
  
  // User profile hooks
  useGetCurrentUserQuery,
  useUpdateProfileMutation,
  useChangePasswordMutation,
  
  // API Key hooks
  useGetApiKeysQuery,
  useCreateApiKeyMutation,
  useRevokeApiKeyMutation,
  
  // Session hooks
  useGetSessionsQuery,
  useRevokeSessionMutation,
  
  // Validation hooks
  useCheckUsernameAvailabilityQuery,
  useCheckEmailAvailabilityQuery,
} = authApi;
