/**
 * Dashboard API using RTK Query
 */

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../index';
import { config } from '../../config/environment';

export const dashboardApi = createApi({
  reducerPath: 'dashboardApi',
  baseQuery: fetchBaseQuery({
    baseUrl: `${config.apiBaseUrl}/dashboard`,
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Metrics'],
  endpoints: (builder) => ({
    getMetrics: builder.query<any, void>({
      query: () => '/metrics',
      providesTags: ['Metrics'],
    }),
  }),
});

export const { useGetMetricsQuery } = dashboardApi;
