/**
 * Authentication slice for Redux store
 */

import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { AuthState, User, AuthError } from '../../types/auth';
import { config } from '../../config/environment';

const initialState: AuthState = {
  user: null,
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  lastActivity: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Login actions
    loginStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    loginSuccess: (state, action: PayloadAction<{ user: User; token: string; refreshToken?: string }>) => {
      state.isLoading = false;
      state.isAuthenticated = true;
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.refreshToken = action.payload.refreshToken || null;
      state.error = null;
      state.lastActivity = Date.now();
    },
    loginFailure: (state, action: PayloadAction<AuthError>) => {
      state.isLoading = false;
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      state.refreshToken = null;
      state.error = action.payload.message;
    },
    
    // Logout actions
    logout: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      state.refreshToken = null;
      state.error = null;
      state.lastActivity = null;
    },
    
    // Token refresh actions
    refreshTokenStart: (state) => {
      state.isLoading = true;
    },
    refreshTokenSuccess: (state, action: PayloadAction<{ token: string; user?: User }>) => {
      state.isLoading = false;
      state.token = action.payload.token;
      if (action.payload.user) {
        state.user = action.payload.user;
      }
      state.lastActivity = Date.now();
    },
    refreshTokenFailure: (state, action: PayloadAction<AuthError>) => {
      state.isLoading = false;
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      state.refreshToken = null;
      state.error = action.payload.message;
    },
    
    // User profile actions
    updateUserProfile: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
    
    // Activity tracking
    updateLastActivity: (state) => {
      state.lastActivity = Date.now();
    },
    
    // Error handling
    clearError: (state) => {
      state.error = null;
    },
    
    // Session management
    checkSession: (state) => {
      const now = Date.now();
      const sessionTimeout = config.security.sessionTimeout;
      
      if (state.lastActivity && (now - state.lastActivity) > sessionTimeout) {
        // Session expired
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.refreshToken = null;
        state.error = 'Session expired. Please log in again.';
        state.lastActivity = null;
      }
    },
    
    // Registration actions
    registrationStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    registrationSuccess: (state) => {
      state.isLoading = false;
      state.error = null;
    },
    registrationFailure: (state, action: PayloadAction<AuthError>) => {
      state.isLoading = false;
      state.error = action.payload.message;
    },
    
    // Password reset actions
    passwordResetStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    passwordResetSuccess: (state) => {
      state.isLoading = false;
      state.error = null;
    },
    passwordResetFailure: (state, action: PayloadAction<AuthError>) => {
      state.isLoading = false;
      state.error = action.payload.message;
    },
  },
});

export const {
  loginStart,
  loginSuccess,
  loginFailure,
  logout,
  refreshTokenStart,
  refreshTokenSuccess,
  refreshTokenFailure,
  updateUserProfile,
  updateLastActivity,
  clearError,
  checkSession,
  registrationStart,
  registrationSuccess,
  registrationFailure,
  passwordResetStart,
  passwordResetSuccess,
  passwordResetFailure,
} = authSlice.actions;

// Selectors
export const selectAuth = (state: { auth: AuthState }) => state.auth;
export const selectUser = (state: { auth: AuthState }) => state.auth.user;
export const selectIsAuthenticated = (state: { auth: AuthState }) => state.auth.isAuthenticated;
export const selectAuthLoading = (state: { auth: AuthState }) => state.auth.isLoading;
export const selectAuthError = (state: { auth: AuthState }) => state.auth.error;
export const selectToken = (state: { auth: AuthState }) => state.auth.token;

// Helper selectors
export const selectUserRole = (state: { auth: AuthState }) => state.auth.user?.role;
export const selectUserPermissions = (state: { auth: AuthState }) => {
  const role = state.auth.user?.role;
  // This would typically come from a permissions mapping
  // For now, return basic permissions based on role
  switch (role) {
    case 'admin':
      return ['read', 'write', 'delete', 'admin'];
    case 'security_analyst':
      return ['read', 'write'];
    case 'application_owner':
      return ['read', 'write_own'];
    case 'read_only':
      return ['read'];
    default:
      return [];
  }
};

export const selectIsSessionExpired = (state: { auth: AuthState }) => {
  const { lastActivity } = state.auth;
  if (!lastActivity) return false;
  
  const now = Date.now();
  const sessionTimeout = config.security.sessionTimeout;
  return (now - lastActivity) > sessionTimeout;
};

export default authSlice.reducer;
