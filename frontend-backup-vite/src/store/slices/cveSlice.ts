/**
 * <PERSON><PERSON> slice for managing CVE-related state
 */

import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { CVE, CVEFeedRequest, CVEListState } from '../../types/cve';

const initialState: CVEListState = {
  cves: [],
  loading: false,
  error: null,
  filters: {
    limit: 20,
    offset: 0,
    sort_by: 'published_date',
    sort_order: 'desc',
  },
  pagination: {
    current_page: 1,
    total_pages: 0,
    total_items: 0,
    items_per_page: 20,
  },
  selected_cves: [],
};

const cveSlice = createSlice({
  name: 'cve',
  initialState,
  reducers: {
    // CVE list management
    setCVEs: (state, action: PayloadAction<CVE[]>) => {
      state.cves = action.payload;
    },
    addCVE: (state, action: PayloadAction<CVE>) => {
      state.cves.unshift(action.payload);
    },
    updateCVE: (state, action: PayloadAction<CVE>) => {
      const index = state.cves.findIndex(cve => cve.id === action.payload.id);
      if (index !== -1) {
        state.cves[index] = action.payload;
      }
    },
    removeCVE: (state, action: PayloadAction<string>) => {
      state.cves = state.cves.filter(cve => cve.id !== action.payload);
    },
    
    // Loading and error management
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    
    // Filter management
    setFilters: (state, action: PayloadAction<Partial<CVEFeedRequest>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    resetFilters: (state) => {
      state.filters = {
        limit: 20,
        offset: 0,
        sort_by: 'published_date',
        sort_order: 'desc',
      };
    },
    setSeverityFilter: (state, action: PayloadAction<string[]>) => {
      state.filters.severity = action.payload as any;
    },
    setApplicationFilter: (state, action: PayloadAction<string | undefined>) => {
      state.filters.application_id = action.payload;
    },
    setSearchFilter: (state, action: PayloadAction<string | undefined>) => {
      state.filters.search = action.payload;
    },
    
    // Pagination management
    setPagination: (state, action: PayloadAction<Partial<typeof initialState.pagination>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.pagination.current_page = action.payload;
      state.filters.offset = (action.payload - 1) * state.pagination.items_per_page;
    },
    setItemsPerPage: (state, action: PayloadAction<number>) => {
      state.pagination.items_per_page = action.payload;
      state.filters.limit = action.payload;
    },
    
    // Selection management
    selectCVE: (state, action: PayloadAction<string>) => {
      if (!state.selected_cves.includes(action.payload)) {
        state.selected_cves.push(action.payload);
      }
    },
    deselectCVE: (state, action: PayloadAction<string>) => {
      state.selected_cves = state.selected_cves.filter(id => id !== action.payload);
    },
    selectAllCVEs: (state) => {
      state.selected_cves = state.cves.map(cve => cve.id);
    },
    deselectAllCVEs: (state) => {
      state.selected_cves = [];
    },
    toggleCVESelection: (state, action: PayloadAction<string>) => {
      const index = state.selected_cves.indexOf(action.payload);
      if (index === -1) {
        state.selected_cves.push(action.payload);
      } else {
        state.selected_cves.splice(index, 1);
      }
    },
  },
});

export const {
  setCVEs,
  addCVE,
  updateCVE,
  removeCVE,
  setLoading,
  setError,
  clearError,
  setFilters,
  resetFilters,
  setSeverityFilter,
  setApplicationFilter,
  setSearchFilter,
  setPagination,
  setCurrentPage,
  setItemsPerPage,
  selectCVE,
  deselectCVE,
  selectAllCVEs,
  deselectAllCVEs,
  toggleCVESelection,
} = cveSlice.actions;

// Selectors
export const selectCVEs = (state: { cve: CVEListState }) => state.cve.cves;
export const selectCVELoading = (state: { cve: CVEListState }) => state.cve.loading;
export const selectCVEError = (state: { cve: CVEListState }) => state.cve.error;
export const selectCVEFilters = (state: { cve: CVEListState }) => state.cve.filters;
export const selectCVEPagination = (state: { cve: CVEListState }) => state.cve.pagination;
export const selectSelectedCVEs = (state: { cve: CVEListState }) => state.cve.selected_cves;
export const selectCVEById = (state: { cve: CVEListState }, id: string) => 
  state.cve.cves.find(cve => cve.id === id);

export default cveSlice.reducer;
