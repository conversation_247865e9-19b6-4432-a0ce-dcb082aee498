import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { config } from './config/environment'

async function enableMocking() {
  if (config.enableMockApi) {
    const { startMocking } = await import('./mocks/browser')
    return startMocking()
  }
}

enableMocking().then(() => {
  createRoot(document.getElementById('root')!).render(
    <StrictMode>
      <App />
    </StrictMode>,
  )
})
