/**
 * Dashboard Layout component - Main layout for authenticated pages
 */

import React from 'react';
import { Disclosure } from '@headlessui/react';
import {
  Bars3Icon,
  XMarkIcon,
  HomeIcon,
  ShieldExclamationIcon,
  CubeIcon,
  UsersIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/react/24/outline';
import { Link, useLocation } from 'react-router-dom';
import { clsx } from 'clsx';
import { useAuth } from '../../../hooks/useAuth';
import { useNotifications } from '../../../hooks/useNotifications';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  current?: boolean;
}

const navigation: NavigationItem[] = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'CVE Feed', href: '/cves', icon: ShieldExclamationIcon },
  { name: 'Applications', href: '/applications', icon: CubeIcon },
  { name: 'Components', href: '/components', icon: Cog6ToothIcon },
  { name: 'Users', href: '/users', icon: UsersIcon },
];

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const location = useLocation();
  const { user, logout } = useAuth();
  const { showSuccess } = useNotifications();

  const handleLogout = async () => {
    try {
      await logout();
      showSuccess('Logged out successfully', 'You have been securely logged out.');
    } catch (error) {
      // Logout should always succeed locally even if API fails
      showSuccess('Logged out', 'You have been logged out.');
    }
  };

  // Update navigation items with current state
  const navigationWithCurrent = navigation.map(item => ({
    ...item,
    current: location.pathname === item.href,
  }));

  return (
    <div className="min-h-screen bg-gray-900">
      <Disclosure as="nav" className="bg-gray-800 border-b border-gray-700">
        {({ open }) => (
          <>
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              <div className="flex h-16 items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="flex items-center">
                      <ShieldExclamationIcon className="h-8 w-8 text-primary-500" />
                      <span className="ml-2 text-xl font-bold text-gray-100">
                        CVE Feed Service
                      </span>
                    </div>
                  </div>
                  <div className="hidden md:block">
                    <div className="ml-10 flex items-baseline space-x-4">
                      {navigationWithCurrent.map((item) => (
                        <Link
                          key={item.name}
                          to={item.href}
                          className={clsx(
                            item.current
                              ? 'bg-gray-900 text-white'
                              : 'text-gray-300 hover:bg-gray-700 hover:text-white',
                            'rounded-md px-3 py-2 text-sm font-medium flex items-center'
                          )}
                          aria-current={item.current ? 'page' : undefined}
                          data-testid={`nav-${item.name.toLowerCase().replace(' ', '-')}`}
                        >
                          <item.icon className="h-4 w-4 mr-2" />
                          {item.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="hidden md:block">
                  <div className="ml-4 flex items-center md:ml-6">
                    {/* User menu */}
                    <div className="relative ml-3">
                      <div className="flex items-center space-x-4">
                        <span className="text-sm text-gray-300">
                          Welcome, {user?.first_name || 'User'}
                        </span>
                        <button
                          onClick={handleLogout}
                          className="flex items-center text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium"
                          data-testid="logout-button"
                        >
                          <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
                          Logout
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="-mr-2 flex md:hidden">
                  {/* Mobile menu button */}
                  <Disclosure.Button className="inline-flex items-center justify-center rounded-md bg-gray-800 p-2 text-gray-400 hover:bg-gray-700 hover:text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800">
                    <span className="sr-only">Open main menu</span>
                    {open ? (
                      <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                    ) : (
                      <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                    )}
                  </Disclosure.Button>
                </div>
              </div>
            </div>

            <Disclosure.Panel className="md:hidden">
              <div className="space-y-1 px-2 pb-3 pt-2 sm:px-3">
                {navigationWithCurrent.map((item) => (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={clsx(
                      item.current
                        ? 'bg-gray-900 text-white'
                        : 'text-gray-300 hover:bg-gray-700 hover:text-white',
                      'block rounded-md px-3 py-2 text-base font-medium flex items-center'
                    )}
                    aria-current={item.current ? 'page' : undefined}
                  >
                    <item.icon className="h-5 w-5 mr-3" />
                    {item.name}
                  </Link>
                ))}
              </div>
              <div className="border-t border-gray-700 pb-3 pt-4">
                <div className="flex items-center px-5">
                  <div className="text-base font-medium leading-none text-white">
                    {user?.first_name} {user?.last_name}
                  </div>
                  <div className="text-sm font-medium leading-none text-gray-400 ml-auto">
                    {user?.email}
                  </div>
                </div>
                <div className="mt-3 space-y-1 px-2">
                  <button
                    onClick={handleLogout}
                    className="block rounded-md px-3 py-2 text-base font-medium text-gray-400 hover:bg-gray-700 hover:text-white w-full text-left"
                  >
                    Logout
                  </button>
                </div>
              </div>
            </Disclosure.Panel>
          </>
        )}
      </Disclosure>

      <main className="flex-1">
        {children}
      </main>
    </div>
  );
};
