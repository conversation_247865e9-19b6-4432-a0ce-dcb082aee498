/**
 * NotificationSystem component - Global notification management
 */

import React, { useEffect } from 'react';
import { Transition } from '@headlessui/react';
import {
  CheckCircleIcon,
  ExclamationCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { useAppSelector, useAppDispatch } from '../../../store/hooks';
import { selectNotifications, removeNotification } from '../../../store/slices/uiSlice';
import type { Notification } from '../../../store/slices/uiSlice';

const NotificationIcon: React.FC<{ type: Notification['type'] }> = ({ type }) => {
  const iconClass = "h-6 w-6";
  
  switch (type) {
    case 'success':
      return <CheckCircleIcon className={`${iconClass} text-green-400`} />;
    case 'error':
      return <ExclamationCircleIcon className={`${iconClass} text-red-400`} />;
    case 'warning':
      return <ExclamationTriangleIcon className={`${iconClass} text-yellow-400`} />;
    case 'info':
    default:
      return <InformationCircleIcon className={`${iconClass} text-blue-400`} />;
  }
};

const NotificationItem: React.FC<{ notification: Notification }> = ({ notification }) => {
  const dispatch = useAppDispatch();

  const handleDismiss = () => {
    dispatch(removeNotification(notification.id));
  };

  // Auto-dismiss after duration
  useEffect(() => {
    if (notification.duration && notification.duration > 0) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, notification.duration);

      return () => clearTimeout(timer);
    }
  }, [notification.duration, notification.id]);

  const bgColorClass = {
    success: 'bg-green-900/50 border-green-700',
    error: 'bg-red-900/50 border-red-700',
    warning: 'bg-yellow-900/50 border-yellow-700',
    info: 'bg-blue-900/50 border-blue-700',
  }[notification.type];

  return (
    <Transition
      appear
      show={true}
      enter="transform ease-out duration-300 transition"
      enterFrom="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
      enterTo="translate-y-0 opacity-100 sm:translate-x-0"
      leave="transition ease-in duration-100"
      leaveFrom="opacity-100"
      leaveTo="opacity-0"
    >
      <div className={`max-w-sm w-full shadow-lg rounded-lg pointer-events-auto border ${bgColorClass}`}>
        <div className="p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <NotificationIcon type={notification.type} />
            </div>
            <div className="ml-3 w-0 flex-1 pt-0.5">
              <p className="text-sm font-medium text-gray-100">
                {notification.title}
              </p>
              {notification.message && (
                <p className="mt-1 text-sm text-gray-300">
                  {notification.message}
                </p>
              )}
              {notification.actions && notification.actions.length > 0 && (
                <div className="mt-3 flex space-x-2">
                  {notification.actions.map((action, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        action.onClick();
                        handleDismiss();
                      }}
                      className="text-sm font-medium text-primary-400 hover:text-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded"
                    >
                      {action.label}
                    </button>
                  ))}
                </div>
              )}
            </div>
            <div className="ml-4 flex-shrink-0 flex">
              <button
                onClick={handleDismiss}
                className="rounded-md inline-flex text-gray-400 hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                aria-label="Dismiss notification"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  );
};

export const NotificationSystem: React.FC = () => {
  const notifications = useAppSelector(selectNotifications);

  return (
    <div
      aria-live="assertive"
      className="fixed inset-0 flex items-end justify-center px-4 py-6 pointer-events-none sm:p-6 sm:items-start sm:justify-end z-50"
    >
      <div className="w-full flex flex-col items-center space-y-4 sm:items-end">
        {notifications.map((notification) => (
          <NotificationItem key={notification.id} notification={notification} />
        ))}
      </div>
    </div>
  );
};
