/**
 * LoginForm component - Handles user authentication
 */

import React from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Link } from 'react-router-dom';
import { EnvelopeIcon, LockClosedIcon } from '@heroicons/react/24/outline';
import { Button } from '../../atoms/Button';
import { Input } from '../../atoms/Input';
import type { LoginRequest } from '../../../types/auth';

const loginSchema = yup.object({
  username: yup
    .string()
    .email('Please enter a valid email address')
    .required('Email is required'),
  password: yup
    .string()
    .min(8, 'Password must be at least 8 characters')
    .required('Password is required'),
  remember_me: yup.boolean().default(false),
});

type LoginFormData = yup.InferType<typeof loginSchema>;

export interface LoginFormProps {
  onSubmit: (data: LoginRequest) => void;
  loading?: boolean;
  error?: string | null;
  className?: string;
}

export const LoginForm: React.FC<LoginFormProps> = ({
  onSubmit,
  loading = false,
  error,
  className,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    clearErrors,
  } = useForm<LoginFormData>({
    resolver: yupResolver(loginSchema),
    mode: 'onChange',
  });

  const handleFormSubmit = (data: LoginFormData) => {
    onSubmit(data);
  };

  // Clear errors when user starts typing
  const handleInputChange = () => {
    if (error) {
      clearErrors();
    }
  };

  return (
    <div className={className}>
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-100">
          Sign in to CVE Feed Service
        </h1>
        <p className="mt-2 text-gray-400">
          Access your vulnerability management dashboard
        </p>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {error && (
          <div
            className="bg-red-900/50 border border-red-700 text-red-200 px-4 py-3 rounded-lg"
            role="alert"
            data-testid="error-message"
          >
            <p className="text-sm">{error}</p>
          </div>
        )}

        <Input
          {...register('username')}
          type="email"
          label="Email address"
          placeholder="Enter your email"
          leftIcon={<EnvelopeIcon />}
          error={errors.username?.message}
          onChange={(e) => {
            register('username').onChange(e);
            handleInputChange();
          }}
          data-testid="email-input"
          autoComplete="email"
          required
        />

        <Input
          {...register('password')}
          type="password"
          label="Password"
          placeholder="Enter your password"
          leftIcon={<LockClosedIcon />}
          error={errors.password?.message}
          onChange={(e) => {
            register('password').onChange(e);
            handleInputChange();
          }}
          data-testid="password-input"
          autoComplete="current-password"
          required
        />

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input
              {...register('remember_me')}
              id="remember-me"
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-600 bg-gray-800 rounded"
              data-testid="remember-me-toggle"
            />
            <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-300">
              Remember me
            </label>
          </div>

          <Link
            to="/forgot-password"
            className="text-sm text-primary-400 hover:text-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded"
            data-testid="forgot-password-link"
          >
            Forgot your password?
          </Link>
        </div>

        <Button
          type="submit"
          variant="primary"
          size="lg"
          fullWidth
          loading={loading}
          disabled={!isValid || loading}
          data-testid="login-button"
        >
          {loading ? 'Signing in...' : 'Sign in'}
        </Button>

        <div className="text-center">
          <p className="text-sm text-gray-400">
            Don't have an account?{' '}
            <Link
              to="/register"
              className="text-primary-400 hover:text-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded"
            >
              Sign up here
            </Link>
          </p>
        </div>
      </form>
    </div>
  );
};
