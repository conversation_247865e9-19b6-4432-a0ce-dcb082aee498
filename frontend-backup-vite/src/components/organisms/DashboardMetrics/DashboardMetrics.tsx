/**
 * Dashboard Metrics component - Displays key vulnerability metrics
 */

import React from 'react';
import { 
  ShieldExclamationIcon, 
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  CubeIcon,
  ServerIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
} from '@heroicons/react/24/outline';
import { useGetMetricsQuery } from '../../../store/api/dashboardApi';
import { clsx } from 'clsx';

interface MetricCardProps {
  title: string;
  value: number;
  icon: React.ComponentType<{ className?: string }>;
  trend?: {
    value: number;
    direction: 'up' | 'down';
    period: string;
  };
  severity?: 'critical' | 'high' | 'medium' | 'low' | 'info';
  loading?: boolean;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon: Icon,
  trend,
  severity = 'info',
  loading = false,
}) => {
  const severityClasses = {
    critical: 'border-red-500 bg-red-900/20',
    high: 'border-orange-500 bg-orange-900/20',
    medium: 'border-yellow-500 bg-yellow-900/20',
    low: 'border-green-500 bg-green-900/20',
    info: 'border-blue-500 bg-blue-900/20',
  };

  const iconClasses = {
    critical: 'text-red-400',
    high: 'text-orange-400',
    medium: 'text-yellow-400',
    low: 'text-green-400',
    info: 'text-blue-400',
  };

  if (loading) {
    return (
      <div className="card p-6 animate-pulse">
        <div className="flex items-center justify-between mb-4">
          <div className="h-4 bg-gray-700 rounded w-24"></div>
          <div className="h-8 w-8 bg-gray-700 rounded"></div>
        </div>
        <div className="h-8 bg-gray-700 rounded w-16 mb-2"></div>
        <div className="h-3 bg-gray-700 rounded w-20"></div>
      </div>
    );
  }

  return (
    <div className={clsx('card p-6 border-l-4', severityClasses[severity])}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-300">{title}</h3>
        <Icon className={clsx('h-8 w-8', iconClasses[severity])} />
      </div>
      
      <div className="flex items-baseline justify-between">
        <p className="text-3xl font-bold text-gray-100">
          {value.toLocaleString()}
        </p>
        
        {trend && (
          <div className="flex items-center space-x-1">
            {trend.direction === 'up' ? (
              <ArrowTrendingUpIcon className="h-4 w-4 text-red-400" />
            ) : (
              <ArrowTrendingDownIcon className="h-4 w-4 text-green-400" />
            )}
            <span className={clsx(
              'text-sm font-medium',
              trend.direction === 'up' ? 'text-red-400' : 'text-green-400'
            )}>
              {trend.value}
            </span>
            <span className="text-xs text-gray-400">{trend.period}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export const DashboardMetrics: React.FC = () => {
  const { data: metrics, isLoading, error, refetch } = useGetMetricsQuery();

  if (error) {
    return (
      <div className="card p-6 border-red-500 bg-red-900/20">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-red-400">Failed to load metrics</h3>
            <p className="text-sm text-gray-400 mt-1">
              Unable to fetch dashboard metrics. Please try again.
            </p>
          </div>
          <button
            onClick={() => refetch()}
            className="btn-secondary px-4 py-2 text-sm"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-100">Security Overview</h2>
        <div className="text-sm text-gray-400">
          Last updated: {metrics?.last_updated ? new Date(metrics.last_updated).toLocaleString() : 'Loading...'}
        </div>
      </div>

      {/* Main metrics grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Critical Vulnerabilities"
          value={metrics?.critical_cves || 0}
          icon={ShieldExclamationIcon}
          severity="critical"
          trend={
            metrics?.trend_data?.cves_last_7_days ? {
              value: metrics.trend_data.cves_last_7_days,
              direction: 'up',
              period: 'this week',
            } : undefined
          }
          loading={isLoading}
        />
        
        <MetricCard
          title="High Priority"
          value={metrics?.high_cves || 0}
          icon={ExclamationTriangleIcon}
          severity="high"
          loading={isLoading}
        />
        
        <MetricCard
          title="Medium Priority"
          value={metrics?.medium_cves || 0}
          icon={InformationCircleIcon}
          severity="medium"
          loading={isLoading}
        />
        
        <MetricCard
          title="Low Priority"
          value={metrics?.low_cves || 0}
          icon={CheckCircleIcon}
          severity="low"
          loading={isLoading}
        />
      </div>

      {/* Secondary metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <MetricCard
          title="Total Vulnerabilities"
          value={metrics?.total_cves || 0}
          icon={ShieldExclamationIcon}
          severity="info"
          trend={
            metrics?.trend_data?.cves_last_30_days ? {
              value: metrics.trend_data.cves_last_30_days,
              direction: 'up',
              period: 'last 30 days',
            } : undefined
          }
          loading={isLoading}
        />
        
        <MetricCard
          title="Applications Monitored"
          value={metrics?.applications_count || 0}
          icon={CubeIcon}
          severity="info"
          trend={
            metrics?.trend_data?.new_applications_last_30_days ? {
              value: metrics.trend_data.new_applications_last_30_days,
              direction: 'up',
              period: 'new this month',
            } : undefined
          }
          loading={isLoading}
        />
        
        <MetricCard
          title="Components Tracked"
          value={metrics?.components_count || 0}
          icon={ServerIcon}
          severity="info"
          loading={isLoading}
        />
      </div>
    </div>
  );
};
