/**
 * Custom render function for testing with providers
 */

import React from 'react';
import { render, type RenderOptions } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import authSlice from '../store/slices/authSlice';
import uiSlice from '../store/slices/uiSlice';
import cveSlice from '../store/slices/cveSlice';
import { authApi } from '../store/api/authApi';
import { dashboardApi } from '../store/api/dashboardApi';
import { cveApi } from '../store/api/cveApi';
import { applicationApi } from '../store/api/applicationApi';
import type { RootState } from '../store';

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  preloadedState?: Partial<RootState>;
  route?: string;
}

export function createMockStore(preloadedState?: Partial<RootState>) {
  return configureStore({
    reducer: {
      auth: authSlice,
      ui: uiSlice,
      cve: cveSlice,
      [authApi.reducerPath]: authApi.reducer,
      [dashboardApi.reducerPath]: dashboardApi.reducer,
      [cveApi.reducerPath]: cveApi.reducer,
      [applicationApi.reducerPath]: applicationApi.reducer,
    },
    preloadedState,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }).concat(
        authApi.middleware,
        dashboardApi.middleware,
        cveApi.middleware,
        applicationApi.middleware
      ),
  });
}

export function renderWithProviders(
  ui: React.ReactElement,
  {
    preloadedState,
    route = '/',
    ...renderOptions
  }: CustomRenderOptions = {}
) {
  const store = createMockStore(preloadedState);

  // Set initial route
  window.history.pushState({}, 'Test page', route);

  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <Provider store={store}>
        <BrowserRouter>
          {children}
        </BrowserRouter>
      </Provider>
    );
  }

  return {
    store,
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
  };
}

// Re-export everything from React Testing Library
export * from '@testing-library/react';

// Override render method
export { renderWithProviders as render };
