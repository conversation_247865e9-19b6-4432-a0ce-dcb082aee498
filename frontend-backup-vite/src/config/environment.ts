/**
 * Environment configuration for the CVE Feed Service frontend
 */

export const config = {
  // API Configuration
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1',
  wsUrl: import.meta.env.VITE_WS_URL || 'ws://localhost:8000/ws',
  
  // Application Configuration
  appName: 'CVE Feed Service',
  version: import.meta.env.VITE_APP_VERSION || '1.0.0',
  environment: import.meta.env.VITE_ENVIRONMENT || 'development',
  
  // Development Configuration
  enableDevTools: import.meta.env.DEV,
  enableMockApi: import.meta.env.VITE_ENABLE_MOCK_API === 'true',
  
  // External Services
  sentryDsn: import.meta.env.VITE_SENTRY_DSN,
  
  // Feature Flags
  features: {
    realTimeUpdates: import.meta.env.VITE_FEATURE_REAL_TIME_UPDATES !== 'false',
    darkModeOnly: import.meta.env.VITE_FEATURE_DARK_MODE_ONLY === 'true',
    exportFunctionality: import.meta.env.VITE_FEATURE_EXPORT !== 'false',
    advancedFiltering: import.meta.env.VITE_FEATURE_ADVANCED_FILTERING !== 'false',
  },
  
  // UI Configuration
  ui: {
    defaultPageSize: 20,
    maxPageSize: 100,
    debounceMs: 300,
    toastDuration: 5000,
    animationDuration: 300,
  },
  
  // Security Configuration
  security: {
    tokenRefreshThreshold: 5 * 60 * 1000, // 5 minutes in milliseconds
    maxLoginAttempts: 5,
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  },
  
  // Performance Configuration
  performance: {
    virtualScrollThreshold: 100,
    imageOptimization: true,
    lazyLoadingEnabled: true,
  },
} as const;

// Type-safe environment validation
export const validateEnvironment = () => {
  const requiredEnvVars = [
    'VITE_API_BASE_URL',
  ];
  
  const missingVars = requiredEnvVars.filter(
    varName => !import.meta.env[varName] && !config.apiBaseUrl
  );
  
  if (missingVars.length > 0) {
    console.warn('Missing environment variables:', missingVars);
  }
  
  return {
    isValid: missingVars.length === 0,
    missingVars,
  };
};

// Export environment info for debugging
export const getEnvironmentInfo = () => ({
  environment: config.environment,
  version: config.version,
  apiBaseUrl: config.apiBaseUrl,
  features: config.features,
  timestamp: new Date().toISOString(),
});
