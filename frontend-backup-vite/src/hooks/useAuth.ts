/**
 * Authentication hook for managing user authentication state and actions
 */

import { useCallback, useEffect } from 'react';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { useLoginMutation, useLogoutMutation, useGetCurrentUserQuery } from '../store/api/authApi';
import {
  loginStart,
  loginSuccess,
  loginFailure,
  logout as logoutAction,
  updateLastActivity,
  checkSession,
} from '../store/slices/authSlice';
import {
  selectAuth,
  selectIsAuthenticated,
  selectUser,
  selectAuthLoading,
  selectAuthError,
  selectIsSessionExpired,
} from '../store/slices/authSlice';
import type { LoginRequest } from '../types/auth';

export const useAuth = () => {
  const dispatch = useAppDispatch();
  const auth = useAppSelector(selectAuth);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const user = useAppSelector(selectUser);
  const loading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);
  const isSessionExpired = useAppSelector(selectIsSessionExpired);

  const [loginMutation, { isLoading: loginLoading }] = useLoginMutation();
  const [logoutMutation] = useLogoutMutation();

  // Get current user data if authenticated
  const { data: currentUser, refetch: refetchUser } = useGetCurrentUserQuery(undefined, {
    skip: !isAuthenticated,
  });

  // Login function
  const login = useCallback(async (credentials: LoginRequest) => {
    try {
      dispatch(loginStart());
      const result = await loginMutation(credentials).unwrap();
      
      dispatch(loginSuccess({
        user: result.user,
        token: result.access_token,
        refreshToken: result.refresh_token,
      }));

      // Store token in localStorage for persistence
      if (credentials.remember_me) {
        localStorage.setItem('auth_token', result.access_token);
        if (result.refresh_token) {
          localStorage.setItem('refresh_token', result.refresh_token);
        }
      }

      return result;
    } catch (error: any) {
      const errorMessage = error.data?.detail || 'Login failed. Please check your credentials.';
      dispatch(loginFailure({
        code: error.status || 'LOGIN_ERROR',
        message: errorMessage,
      }));
      throw error;
    }
  }, [dispatch, loginMutation]);

  // Logout function
  const logout = useCallback(async () => {
    try {
      // Call logout API to invalidate server-side session
      await logoutMutation().unwrap();
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      // Clear local storage
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      
      // Clear Redux state
      dispatch(logoutAction());
    }
  }, [dispatch, logoutMutation]);

  // Update activity timestamp
  const updateActivity = useCallback(() => {
    dispatch(updateLastActivity());
  }, [dispatch]);

  // Check if session is valid
  const checkSessionValidity = useCallback(() => {
    dispatch(checkSession());
  }, [dispatch]);

  // Initialize authentication from localStorage
  const initializeAuth = useCallback(() => {
    const token = localStorage.getItem('auth_token');
    const refreshToken = localStorage.getItem('refresh_token');
    
    if (token && !isAuthenticated) {
      // Validate token by fetching current user
      refetchUser();
    }
  }, [isAuthenticated, refetchUser]);

  // Auto-logout on session expiry
  useEffect(() => {
    if (isSessionExpired && isAuthenticated) {
      logout();
    }
  }, [isSessionExpired, isAuthenticated, logout]);

  // Initialize auth on mount
  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  // Set up activity tracking
  useEffect(() => {
    if (!isAuthenticated) return;

    const handleActivity = () => {
      updateActivity();
    };

    // Track user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // Check session validity periodically
    const sessionCheckInterval = setInterval(checkSessionValidity, 60000); // Every minute

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
      clearInterval(sessionCheckInterval);
    };
  }, [isAuthenticated, updateActivity, checkSessionValidity]);

  return {
    // State
    isAuthenticated,
    user: user || currentUser,
    loading: loading || loginLoading,
    error,
    isSessionExpired,
    
    // Actions
    login,
    logout,
    updateActivity,
    checkSessionValidity,
    refetchUser,
    
    // Utilities
    hasRole: (role: string) => user?.role === role,
    hasPermission: (permission: string) => {
      // This would be implemented based on your permission system
      return true; // Placeholder
    },
  };
};
