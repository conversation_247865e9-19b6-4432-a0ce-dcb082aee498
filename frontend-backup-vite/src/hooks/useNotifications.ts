/**
 * Notifications hook for managing toast notifications
 */

import { useCallback } from 'react';
import { useAppDispatch } from '../store/hooks';
import { addNotification, removeNotification } from '../store/slices/uiSlice';
import type { Notification } from '../store/slices/uiSlice';

export const useNotifications = () => {
  const dispatch = useAppDispatch();

  const showNotification = useCallback((notification: Omit<Notification, 'id'>) => {
    dispatch(addNotification({
      duration: 5000, // Default 5 seconds
      ...notification,
    }));
  }, [dispatch]);

  const showSuccess = useCallback((title: string, message?: string) => {
    showNotification({
      type: 'success',
      title,
      message,
    });
  }, [showNotification]);

  const showError = useCallback((title: string, message?: string) => {
    showNotification({
      type: 'error',
      title,
      message,
      duration: 8000, // Errors stay longer
    });
  }, [showNotification]);

  const showWarning = useCallback((title: string, message?: string) => {
    showNotification({
      type: 'warning',
      title,
      message,
    });
  }, [showNotification]);

  const showInfo = useCallback((title: string, message?: string) => {
    showNotification({
      type: 'info',
      title,
      message,
    });
  }, [showNotification]);

  const dismissNotification = useCallback((id: string) => {
    dispatch(removeNotification(id));
  }, [dispatch]);

  return {
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    dismissNotification,
  };
};
