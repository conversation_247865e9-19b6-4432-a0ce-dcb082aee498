@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-900 text-gray-50 antialiased;
  }

  /* Dark mode by default */
  .dark {
    color-scheme: dark;
  }
}

@layer components {
  /* Custom component styles */
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900 disabled:bg-primary-300 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply bg-gray-700 text-gray-100 hover:bg-gray-600 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-gray-900 disabled:bg-gray-600 disabled:cursor-not-allowed;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-gray-900 disabled:bg-red-300 disabled:cursor-not-allowed;
  }

  .input-field {
    @apply block w-full rounded-md border-0 bg-gray-800 py-1.5 text-gray-100 shadow-sm ring-1 ring-inset ring-gray-600 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6;
  }

  .card {
    @apply bg-gray-800 rounded-lg shadow-lg border border-gray-700;
  }

  .severity-critical {
    @apply bg-red-600 text-white;
  }

  .severity-high {
    @apply bg-orange-600 text-white;
  }

  .severity-medium {
    @apply bg-yellow-600 text-white;
  }

  .severity-low {
    @apply bg-green-600 text-white;
  }
}

@layer utilities {
  /* Custom utility classes */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
