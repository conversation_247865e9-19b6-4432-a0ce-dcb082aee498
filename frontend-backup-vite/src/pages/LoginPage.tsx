/**
 * Login page component
 */

import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { LoginForm } from '../components/molecules/LoginForm';
import { useAuth } from '../hooks/useAuth';
import { useNotifications } from '../hooks/useNotifications';
import type { LoginRequest } from '../types/auth';

export const LoginPage: React.FC = () => {
  const location = useLocation();
  const { isAuthenticated, login, loading, error } = useAuth();
  const { showSuccess, showError } = useNotifications();

  // Redirect to intended page after login
  const from = (location.state as any)?.from?.pathname || '/dashboard';

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to={from} replace />;
  }

  const handleLogin = async (loginData: LoginRequest) => {
    try {
      await login(loginData);
      showSuccess('Welcome back!', 'You have been successfully logged in.');
    } catch (error: any) {
      showError(
        'Login Failed',
        error.data?.detail || 'Please check your credentials and try again.'
      );
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-gray-800 py-8 px-4 shadow-xl sm:rounded-lg sm:px-10 border border-gray-700">
          <LoginForm
            onSubmit={handleLogin}
            loading={loading}
            error={error}
          />
        </div>
      </div>

      {/* Footer */}
      <div className="mt-8 text-center">
        <p className="text-sm text-gray-500">
          CVE Feed Service v1.0.0 - Secure Vulnerability Management
        </p>
      </div>
    </div>
  );
};
