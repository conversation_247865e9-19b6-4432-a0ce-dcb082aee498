/**
 * Dashboard page component
 */

import React from 'react';
import { DashboardLayout } from '../components/layout/DashboardLayout';
import { DashboardMetrics } from '../components/organisms/DashboardMetrics';
import { useAuth } from '../hooks/useAuth';

export const DashboardPage: React.FC = () => {
  const { user } = useAuth();

  return (
    <DashboardLayout>
      <div className="p-8">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-100">
              Welcome back, {user?.first_name || 'User'}!
            </h1>
            <p className="mt-2 text-gray-400">
              Here's your vulnerability management overview
            </p>
          </div>

        {/* Placeholder content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-200 mb-2">Total CVEs</h3>
            <p className="text-3xl font-bold text-primary-400">1,234</p>
            <p className="text-sm text-gray-400 mt-1">+12 this week</p>
          </div>
          
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-200 mb-2">Critical</h3>
            <p className="text-3xl font-bold text-red-400">23</p>
            <p className="text-sm text-gray-400 mt-1">Requires attention</p>
          </div>
          
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-200 mb-2">High Priority</h3>
            <p className="text-3xl font-bold text-orange-400">156</p>
            <p className="text-sm text-gray-400 mt-1">Review needed</p>
          </div>
          
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-200 mb-2">Applications</h3>
            <p className="text-3xl font-bold text-green-400">42</p>
            <p className="text-sm text-gray-400 mt-1">Being monitored</p>
          </div>
        </div>

        <div className="card p-6">
          <h2 className="text-xl font-semibold text-gray-200 mb-4">Recent Activity</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between py-3 border-b border-gray-700">
              <div>
                <p className="text-gray-200">New critical CVE detected</p>
                <p className="text-sm text-gray-400">CVE-2023-1234 affects nginx 1.20.1</p>
              </div>
              <span className="text-sm text-gray-400">2 hours ago</span>
            </div>
            
            <div className="flex items-center justify-between py-3 border-b border-gray-700">
              <div>
                <p className="text-gray-200">Application scan completed</p>
                <p className="text-sm text-gray-400">WebApp-Production - 5 new vulnerabilities</p>
              </div>
              <span className="text-sm text-gray-400">4 hours ago</span>
            </div>
            
            <div className="flex items-center justify-between py-3">
              <div>
                <p className="text-gray-200">CVE status updated</p>
                <p className="text-sm text-gray-400">CVE-2023-5678 marked as resolved</p>
              </div>
              <span className="text-sm text-gray-400">1 day ago</span>
            </div>
          </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};
