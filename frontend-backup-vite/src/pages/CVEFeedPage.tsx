/**
 * CVE Feed page component
 */

import React from 'react';
import { DashboardLayout } from '../components/layout/DashboardLayout';

export const CVEFeedPage: React.FC = () => {
  return (
    <DashboardLayout>
      <div className="p-8">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-100">CVE Feed</h1>
            <p className="mt-2 text-gray-400">
              Monitor and manage vulnerabilities affecting your applications
            </p>
          </div>

          {/* Placeholder content */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold text-gray-200 mb-4">Recent Vulnerabilities</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between py-3 border-b border-gray-700">
                <div className="flex items-center space-x-4">
                  <span className="severity-critical px-2 py-1 rounded text-xs font-medium">
                    CRITICAL
                  </span>
                  <div>
                    <p className="text-gray-200 font-medium">CVE-2023-1234</p>
                    <p className="text-sm text-gray-400">Remote Code Execution in nginx</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-400">CVSS: 9.8</p>
                  <p className="text-xs text-gray-500">2 hours ago</p>
                </div>
              </div>
              
              <div className="flex items-center justify-between py-3 border-b border-gray-700">
                <div className="flex items-center space-x-4">
                  <span className="severity-high px-2 py-1 rounded text-xs font-medium">
                    HIGH
                  </span>
                  <div>
                    <p className="text-gray-200 font-medium">CVE-2023-5678</p>
                    <p className="text-sm text-gray-400">SQL Injection in web application</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-400">CVSS: 8.1</p>
                  <p className="text-xs text-gray-500">4 hours ago</p>
                </div>
              </div>
              
              <div className="flex items-center justify-between py-3">
                <div className="flex items-center space-x-4">
                  <span className="severity-medium px-2 py-1 rounded text-xs font-medium">
                    MEDIUM
                  </span>
                  <div>
                    <p className="text-gray-200 font-medium">CVE-2023-9012</p>
                    <p className="text-sm text-gray-400">Cross-site scripting vulnerability</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-400">CVSS: 6.1</p>
                  <p className="text-xs text-gray-500">1 day ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};
