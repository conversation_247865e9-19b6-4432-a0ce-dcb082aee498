/**
 * Authentication-related type definitions
 */

export interface User {
  id: string;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role: UserRole;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  last_login?: string;
  avatar_url?: string;
}

export type UserRole = 
  | 'admin'
  | 'security_analyst'
  | 'application_owner'
  | 'read_only';

export interface LoginRequest {
  username: string;
  password: string;
  remember_me?: boolean;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: User;
  refresh_token?: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  confirm_password: string;
  first_name: string;
  last_name: string;
  organization?: string;
  terms_accepted: boolean;
}

export interface RegisterResponse {
  message: string;
  user_id: string;
  verification_required: boolean;
}

export interface TokenRefreshRequest {
  refresh_token: string;
}

export interface TokenRefreshResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ForgotPasswordResponse {
  message: string;
}

export interface ResetPasswordRequest {
  token: string;
  new_password: string;
  confirm_password: string;
}

export interface ResetPasswordResponse {
  message: string;
}

export interface VerifyEmailRequest {
  token: string;
}

export interface VerifyEmailResponse {
  message: string;
  user: User;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  lastActivity: number | null;
}

export interface AuthError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// Permission-related types
export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
}

export interface RolePermissions {
  role: UserRole;
  permissions: Permission[];
}

// Session management
export interface SessionInfo {
  user_id: string;
  session_id: string;
  created_at: string;
  last_activity: string;
  ip_address: string;
  user_agent: string;
  is_active: boolean;
}

// API Key management
export interface ApiKey {
  id: string;
  name: string;
  key_preview: string; // Only first/last few characters
  created_at: string;
  last_used?: string;
  expires_at?: string;
  is_active: boolean;
  permissions: string[];
}

export interface CreateApiKeyRequest {
  name: string;
  expires_at?: string;
  permissions: string[];
}

export interface CreateApiKeyResponse {
  api_key: ApiKey;
  key_value: string; // Full key value, only returned once
}
