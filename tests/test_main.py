"""Test main application functionality."""

import pytest
from fastapi.testclient import Test<PERSON>lient

from src.cve_feed_service.main import app

client = TestClient(app)


def test_health_endpoint():
    """Test health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "version" in data


def test_readiness_endpoint():
    """Test readiness check endpoint."""
    response = client.get("/readiness")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ready"
    assert "version" in data


def test_openapi_docs():
    """Test OpenAPI documentation is accessible."""
    response = client.get("/api/v1/docs")
    assert response.status_code == 200


def test_openapi_json():
    """Test OpenAPI JSON schema is accessible."""
    response = client.get("/api/v1/openapi.json")
    assert response.status_code == 200
    data = response.json()
    assert "openapi" in data
    assert "info" in data
    assert data["info"]["title"] == "CVE Feed Service"
