/**
 * Playwright E2E Tests for CVE Feed Functionality
 * Comprehensive testing of CVE feed user interface and interactions
 */

import { test, expect, type Page } from '@playwright/test';

test.describe('CVE Feed Interface', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=password-input]', 'SecurePass123!');
    await page.click('[data-testid=login-button]');
    await expect(page).toHaveURL('/dashboard');
    
    // Navigate to CVE feed
    await page.click('[data-testid=nav-cves]');
    await expect(page).toHaveURL('/cves');
  });

  test('displays CVE feed with proper layout', async ({ page }) => {
    // Wait for CVE list to load
    await expect(page.locator('[data-testid=cve-list]')).toBeVisible();
    
    // Check main layout elements
    await expect(page.locator('[data-testid=filter-sidebar]')).toBeVisible();
    await expect(page.locator('[data-testid=cve-cards-container]')).toBeVisible();
    await expect(page.locator('[data-testid=pagination-controls]')).toBeVisible();
    
    // Verify CVE cards are displayed
    const cveCards = page.locator('[data-testid=cve-card]');
    await expect(cveCards).toHaveCount(20); // Default page size
    
    // Check CVE card structure
    const firstCard = cveCards.first();
    await expect(firstCard.locator('[data-testid=cve-id]')).toBeVisible();
    await expect(firstCard.locator('[data-testid=cve-title]')).toBeVisible();
    await expect(firstCard.locator('[data-testid=severity-badge]')).toBeVisible();
    await expect(firstCard.locator('[data-testid=cvss-score]')).toBeVisible();
    await expect(firstCard.locator('[data-testid=published-date]')).toBeVisible();
  });

  test('filters CVEs by severity level', async ({ page }) => {
    // Wait for initial load
    await expect(page.locator('[data-testid=cve-list]')).toBeVisible();
    
    // Apply HIGH severity filter
    await page.selectOption('[data-testid=severity-filter]', 'HIGH');
    
    // Wait for filtered results
    await page.waitForResponse('/api/v1/cves/feed?severity=HIGH*');
    
    // Verify all displayed CVEs have HIGH severity
    const severityBadges = page.locator('[data-testid=severity-badge]');
    const badgeCount = await severityBadges.count();
    
    for (let i = 0; i < badgeCount; i++) {
      await expect(severityBadges.nth(i)).toContainText('HIGH');
    }
    
    // Verify filter is visually active
    await expect(page.locator('[data-testid=severity-filter]')).toHaveValue('HIGH');
    
    // Test CRITICAL filter
    await page.selectOption('[data-testid=severity-filter]', 'CRITICAL');
    await page.waitForResponse('/api/v1/cves/feed?severity=CRITICAL*');
    
    // Verify CRITICAL CVEs are displayed
    const criticalBadges = page.locator('[data-testid=severity-badge]');
    const criticalCount = await criticalBadges.count();
    
    for (let i = 0; i < criticalCount; i++) {
      await expect(criticalBadges.nth(i)).toContainText('CRITICAL');
    }
  });

  test('filters CVEs by application', async ({ page }) => {
    // Wait for initial load
    await expect(page.locator('[data-testid=cve-list]')).toBeVisible();
    
    // Select application filter
    await page.selectOption('[data-testid=application-filter]', 'WebApp-Production');
    
    // Wait for filtered results
    await page.waitForResponse('/api/v1/cves/feed?application_id=*');
    
    // Verify filter is applied
    await expect(page.locator('[data-testid=application-filter]')).toHaveValue('WebApp-Production');
    
    // Verify results are filtered (should be fewer than total)
    const filteredCards = page.locator('[data-testid=cve-card]');
    const filteredCount = await filteredCards.count();
    expect(filteredCount).toBeGreaterThan(0);
    expect(filteredCount).toBeLessThanOrEqual(20);
    
    // Check that filter indicator is shown
    await expect(page.locator('[data-testid=active-filters]')).toContainText('WebApp-Production');
  });

  test('navigates through CVE feed pages', async ({ page }) => {
    // Wait for initial load
    await expect(page.locator('[data-testid=cve-list]')).toBeVisible();
    
    // Check pagination controls
    await expect(page.locator('[data-testid=pagination-info]')).toBeVisible();
    await expect(page.locator('[data-testid=next-page-btn]')).toBeVisible();
    
    // Get first page CVE IDs
    const firstPageCVEs = await page.locator('[data-testid=cve-id]').allTextContents();
    
    // Navigate to next page
    await page.click('[data-testid=next-page-btn]');
    await page.waitForResponse('/api/v1/cves/feed?offset=20*');
    
    // Verify page changed
    const secondPageCVEs = await page.locator('[data-testid=cve-id]').allTextContents();
    expect(secondPageCVEs).not.toEqual(firstPageCVEs);
    
    // Verify pagination info updated
    await expect(page.locator('[data-testid=pagination-info]')).toContainText('Page 2');
    
    // Test previous page navigation
    await page.click('[data-testid=prev-page-btn]');
    await page.waitForResponse('/api/v1/cves/feed?offset=0*');
    
    // Verify back to first page
    const backToFirstPage = await page.locator('[data-testid=cve-id]').allTextContents();
    expect(backToFirstPage).toEqual(firstPageCVEs);
  });

  test('opens CVE details when clicking on CVE card', async ({ page }) => {
    // Wait for CVE list to load
    await expect(page.locator('[data-testid=cve-list]')).toBeVisible();
    
    // Get first CVE ID
    const firstCVEId = await page.locator('[data-testid=cve-id]').first().textContent();
    
    // Click on first CVE card
    await page.click('[data-testid=cve-card]:first-child');
    
    // Verify navigation to CVE details page
    await expect(page).toHaveURL(new RegExp(`/cves/${firstCVEId}`));
    
    // Verify CVE details page elements
    await expect(page.locator('[data-testid=cve-details-header]')).toBeVisible();
    await expect(page.locator('[data-testid=cve-description]')).toBeVisible();
    await expect(page.locator('[data-testid=cvss-metrics]')).toBeVisible();
    await expect(page.locator('[data-testid=affected-components]')).toBeVisible();
  });

  test('searches CVEs using global search', async ({ page }) => {
    // Use global search from CVE page
    await page.fill('[data-testid=global-search]', 'CVE-2023');
    await page.press('[data-testid=global-search]', 'Enter');
    
    // Wait for search results
    await page.waitForResponse('/api/v1/search?q=CVE-2023*');
    
    // Verify search results page
    await expect(page).toHaveURL('/search?q=CVE-2023');
    await expect(page.locator('[data-testid=search-results]')).toBeVisible();
    
    // Verify search results contain the query
    const searchResults = page.locator('[data-testid=search-result-item]');
    const resultCount = await searchResults.count();
    expect(resultCount).toBeGreaterThan(0);
    
    // Check that results contain the search term
    for (let i = 0; i < Math.min(resultCount, 5); i++) {
      await expect(searchResults.nth(i)).toContainText('CVE-2023');
    }
  });

  test('exports CVE data', async ({ page }) => {
    // Wait for CVE list to load
    await expect(page.locator('[data-testid=cve-list]')).toBeVisible();
    
    // Apply a filter first
    await page.selectOption('[data-testid=severity-filter]', 'HIGH');
    await page.waitForResponse('/api/v1/cves/feed?severity=HIGH*');
    
    // Start download
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid=export-button]');
    await page.click('[data-testid=export-csv]');
    
    // Verify download
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toMatch(/cves-export-.*\.csv/);
    
    // Verify export includes current filters
    await expect(page.locator('[data-testid=export-status]')).toContainText('Export completed');
  });

  test('shows loading states during data fetching', async ({ page }) => {
    // Intercept API call to add delay
    await page.route('/api/v1/cves/feed*', async route => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.continue();
    });
    
    // Navigate to CVE feed
    await page.goto('/cves');
    
    // Verify loading state
    await expect(page.locator('[data-testid=loading-skeleton]')).toBeVisible();
    await expect(page.locator('[data-testid=cve-list]')).not.toBeVisible();
    
    // Wait for loading to complete
    await expect(page.locator('[data-testid=loading-skeleton]')).not.toBeVisible();
    await expect(page.locator('[data-testid=cve-list]')).toBeVisible();
  });

  test('handles API errors gracefully', async ({ page }) => {
    // Mock API error
    await page.route('/api/v1/cves/feed*', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ detail: 'Internal server error' })
      });
    });
    
    // Navigate to CVE feed
    await page.goto('/cves');
    
    // Verify error state
    await expect(page.locator('[data-testid=error-message]')).toBeVisible();
    await expect(page.locator('[data-testid=error-message]')).toContainText('Something went wrong');
    await expect(page.locator('[data-testid=retry-button]')).toBeVisible();
    
    // Test retry functionality
    await page.unroute('/api/v1/cves/feed*');
    await page.click('[data-testid=retry-button]');
    
    // Verify successful retry
    await expect(page.locator('[data-testid=error-message]')).not.toBeVisible();
    await expect(page.locator('[data-testid=cve-list]')).toBeVisible();
  });

  test('is responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Navigate to CVE feed
    await page.goto('/cves');
    await expect(page.locator('[data-testid=cve-list]')).toBeVisible();
    
    // Verify mobile layout
    await expect(page.locator('[data-testid=mobile-filter-toggle]')).toBeVisible();
    await expect(page.locator('[data-testid=filter-sidebar]')).not.toBeVisible();
    
    // Test mobile filter toggle
    await page.click('[data-testid=mobile-filter-toggle]');
    await expect(page.locator('[data-testid=filter-sidebar]')).toBeVisible();
    
    // Verify CVE cards stack vertically
    const cveCards = page.locator('[data-testid=cve-card]');
    const firstCardBox = await cveCards.first().boundingBox();
    const secondCardBox = await cveCards.nth(1).boundingBox();
    
    // Cards should be stacked (second card below first)
    expect(secondCardBox!.y).toBeGreaterThan(firstCardBox!.y + firstCardBox!.height);
    
    // Test touch interactions
    await page.tap('[data-testid=cve-card]:first-child');
    await expect(page).toHaveURL(/\/cves\/CVE-/);
  });

  test('supports keyboard navigation', async ({ page }) => {
    // Wait for CVE list to load
    await expect(page.locator('[data-testid=cve-list]')).toBeVisible();
    
    // Test tab navigation through CVE cards
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid=cve-card]:first-child')).toBeFocused();
    
    // Navigate to next CVE card
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid=cve-card]:nth-child(2)')).toBeFocused();
    
    // Test Enter key to open CVE details
    await page.keyboard.press('Enter');
    await expect(page).toHaveURL(/\/cves\/CVE-/);
    
    // Test keyboard navigation in filters
    await page.goBack();
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid=severity-filter]')).toBeFocused();
    
    // Test filter selection with keyboard
    await page.keyboard.press('Space');
    await page.keyboard.press('ArrowDown');
    await page.keyboard.press('Enter');
    
    // Verify filter was applied
    await page.waitForResponse('/api/v1/cves/feed?severity=*');
  });

  test('maintains filter state during navigation', async ({ page }) => {
    // Apply filters
    await page.selectOption('[data-testid=severity-filter]', 'HIGH');
    await page.selectOption('[data-testid=application-filter]', 'WebApp-Production');
    await page.waitForResponse('/api/v1/cves/feed?severity=HIGH*');
    
    // Navigate to CVE details
    await page.click('[data-testid=cve-card]:first-child');
    await expect(page).toHaveURL(/\/cves\/CVE-/);
    
    // Navigate back to CVE feed
    await page.goBack();
    
    // Verify filters are maintained
    await expect(page.locator('[data-testid=severity-filter]')).toHaveValue('HIGH');
    await expect(page.locator('[data-testid=application-filter]')).toHaveValue('WebApp-Production');
    
    // Verify filtered results are still displayed
    const severityBadges = page.locator('[data-testid=severity-badge]');
    const badgeCount = await severityBadges.count();
    
    for (let i = 0; i < badgeCount; i++) {
      await expect(severityBadges.nth(i)).toContainText('HIGH');
    }
  });
});

test.describe('CVE Feed Accessibility', () => {
  test('meets accessibility standards', async ({ page }) => {
    // Navigate to CVE feed
    await page.goto('/login');
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=password-input]', 'SecurePass123!');
    await page.click('[data-testid=login-button]');
    await page.click('[data-testid=nav-cves]');
    
    // Wait for page to load
    await expect(page.locator('[data-testid=cve-list]')).toBeVisible();
    
    // Check for proper heading structure
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('h1')).toContainText('CVE Feed');
    
    // Check for proper ARIA labels
    await expect(page.locator('[data-testid=severity-filter]')).toHaveAttribute('aria-label');
    await expect(page.locator('[data-testid=application-filter]')).toHaveAttribute('aria-label');
    
    // Check for proper focus management
    await page.keyboard.press('Tab');
    const focusedElement = await page.evaluate(() => document.activeElement?.getAttribute('data-testid'));
    expect(focusedElement).toBeTruthy();
    
    // Check color contrast (severity badges should have sufficient contrast)
    const criticalBadge = page.locator('[data-testid=severity-badge]:has-text("CRITICAL")').first();
    if (await criticalBadge.isVisible()) {
      const styles = await criticalBadge.evaluate(el => {
        const computed = window.getComputedStyle(el);
        return {
          backgroundColor: computed.backgroundColor,
          color: computed.color
        };
      });
      // Verify that colors are defined (actual contrast testing would require additional tools)
      expect(styles.backgroundColor).toBeTruthy();
      expect(styles.color).toBeTruthy();
    }
  });
});
