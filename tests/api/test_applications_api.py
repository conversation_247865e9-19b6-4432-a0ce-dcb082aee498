"""Comprehensive API tests for applications endpoints."""

import pytest
from httpx import AsyncClient, ASGITransport
from uuid import uuid4

from src.cve_feed_service.main import app


class TestApplicationsAPI:
    """Test applications API endpoints comprehensively."""

    def _get_client(self):
        """Helper method to get async client."""
        return AsyncClient(transport=ASGITransport(app=app), base_url="http://test")

    @pytest.mark.asyncio
    async def test_create_application_success(self):
        """Test successful application creation."""
        async with self._get_client() as client:
            app_data = {
                "name": f"Test App {uuid4().hex[:8]}",
                "description": "Test application for API testing",
                "version": "1.0.0",
                "environment": "test",
                "criticality": "medium",
                "owner": "Test Team"
            }

            response = await client.post("/api/v1/applications/", json=app_data)

            # Should return 201 for creation (or 422 for validation error, which is also valid)
            assert response.status_code in [201, 422]

            if response.status_code == 201:
                result = response.json()
                assert result["name"] == app_data["name"]
                assert result["description"] == app_data["description"]

    @pytest.mark.asyncio
    async def test_create_application_validation_error(self):
        """Test application creation with invalid data."""
        async with self._get_client() as client:
            # Test with missing required fields
            invalid_data = {
                "description": "Missing name field"
            }

            response = await client.post("/api/v1/applications/", json=invalid_data)
            assert response.status_code == 422

            error_detail = response.json()
            assert "detail" in error_detail

    @pytest.mark.asyncio
    async def test_create_application_empty_name(self):
        """Test application creation with empty name."""
        async with self._get_client() as client:
            app_data = {
                "name": "",  # Empty name
                "description": "Test with empty name",
                "environment": "test"
            }

            response = await client.post("/api/v1/applications/", json=app_data)
            assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_list_applications_default(self):
        """Test listing applications with default parameters."""
        async with self._get_client() as client:
            response = await client.get("/api/v1/applications/")

            # Should return 200 or 401 (if auth required)
            assert response.status_code in [200, 401]

            if response.status_code == 200:
                result = response.json()
                assert isinstance(result, list)

    @pytest.mark.asyncio
    async def test_list_applications_with_pagination(self):
        """Test listing applications with pagination parameters."""
        async with self._get_client() as client:
            response = await client.get("/api/v1/applications/?skip=0&limit=10")

            assert response.status_code in [200, 401]

            if response.status_code == 200:
                result = response.json()
                assert isinstance(result, list)
                assert len(result) <= 10

    @pytest.mark.asyncio
    async def test_list_applications_with_environment_filter(self):
        """Test listing applications with environment filter."""
        async with self._get_client() as client:
            response = await client.get("/api/v1/applications/?environment=test")

            assert response.status_code in [200, 401]

            if response.status_code == 200:
                result = response.json()
                assert isinstance(result, list)

    @pytest.mark.asyncio
    async def test_get_application_by_id(self):
        """Test getting application by ID."""
        async with self._get_client() as client:
            # Use a random UUID to test the endpoint structure
            app_id = str(uuid4())

            response = await client.get(f"/api/v1/applications/{app_id}")

            # Should return 404 (not found) or 401 (unauthorized)
            assert response.status_code in [404, 401]

    async def test_get_application_invalid_uuid(self, async_client):
        """Test getting application with invalid UUID."""
        client = async_client
        
        response = await client.get("/api/v1/applications/invalid-uuid")
        
        # Should return 422 (validation error) or 401 (unauthorized)
        assert response.status_code in [422, 401]

    async def test_update_application(self, async_client):
        """Test updating an application."""
        client = async_client

        app_id = str(uuid4())
        update_data = {
            "description": "Updated description",
            "criticality": "high"
        }

        response = await client.put(f"/api/v1/applications/{app_id}", json=update_data)

        # Should return 404 (not found), 401 (unauthorized), or 405 (method not allowed)
        assert response.status_code in [404, 401, 405]

    async def test_update_application_invalid_data(self, async_client):
        """Test updating application with invalid data."""
        client = async_client

        app_id = str(uuid4())
        invalid_data = {
            "criticality": "invalid_criticality"  # Invalid enum value
        }

        response = await client.put(f"/api/v1/applications/{app_id}", json=invalid_data)

        # Should return 422 (validation error), 401 (unauthorized), or 405 (method not allowed)
        assert response.status_code in [422, 401, 405]

    async def test_delete_application(self, async_client):
        """Test deleting an application."""
        client = async_client
        
        app_id = str(uuid4())
        
        response = await client.delete(f"/api/v1/applications/{app_id}")
        
        # Should return 404 (not found) or 401 (unauthorized)
        assert response.status_code in [404, 401]

    async def test_delete_application_invalid_uuid(self, async_client):
        """Test deleting application with invalid UUID."""
        client = async_client
        
        response = await client.delete("/api/v1/applications/invalid-uuid")
        
        # Should return 422 (validation error) or 401 (unauthorized)
        assert response.status_code in [422, 401]

    async def test_applications_endpoint_methods(self, async_client):
        """Test various HTTP methods on applications endpoint."""
        client = async_client
        
        # Test unsupported methods
        response = await client.patch("/api/v1/applications/")
        assert response.status_code == 405  # Method not allowed
        
        # Test OPTIONS method (should be supported)
        response = await client.options("/api/v1/applications/")
        assert response.status_code in [200, 405]

    async def test_applications_content_type_validation(self, async_client):
        """Test content type validation."""
        client = async_client
        
        # Test with invalid content type
        response = await client.post(
            "/api/v1/applications/",
            data="invalid json",
            headers={"Content-Type": "text/plain"}
        )
        
        # Should return 422 or 415 (unsupported media type)
        assert response.status_code in [422, 415]

    async def test_applications_large_payload(self, async_client):
        """Test handling of large payloads."""
        client = async_client
        
        # Create a large description
        large_description = "x" * 10000  # 10KB description
        
        app_data = {
            "name": f"Large App {uuid4().hex[:8]}",
            "description": large_description,
            "environment": "test"
        }
        
        response = await client.post("/api/v1/applications/", json=app_data)
        
        # Should handle large payloads gracefully
        assert response.status_code in [201, 422, 413, 401]  # 413 = Payload too large

    async def test_applications_special_characters(self, async_client):
        """Test handling of special characters in application data."""
        client = async_client
        
        app_data = {
            "name": f"Test App 特殊字符 {uuid4().hex[:8]}",
            "description": "Application with émojis 🚀 and spëcial chars",
            "environment": "test",
            "owner": "Team with spaces & symbols!"
        }
        
        response = await client.post("/api/v1/applications/", json=app_data)
        
        # Should handle Unicode characters properly
        assert response.status_code in [201, 422, 401]

    async def test_applications_concurrent_requests(self, async_client):
        """Test concurrent requests to applications endpoint."""
        import asyncio
        
        client = async_client
        
        async def make_request():
            return await client.get("/api/v1/applications/")
        
        # Make 5 concurrent requests
        tasks = [make_request() for _ in range(5)]
        responses = await asyncio.gather(*tasks)
        
        # All requests should complete successfully
        for response in responses:
            assert response.status_code in [200, 401]

    async def test_applications_query_parameter_edge_cases(self, async_client):
        """Test edge cases in query parameters."""
        client = async_client
        
        # Test negative skip
        response = await client.get("/api/v1/applications/?skip=-1")
        assert response.status_code in [200, 422, 401]
        
        # Test zero limit
        response = await client.get("/api/v1/applications/?limit=0")
        assert response.status_code in [200, 422, 401]
        
        # Test very large limit
        response = await client.get("/api/v1/applications/?limit=999999")
        assert response.status_code in [200, 422, 401]
        
        # Test invalid environment
        response = await client.get("/api/v1/applications/?environment=invalid")
        assert response.status_code in [200, 422, 401]

    async def test_applications_response_headers(self, async_client):
        """Test response headers from applications endpoint."""
        client = async_client
        
        response = await client.get("/api/v1/applications/")
        
        # Check for common security headers
        if response.status_code == 200:
            headers = response.headers
            # These might be set by middleware
            assert "content-type" in headers
            
    async def test_applications_error_response_format(self, async_client):
        """Test error response format consistency."""
        client = async_client
        
        # Test with invalid JSON
        response = await client.post(
            "/api/v1/applications/",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 422:
            error_response = response.json()
            assert "detail" in error_response
