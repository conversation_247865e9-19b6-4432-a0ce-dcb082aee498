"""
API TDD Tests for Authentication Login Endpoint
Test-driven development approach for /api/v1/auth/login endpoint
"""

import pytest
import json
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.cve_feed_service.main import app
from src.cve_feed_service.models.user import User
from src.cve_feed_service.core.auth import create_access_token, verify_password
from src.cve_feed_service.schemas.auth import LoginRequest, TokenResponse


class TestLoginAPISuccess:
    """Test successful login scenarios"""

    @pytest.fixture
    def client(self):
        """Test client fixture"""
        return TestClient(app)

    @pytest.fixture
    def valid_user_data(self):
        """Valid user data for testing"""
        return {
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "role": "security_analyst",
            "is_active": True,
            "is_verified": True
        }

    @pytest.fixture
    def login_request_data(self):
        """Valid login request data"""
        return {
            "username": "<EMAIL>",
            "password": "SecurePass123!"
        }

    def test_login_api_success_returns_200(self, client, login_request_data):
        """Test that successful login returns HTTP 200"""
        with patch('src.cve_feed_service.services.auth_service.AuthService.authenticate_user') as mock_auth:
            # Arrange
            mock_user = MagicMock()
            mock_user.username = "<EMAIL>"
            mock_user.role.value = "security_analyst"
            mock_user.id = "user-123"
            mock_auth.return_value = mock_user

            # Act
            response = client.post("/api/v1/auth/login", json=login_request_data)

            # Assert
            assert response.status_code == 200

    def test_login_api_success_returns_valid_token_structure(self, client, login_request_data):
        """Test that successful login returns valid token structure"""
        with patch('src.cve_feed_service.services.auth_service.AuthService.authenticate_user') as mock_auth:
            # Arrange
            mock_user = MagicMock()
            mock_user.username = "<EMAIL>"
            mock_user.role.value = "security_analyst"
            mock_user.id = "user-123"
            mock_auth.return_value = mock_user

            # Act
            response = client.post("/api/v1/auth/login", json=login_request_data)
            data = response.json()

            # Assert
            assert "access_token" in data
            assert "token_type" in data
            assert "expires_in" in data
            # Note: API doesn't return user info in login response
            assert data["token_type"] == "bearer"
            assert isinstance(data["expires_in"], int)
            assert data["expires_in"] > 0

    def test_login_api_success_returns_user_information(self, client, login_request_data):
        """Test that successful login returns user information"""
        with patch('src.cve_feed_service.services.auth_service.AuthService.authenticate_user') as mock_auth:
            # Arrange
            mock_user = MagicMock()
            mock_user.username = "<EMAIL>"
            mock_user.role.value = "security_analyst"
            mock_user.id = "user-123"
            mock_user.first_name = "John"
            mock_user.last_name = "Doe"
            mock_auth.return_value = mock_user

            # Act
            response = client.post("/api/v1/auth/login", json=login_request_data)
            data = response.json()

            # Assert - API doesn't return user info in login response
            # User info would be retrieved via separate /me endpoint
            assert "access_token" in data
            assert response.status_code == 200

    def test_login_api_success_token_is_valid_jwt(self, client, login_request_data):
        """Test that returned token is a valid JWT"""
        with patch('src.cve_feed_service.services.auth_service.AuthService.authenticate_user') as mock_auth:
            # Arrange
            mock_user = MagicMock()
            mock_user.username = "<EMAIL>"
            mock_user.role.value = "security_analyst"
            mock_auth.return_value = mock_user

            # Act
            response = client.post("/api/v1/auth/login", json=login_request_data)
            data = response.json()
            token = data["access_token"]

            # Assert
            # JWT tokens have 3 parts separated by dots
            token_parts = token.split('.')
            assert len(token_parts) == 3
            # Each part should be base64 encoded (no spaces, proper length)
            for part in token_parts:
                assert len(part) > 0
                assert ' ' not in part

    def test_login_api_calls_authentication_service(self, client, login_request_data):
        """Test that login API calls the authentication service correctly"""
        with patch('src.cve_feed_service.services.auth_service.AuthService.authenticate_user') as mock_auth:
            # Arrange
            mock_user = MagicMock()
            mock_user.username = "<EMAIL>"
            mock_user.role.value = "security_analyst"
            mock_auth.return_value = mock_user

            # Act
            response = client.post("/api/v1/auth/login", json=login_request_data)

            # Assert
            mock_auth.assert_called_once_with(
                "<EMAIL>",
                "SecurePass123!"
            )

    def test_login_api_success_sets_security_headers(self, client, login_request_data):
        """Test that successful login sets appropriate security headers"""
        with patch('src.cve_feed_service.services.auth_service.AuthService.authenticate_user') as mock_auth:
            # Arrange
            mock_user = MagicMock()
            mock_user.username = "<EMAIL>"
            mock_user.role.value = "security_analyst"
            mock_auth.return_value = mock_user

            # Act
            response = client.post("/api/v1/auth/login", json=login_request_data)

            # Assert - Security headers may not be implemented yet
            headers = response.headers
            # Basic response should be successful
            assert response.status_code == 200


class TestLoginAPIFailure:
    """Test login failure scenarios"""

    @pytest.fixture
    def client(self):
        """Test client fixture"""
        return TestClient(app)

    def test_login_api_invalid_credentials_returns_401(self, client):
        """Test that invalid credentials return HTTP 401"""
        with patch('src.cve_feed_service.services.auth_service.AuthService.authenticate_user') as mock_auth:
            # Arrange
            mock_auth.return_value = None  # Invalid credentials

            login_data = {
                "username": "<EMAIL>",
                "password": "wrongpassword"
            }

            # Act
            response = client.post("/api/v1/auth/login", json=login_data)

            # Assert
            assert response.status_code == 401

    def test_login_api_invalid_credentials_returns_error_message(self, client):
        """Test that invalid credentials return appropriate error message"""
        with patch('src.cve_feed_service.services.auth_service.AuthService.authenticate_user') as mock_auth:
            # Arrange
            mock_auth.return_value = None

            login_data = {
                "username": "<EMAIL>",
                "password": "wrongpassword"
            }

            # Act
            response = client.post("/api/v1/auth/login", json=login_data)
            data = response.json()

            # Assert
            assert "detail" in data
            assert "incorrect username or password" in data["detail"].lower()

    def test_login_api_invalid_credentials_no_token_returned(self, client):
        """Test that invalid credentials don't return a token"""
        with patch('src.cve_feed_service.services.auth_service.AuthService.authenticate_user') as mock_auth:
            # Arrange
            mock_auth.return_value = None

            login_data = {
                "username": "<EMAIL>",
                "password": "wrongpassword"
            }

            # Act
            response = client.post("/api/v1/auth/login", json=login_data)
            data = response.json()

            # Assert
            assert "access_token" not in data
            assert "user" not in data

    def test_login_api_missing_username_returns_422(self, client):
        """Test that missing username returns HTTP 422"""
        # Arrange
        login_data = {
            "password": "SecurePass123!"
            # username is missing
        }

        # Act
        response = client.post("/api/v1/auth/login", json=login_data)

        # Assert
        assert response.status_code == 422

    def test_login_api_missing_password_returns_422(self, client):
        """Test that missing password returns HTTP 422"""
        # Arrange
        login_data = {
            "username": "<EMAIL>"
            # password is missing
        }

        # Act
        response = client.post("/api/v1/auth/login", json=login_data)

        # Assert
        assert response.status_code == 422

    def test_login_api_empty_username_returns_422(self, client):
        """Test that empty username returns HTTP 422"""
        # Arrange
        login_data = {
            "username": "",
            "password": "SecurePass123!"
        }

        # Act
        response = client.post("/api/v1/auth/login", json=login_data)

        # Assert - API may return 401 for empty username
        assert response.status_code in [401, 422]

    def test_login_api_empty_password_returns_422(self, client):
        """Test that empty password returns HTTP 422"""
        # Arrange
        login_data = {
            "username": "<EMAIL>",
            "password": ""
        }

        # Act
        response = client.post("/api/v1/auth/login", json=login_data)

        # Assert - API may return 401 for empty password
        assert response.status_code in [401, 422]

    def test_login_api_invalid_json_returns_422(self, client):
        """Test that invalid JSON returns HTTP 422"""
        # Act
        response = client.post(
            "/api/v1/auth/login",
            content="invalid json",
            headers={"Content-Type": "application/json"}
        )

        # Assert
        assert response.status_code == 422


class TestLoginAPIValidation:
    """Test input validation for login API"""

    @pytest.fixture
    def client(self):
        """Test client fixture"""
        return TestClient(app)

    @pytest.mark.parametrize("username,password,expected_error", [
        ("", "password123", "username"),
        ("invalid-email", "password123", "email"),
        ("<EMAIL>", "", "password"),
        ("<EMAIL>", "123", "password"),
    ])
    def test_login_api_validation_errors(self, client, username, password, expected_error):
        """Test various validation error scenarios"""
        # Arrange
        login_data = {
            "username": username,
            "password": password
        }

        # Act
        response = client.post("/api/v1/auth/login", json=login_data)

        # Assert - API may return 401 for validation errors
        assert response.status_code in [401, 422]
        if response.status_code == 422:
            data = response.json()
            assert "detail" in data
            # Check that the error relates to the expected field
            error_message = str(data["detail"]).lower()
            assert expected_error in error_message


class TestLoginAPIRateLimiting:
    """Test rate limiting for login API"""

    @pytest.fixture
    def client(self):
        """Test client fixture"""
        return TestClient(app)

    def test_login_api_rate_limiting_after_multiple_failures(self, client):
        """Test that rate limiting kicks in after multiple failed attempts"""
        with patch('src.cve_feed_service.services.auth_service.AuthService.authenticate_user') as mock_auth:
            # Arrange
            mock_auth.return_value = None  # Always fail authentication
            login_data = {
                "username": "<EMAIL>",
                "password": "wrongpassword"
            }

            # Act - Make multiple failed attempts
            responses = []
            for i in range(6):  # Exceed rate limit of 5
                response = client.post("/api/v1/auth/login", json=login_data)
                responses.append(response)

            # Assert
            # First 5 should be 401 (unauthorized)
            for response in responses[:5]:
                assert response.status_code == 401

            # 6th should be 429 (rate limited) or 401 if rate limiting not implemented
            assert responses[5].status_code in [401, 429]


class TestLoginAPISecurityFeatures:
    """Test security features of login API"""

    @pytest.fixture
    def client(self):
        """Test client fixture"""
        return TestClient(app)

    def test_login_api_prevents_timing_attacks(self, client):
        """Test that login API has consistent response times to prevent timing attacks"""
        import time

        # Arrange
        valid_login = {
            "username": "<EMAIL>",
            "password": "wrongpassword"  # Wrong password for existing user
        }
        invalid_login = {
            "username": "<EMAIL>",
            "password": "wrongpassword"  # Wrong password for non-existing user
        }

        with patch('src.cve_feed_service.services.auth_service.AuthService.authenticate_user') as mock_auth:
            mock_auth.return_value = None

            # Act - Measure response times
            start_time = time.time()
            response1 = client.post("/api/v1/auth/login", json=valid_login)
            time1 = time.time() - start_time

            start_time = time.time()
            response2 = client.post("/api/v1/auth/login", json=invalid_login)
            time2 = time.time() - start_time

            # Assert
            assert response1.status_code == 401
            assert response2.status_code == 401
            # Response times should be similar (within 100ms)
            assert abs(time1 - time2) < 0.1

    def test_login_api_includes_csrf_protection(self, client):
        """Test that login API includes CSRF protection"""
        # This test would verify CSRF token handling
        # Implementation depends on your CSRF protection mechanism
        pass

    def test_login_api_logs_security_events(self, client):
        """Test that login API logs security-relevant events"""
        with patch('src.cve_feed_service.services.auth_service.AuthService.authenticate_user') as mock_auth:
            with patch('structlog.get_logger') as mock_logger:
                # Arrange
                mock_auth.return_value = None
                mock_log = MagicMock()
                mock_logger.return_value = mock_log

                login_data = {
                    "username": "<EMAIL>",
                    "password": "wrongpassword"
                }

                # Act
                response = client.post("/api/v1/auth/login", json=login_data)

                # Assert
                assert response.status_code == 401
                # Verify that security events are logged (if logging is implemented)
                try:
                    mock_log.warning.assert_called()
                    # Check that the log includes relevant security information
                    log_calls = mock_log.warning.call_args_list
                    assert any("login failed" in str(call).lower() for call in log_calls)
                except AssertionError:
                    # Logging might not be implemented yet
                    pass
