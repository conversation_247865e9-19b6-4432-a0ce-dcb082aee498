"""Simple database integration tests focusing on Application model only."""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.pool import StaticPool

from src.cve_feed_service.db.base import Base
from src.cve_feed_service.models.application import Application
from src.cve_feed_service.services.application_service import ApplicationService
from src.cve_feed_service.schemas.application import ApplicationCreate, ApplicationUpdate


@pytest.mark.integration
class TestSimpleDatabase:
    """Test simple database operations with Application model only."""

    async def test_application_table_creation(self):
        """Test creating just the applications table."""
        # Create in-memory SQLite database
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        # Create only the applications table
        async with engine.begin() as conn:
            await conn.run_sync(lambda sync_conn: Application.__table__.create(sync_conn))
        
        # Test basic operations
        async with AsyncSession(engine) as session:
            # Test direct model creation
            from uuid import uuid4
            from datetime import datetime
            
            app = Application(
                id=uuid4(),
                name="Test Application",
                environment="test",
                criticality="medium",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            session.add(app)
            await session.commit()
            await session.refresh(app)
            
            assert app.id is not None
            assert app.name == "Test Application"
            assert app.environment == "test"
            assert app.criticality == "medium"
        
        await engine.dispose()

    async def test_application_service_basic_operations(self):
        """Test ApplicationService with just the applications table."""
        # Create in-memory SQLite database
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        # Create only the applications table
        async with engine.begin() as conn:
            await conn.run_sync(lambda sync_conn: Application.__table__.create(sync_conn))
        
        async with AsyncSession(engine) as session:
            app_service = ApplicationService(session)
            
            # CREATE
            app_data = ApplicationCreate(
                name="Service Test App",
                description="Testing service operations",
                environment="test",
                criticality="low",
                owner="Test Team"
            )
            created_app = await app_service.create_application(app_data)
            
            assert created_app.name == "Service Test App"
            assert created_app.description == "Testing service operations"
            assert created_app.environment == "test"
            assert created_app.criticality == "low"
            assert created_app.owner == "Test Team"
            assert created_app.id is not None
            
            # READ
            retrieved_app = await app_service.get_application(created_app.id)
            assert retrieved_app is not None
            assert retrieved_app.id == created_app.id
            assert retrieved_app.name == created_app.name
            
            # LIST
            apps = await app_service.list_applications()
            assert len(apps) == 1
            assert apps[0].name == "Service Test App"
            
            # UPDATE
            update_data = ApplicationUpdate(
                description="Updated description",
                criticality="high"
            )
            updated_app = await app_service.update_application(created_app.id, update_data)
            
            assert updated_app is not None
            assert updated_app.description == "Updated description"
            assert updated_app.criticality == "high"
            assert updated_app.name == "Service Test App"  # Unchanged
            
            # DELETE
            delete_result = await app_service.delete_application(created_app.id)
            assert delete_result is True
            
            # Verify deletion (soft delete)
            deleted_app = await app_service.get_application(created_app.id)
            assert deleted_app is None
        
        await engine.dispose()

    async def test_application_filtering_and_pagination(self):
        """Test application filtering and pagination."""
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(lambda sync_conn: Application.__table__.create(sync_conn))
        
        async with AsyncSession(engine) as session:
            app_service = ApplicationService(session)
            
            # Create multiple applications
            apps_data = [
                ApplicationCreate(name="Prod App 1", environment="production", criticality="high"),
                ApplicationCreate(name="Prod App 2", environment="production", criticality="medium"),
                ApplicationCreate(name="Test App 1", environment="test", criticality="low"),
                ApplicationCreate(name="Test App 2", environment="test", criticality="high"),
                ApplicationCreate(name="Dev App 1", environment="development", criticality="low"),
            ]
            
            created_apps = []
            for app_data in apps_data:
                app = await app_service.create_application(app_data)
                created_apps.append(app)
            
            # Test listing all applications
            all_apps = await app_service.list_applications()
            assert len(all_apps) == 5
            
            # Test filtering by environment
            prod_apps = await app_service.list_applications(environment="production")
            assert len(prod_apps) == 2
            assert all(app.environment == "production" for app in prod_apps)
            
            test_apps = await app_service.list_applications(environment="test")
            assert len(test_apps) == 2
            assert all(app.environment == "test" for app in test_apps)
            
            dev_apps = await app_service.list_applications(environment="development")
            assert len(dev_apps) == 1
            assert dev_apps[0].environment == "development"
            
            # Test pagination
            page1 = await app_service.list_applications(skip=0, limit=2)
            assert len(page1) == 2
            
            page2 = await app_service.list_applications(skip=2, limit=2)
            assert len(page2) == 2
            
            page3 = await app_service.list_applications(skip=4, limit=2)
            assert len(page3) == 1
            
            # Verify no duplicates between pages
            page1_ids = {app.id for app in page1}
            page2_ids = {app.id for app in page2}
            page3_ids = {app.id for app in page3}
            
            assert len(page1_ids.intersection(page2_ids)) == 0
            assert len(page1_ids.intersection(page3_ids)) == 0
            assert len(page2_ids.intersection(page3_ids)) == 0
        
        await engine.dispose()

    async def test_application_duplicate_handling(self):
        """Test handling of duplicate application names."""
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(lambda sync_conn: Application.__table__.create(sync_conn))
        
        async with AsyncSession(engine) as session:
            app_service = ApplicationService(session)
            
            # Create first application
            app_data = ApplicationCreate(
                name="Duplicate Test",
                environment="production"
            )
            first_app = await app_service.create_application(app_data)
            assert first_app.name == "Duplicate Test"
            assert first_app.environment == "production"
            
            # Try to create duplicate in same environment (should fail)
            with pytest.raises(ValueError, match="already exists"):
                await app_service.create_application(app_data)
            
            # Create same name in different environment (should succeed)
            app_data_different_env = ApplicationCreate(
                name="Duplicate Test",
                environment="test"
            )
            second_app = await app_service.create_application(app_data_different_env)
            assert second_app.name == "Duplicate Test"
            assert second_app.environment == "test"
            assert second_app.id != first_app.id
            
            # Verify both exist
            all_apps = await app_service.list_applications()
            assert len(all_apps) == 2
            
            environments = {app.environment for app in all_apps}
            assert environments == {"production", "test"}
        
        await engine.dispose()

    async def test_application_soft_delete(self):
        """Test soft delete functionality."""
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(lambda sync_conn: Application.__table__.create(sync_conn))
        
        async with AsyncSession(engine) as session:
            app_service = ApplicationService(session)
            
            # Create application
            app_data = ApplicationCreate(
                name="Soft Delete Test",
                environment="test"
            )
            created_app = await app_service.create_application(app_data)
            app_id = created_app.id
            
            # Verify it exists
            retrieved_app = await app_service.get_application(app_id)
            assert retrieved_app is not None
            
            # Delete it
            delete_result = await app_service.delete_application(app_id)
            assert delete_result is True
            
            # Verify it's gone from normal queries
            deleted_app = await app_service.get_application(app_id)
            assert deleted_app is None
            
            # Verify it doesn't appear in list
            apps = await app_service.list_applications()
            assert len(apps) == 0
            
            # Create another app with same name (should work since first is soft-deleted)
            new_app_data = ApplicationCreate(
                name="Soft Delete Test",
                environment="test"
            )
            new_app = await app_service.create_application(new_app_data)
            assert new_app.name == "Soft Delete Test"
            assert new_app.id != app_id  # Different ID
        
        await engine.dispose()

    def test_application_schema_validation(self):
        """Test application schema validation."""
        # Valid data
        valid_data = {
            "name": "Valid App",
            "environment": "production",
            "criticality": "high",
            "description": "A valid application",
            "owner": "Team A"
        }
        app_create = ApplicationCreate(**valid_data)
        assert app_create.name == "Valid App"
        assert app_create.environment == "production"
        assert app_create.criticality == "high"
        
        # Test with minimal data
        minimal_data = {"name": "Minimal App"}
        app_create_minimal = ApplicationCreate(**minimal_data)
        assert app_create_minimal.name == "Minimal App"
        assert app_create_minimal.environment is None
        
        # Test update schema
        update_data = {"description": "Updated", "criticality": "low"}
        app_update = ApplicationUpdate(**update_data)
        assert app_update.description == "Updated"
        assert app_update.criticality == "low"
        assert app_update.name is None  # Not provided
        
        # Test validation errors
        with pytest.raises(ValueError):
            ApplicationCreate(name="")  # Empty name
        
        with pytest.raises(ValueError):
            ApplicationCreate(name="x" * 300)  # Too long name
