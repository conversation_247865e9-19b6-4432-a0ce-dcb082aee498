"""Simple database integration tests that work around SQLAlchemy async issues."""

import pytest
from uuid import uuid4
from datetime import datetime

from src.cve_feed_service.services.application_service import ApplicationService
from src.cve_feed_service.services.auth_service import AuthService
from src.cve_feed_service.services.cve_service import CVEService
from src.cve_feed_service.schemas.application import ApplicationCreate, ApplicationUpdate
from src.cve_feed_service.schemas.auth import UserCreate, APIKeyCreate
from src.cve_feed_service.schemas.cve import CVECreate
from src.cve_feed_service.models.user import UserRole


@pytest.mark.integration
class TestSimpleDatabaseIntegration:
    """Test simple database integration that avoids SQLAlchemy async issues."""

    async def test_application_crud_operations(self, db_session):
        """Test application CRUD operations without soft delete."""
        service = ApplicationService(db_session)
        
        # CREATE
        app_data = ApplicationCreate(
            name="Simple Test App",
            description="Testing simple operations",
            environment="test",
            criticality="medium",
            owner="Test Team"
        )
        
        created_app = await service.create_application(app_data)
        assert created_app.name == "Simple Test App"
        assert created_app.environment == "test"
        assert created_app.criticality == "medium"
        assert created_app.id is not None
        
        # Store the ID for later use
        app_id = created_app.id
        
        # READ
        retrieved_app = await service.get_application(app_id)
        assert retrieved_app is not None
        assert retrieved_app.id == app_id
        assert retrieved_app.name == "Simple Test App"
        
        # UPDATE
        update_data = ApplicationUpdate(
            description="Updated description",
            criticality="high"
        )
        updated_app = await service.update_application(app_id, update_data)
        assert updated_app is not None
        assert updated_app.description == "Updated description"
        assert updated_app.criticality == "high"
        assert updated_app.name == "Simple Test App"  # Unchanged
        
        # LIST
        apps = await service.list_applications()
        assert len(apps) >= 1
        app_names = [app.name for app in apps]
        assert "Simple Test App" in app_names

    async def test_application_filtering_and_pagination(self, db_session):
        """Test application filtering and pagination."""
        service = ApplicationService(db_session)
        
        # Create multiple applications
        test_apps = []
        for i in range(5):
            app_data = ApplicationCreate(
                name=f"Filter Test App {i}",
                environment="test" if i % 2 == 0 else "prod",
                criticality="low" if i < 3 else "high",
                description=f"Test app number {i}"
            )
            app = await service.create_application(app_data)
            test_apps.append(app)
        
        # Test environment filtering
        test_env_apps = await service.list_applications(environment="test")
        assert len(test_env_apps) >= 3  # At least the 3 we created (0, 2, 4)
        
        prod_env_apps = await service.list_applications(environment="prod")
        assert len(prod_env_apps) >= 2  # At least the 2 we created (1, 3)
        
        # Test pagination
        page1 = await service.list_applications(skip=0, limit=3)
        page2 = await service.list_applications(skip=3, limit=3)
        
        assert len(page1) == 3
        assert len(page2) >= 2  # At least 2 more from our test
        
        # Verify no overlap
        page1_ids = {app.id for app in page1}
        page2_ids = {app.id for app in page2}
        overlap = page1_ids.intersection(page2_ids)
        assert len(overlap) == 0

    async def test_user_authentication_flow(self, db_session):
        """Test user creation and authentication."""
        service = AuthService(db_session)
        
        # CREATE USER
        user_data = UserCreate(
            username="test_user_simple",
            email="<EMAIL>",
            full_name="Simple Test User",
            password="secure_password_123",
            role=UserRole.IT_ADMIN
        )
        
        created_user = await service.create_user(user_data)
        assert created_user.username == "test_user_simple"
        assert created_user.email == "<EMAIL>"
        assert created_user.role == UserRole.IT_ADMIN
        assert created_user.id is not None
        
        # AUTHENTICATE USER - correct password
        authenticated_user = await service.authenticate_user(
            "test_user_simple", 
            "secure_password_123"
        )
        assert authenticated_user is not None
        assert authenticated_user.username == "test_user_simple"
        
        # AUTHENTICATE USER - wrong password
        wrong_auth = await service.authenticate_user(
            "test_user_simple", 
            "wrong_password"
        )
        assert wrong_auth is None
        
        # GET USER BY USERNAME
        retrieved_user = await service.get_user_by_username("test_user_simple")
        assert retrieved_user is not None
        assert retrieved_user.username == "test_user_simple"
        assert retrieved_user.email == "<EMAIL>"

    async def test_cve_operations(self, db_session):
        """Test CVE operations."""
        service = CVEService(db_session)
        
        # CREATE CVE
        cve_data = CVECreate(
            cve_id="CVE-2023-SIMPLE",
            description="Simple test CVE for integration testing",
            published_date=datetime.utcnow(),
            last_modified_date=datetime.utcnow(),
            cvss_v3_score=6.5,
            cvss_v3_vector="CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:N",
            cvss_v3_severity="MEDIUM"
        )
        
        created_cve = await service.create_cve(cve_data)
        assert created_cve.cve_id == "CVE-2023-SIMPLE"
        assert created_cve.description == "Simple test CVE for integration testing"
        assert created_cve.cvss_v3_score == 6.5
        assert created_cve.cvss_v3_severity == "MEDIUM"
        
        # GET CVE BY ID
        retrieved_cve = await service.get_cve_by_id("CVE-2023-SIMPLE")
        assert retrieved_cve is not None
        assert retrieved_cve.cve_id == "CVE-2023-SIMPLE"
        assert retrieved_cve.description == "Simple test CVE for integration testing"
        
        # LIST CVES
        cves, total = await service.list_cves()
        assert total >= 1
        assert len(cves) >= 1
        cve_ids = [cve.cve_id for cve in cves]
        assert "CVE-2023-SIMPLE" in cve_ids

    async def test_duplicate_prevention(self, db_session):
        """Test that duplicate prevention works correctly."""
        app_service = ApplicationService(db_session)
        cve_service = CVEService(db_session)
        auth_service = AuthService(db_session)
        
        # Test application duplicates
        app_data = ApplicationCreate(
            name="Duplicate Test",
            environment="production",
            description="Testing duplicates"
        )
        
        # First creation should succeed
        first_app = await app_service.create_application(app_data)
        assert first_app.name == "Duplicate Test"
        
        # Second creation should fail
        with pytest.raises(ValueError, match="already exists"):
            await app_service.create_application(app_data)
        
        # Same name in different environment should succeed
        app_data_diff_env = ApplicationCreate(
            name="Duplicate Test",
            environment="development",
            description="Same name, different environment"
        )
        second_app = await app_service.create_application(app_data_diff_env)
        assert second_app.name == "Duplicate Test"
        assert second_app.environment == "development"
        
        # Test CVE duplicates
        cve_data = CVECreate(
            cve_id="CVE-2023-DUPLICATE",
            description="Testing CVE duplicates"
        )
        
        # First creation should succeed
        first_cve = await cve_service.create_cve(cve_data)
        assert first_cve.cve_id == "CVE-2023-DUPLICATE"
        
        # Second creation should fail
        with pytest.raises(ValueError, match="already exists"):
            await cve_service.create_cve(cve_data)
        
        # Test user duplicates
        user_data = UserCreate(
            username="duplicate_user",
            email="<EMAIL>",
            password="password123",
            role=UserRole.IT_ADMIN
        )
        
        # First creation should succeed
        first_user = await auth_service.create_user(user_data)
        assert first_user.username == "duplicate_user"
        
        # Second creation should fail
        with pytest.raises(ValueError, match="already exists"):
            await auth_service.create_user(user_data)

    async def test_error_handling(self, db_session):
        """Test error handling for non-existent entities."""
        app_service = ApplicationService(db_session)
        cve_service = CVEService(db_session)
        auth_service = AuthService(db_session)
        
        # Test non-existent entity retrieval
        non_existent_id = uuid4()
        
        # Application
        app_result = await app_service.get_application(non_existent_id)
        assert app_result is None
        
        # CVE
        cve_result = await cve_service.get_cve_by_id("CVE-9999-NONEXISTENT")
        assert cve_result is None
        
        # User
        user_result = await auth_service.get_user_by_username("nonexistent_user")
        assert user_result is None
        
        # Test update of non-existent entities
        update_data = ApplicationUpdate(description="Won't work")
        update_result = await app_service.update_application(non_existent_id, update_data)
        assert update_result is None

    async def test_cross_service_data_consistency(self, db_session):
        """Test data consistency across services."""
        app_service = ApplicationService(db_session)
        auth_service = AuthService(db_session)
        cve_service = CVEService(db_session)
        
        # Create entities in different services
        user_data = UserCreate(
            username="consistency_user",
            email="<EMAIL>",
            password="password123",
            role=UserRole.IT_ADMIN
        )
        user = await auth_service.create_user(user_data)
        
        app_data = ApplicationCreate(
            name="Consistency App",
            environment="test",
            owner="consistency_user"  # Reference the user
        )
        app = await app_service.create_application(app_data)
        app_id = app.id  # Store ID before potential lazy loading issues

        cve_data = CVECreate(
            cve_id="CVE-2023-CONSISTENCY",
            description="Testing cross-service consistency"
        )
        cve = await cve_service.create_cve(cve_data)

        # Verify all entities exist and can be retrieved
        retrieved_user = await auth_service.get_user_by_username("consistency_user")
        retrieved_app = await app_service.get_application(app_id)
        retrieved_cve = await cve_service.get_cve_by_id("CVE-2023-CONSISTENCY")
        
        assert retrieved_user is not None
        assert retrieved_user.username == "consistency_user"
        
        assert retrieved_app is not None
        assert retrieved_app.name == "Consistency App"
        assert retrieved_app.owner == "consistency_user"
        
        assert retrieved_cve is not None
        assert retrieved_cve.cve_id == "CVE-2023-CONSISTENCY"
