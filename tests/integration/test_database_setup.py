"""Integration tests for database setup and basic operations."""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.pool import StaticPool

from src.cve_feed_service.db.base import Base
from src.cve_feed_service.models.application import Application
from src.cve_feed_service.models.user import User
from src.cve_feed_service.models.cve import CVE
from src.cve_feed_service.services.application_service import ApplicationService
from src.cve_feed_service.schemas.application import ApplicationCreate


@pytest.mark.integration
class TestDatabaseSetup:
    """Test database setup and basic operations."""

    async def test_create_tables_and_basic_operations(self):
        """Test creating tables and performing basic operations."""
        # Create in-memory SQLite database
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
            echo=True,  # Enable SQL logging for debugging
        )
        
        # Create all tables
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        # Test basic operations
        async with AsyncSession(engine) as session:
            # Test application creation
            app_service = ApplicationService(session)
            app_data = ApplicationCreate(
                name="Test Application",
                environment="test",
                criticality="medium"
            )
            
            # Create application
            created_app = await app_service.create_application(app_data)
            
            # Verify creation
            assert created_app.name == "Test Application"
            assert created_app.environment == "test"
            assert created_app.criticality == "medium"
            assert created_app.id is not None
            
            # Test retrieval
            retrieved_app = await app_service.get_application(created_app.id)
            assert retrieved_app is not None
            assert retrieved_app.name == created_app.name
            
            # Test listing
            apps = await app_service.list_applications()
            assert len(apps) == 1
            assert apps[0].name == "Test Application"
        
        # Clean up
        await engine.dispose()

    async def test_application_service_crud_operations(self):
        """Test complete CRUD operations for applications."""
        # Create in-memory SQLite database
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        # Create all tables
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        async with AsyncSession(engine) as session:
            app_service = ApplicationService(session)
            
            # CREATE
            app_data = ApplicationCreate(
                name="CRUD Test App",
                description="Testing CRUD operations",
                environment="test",
                criticality="low",
                owner="Test Team"
            )
            created_app = await app_service.create_application(app_data)
            
            assert created_app.name == "CRUD Test App"
            assert created_app.description == "Testing CRUD operations"
            
            # READ
            retrieved_app = await app_service.get_application(created_app.id)
            assert retrieved_app is not None
            assert retrieved_app.id == created_app.id
            
            # UPDATE
            from src.cve_feed_service.schemas.application import ApplicationUpdate
            update_data = ApplicationUpdate(
                description="Updated description",
                criticality="high"
            )
            updated_app = await app_service.update_application(created_app.id, update_data)
            
            assert updated_app is not None
            assert updated_app.description == "Updated description"
            assert updated_app.criticality == "high"
            assert updated_app.name == "CRUD Test App"  # Unchanged
            
            # DELETE
            delete_result = await app_service.delete_application(created_app.id)
            assert delete_result is True
            
            # Verify deletion (soft delete)
            deleted_app = await app_service.get_application(created_app.id)
            assert deleted_app is None
        
        await engine.dispose()

    async def test_multiple_applications_with_filtering(self):
        """Test creating multiple applications and filtering."""
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        async with AsyncSession(engine) as session:
            app_service = ApplicationService(session)
            
            # Create multiple applications
            apps_data = [
                ApplicationCreate(name="Prod App 1", environment="production", criticality="high"),
                ApplicationCreate(name="Prod App 2", environment="production", criticality="medium"),
                ApplicationCreate(name="Test App 1", environment="test", criticality="low"),
                ApplicationCreate(name="Test App 2", environment="test", criticality="high"),
            ]
            
            created_apps = []
            for app_data in apps_data:
                app = await app_service.create_application(app_data)
                created_apps.append(app)
            
            # Test listing all applications
            all_apps = await app_service.list_applications()
            assert len(all_apps) == 4
            
            # Test filtering by environment
            prod_apps = await app_service.list_applications(environment="production")
            assert len(prod_apps) == 2
            assert all(app.environment == "production" for app in prod_apps)
            
            test_apps = await app_service.list_applications(environment="test")
            assert len(test_apps) == 2
            assert all(app.environment == "test" for app in test_apps)
            
            # Test pagination
            page1 = await app_service.list_applications(skip=0, limit=2)
            assert len(page1) == 2
            
            page2 = await app_service.list_applications(skip=2, limit=2)
            assert len(page2) == 2
            
            # Verify no duplicates between pages
            page1_ids = {app.id for app in page1}
            page2_ids = {app.id for app in page2}
            assert len(page1_ids.intersection(page2_ids)) == 0
        
        await engine.dispose()

    async def test_duplicate_application_handling(self):
        """Test handling of duplicate application names."""
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        async with AsyncSession(engine) as session:
            app_service = ApplicationService(session)
            
            # Create first application
            app_data = ApplicationCreate(
                name="Duplicate Test",
                environment="production"
            )
            first_app = await app_service.create_application(app_data)
            assert first_app.name == "Duplicate Test"
            
            # Try to create duplicate in same environment (should fail)
            with pytest.raises(ValueError, match="already exists"):
                await app_service.create_application(app_data)
            
            # Create same name in different environment (should succeed)
            app_data_different_env = ApplicationCreate(
                name="Duplicate Test",
                environment="test"
            )
            second_app = await app_service.create_application(app_data_different_env)
            assert second_app.name == "Duplicate Test"
            assert second_app.environment == "test"
            assert second_app.id != first_app.id
        
        await engine.dispose()

    async def test_database_models_relationships(self):
        """Test that database models and relationships work correctly."""
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        async with AsyncSession(engine) as session:
            # Test that we can create basic model instances
            from uuid import uuid4
            from datetime import datetime
            
            # Create application directly
            app = Application(
                id=uuid4(),
                name="Direct Model Test",
                environment="test",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            session.add(app)
            await session.commit()
            await session.refresh(app)
            
            assert app.id is not None
            assert app.name == "Direct Model Test"
            assert app.created_at is not None
        
        await engine.dispose()

    def test_model_imports_and_structure(self):
        """Test that all models can be imported and have expected structure."""
        # Test model imports
        from src.cve_feed_service.models.application import Application, Component, CPEMapping
        from src.cve_feed_service.models.user import User, APIKey, UserRole
        from src.cve_feed_service.models.cve import CVE, CVECPEApplicability
        
        # Test that models have expected attributes
        assert hasattr(Application, '__tablename__')
        assert hasattr(Application, 'id')
        assert hasattr(Application, 'name')
        assert hasattr(Application, 'environment')
        
        assert hasattr(Component, '__tablename__')
        assert hasattr(Component, 'id')
        assert hasattr(Component, 'application_id')
        assert hasattr(Component, 'name')
        
        assert hasattr(CPEMapping, '__tablename__')
        assert hasattr(CPEMapping, 'id')
        assert hasattr(CPEMapping, 'component_id')
        assert hasattr(CPEMapping, 'cpe_string')
        
        assert hasattr(User, '__tablename__')
        assert hasattr(User, 'id')
        assert hasattr(User, 'username')
        assert hasattr(User, 'email')
        
        assert hasattr(CVE, '__tablename__')
        assert hasattr(CVE, 'id')
        assert hasattr(CVE, 'cve_id')
        assert hasattr(CVE, 'description')
        
        # Test that Base metadata includes all tables
        table_names = [table.name for table in Base.metadata.tables.values()]
        expected_tables = ['applications', 'components', 'cpe_mappings', 'users', 'api_keys', 'cves', 'cve_cpe_applicability']
        
        for expected_table in expected_tables:
            assert expected_table in table_names, f"Table {expected_table} not found in metadata"
