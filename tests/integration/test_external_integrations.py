"""Tests for external integrations (NVD client and CVE ingestion)."""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, patch, MagicMock

from src.cve_feed_service.services.nvd_client import NVDAPIClient
from src.cve_feed_service.services.cve_ingestion_service import CVEIngestionService
from src.cve_feed_service.models.cve import CVE


@pytest.mark.integration
class TestExternalIntegrations:
    """Test external integrations with proper mocking."""

    @pytest.fixture
    def sample_nvd_response(self):
        """Sample NVD API response."""
        return {
            "resultsPerPage": 1,
            "startIndex": 0,
            "totalResults": 1,
            "vulnerabilities": [
                {
                    "cve": {
                        "id": "CVE-2023-EXTERNAL",
                        "published": "2023-01-01T10:00:00.000Z",
                        "lastModified": "2023-01-02T10:00:00.000Z",
                        "descriptions": [
                            {
                                "lang": "en",
                                "value": "External integration test CVE"
                            }
                        ],
                        "metrics": {
                            "cvssMetricV31": [
                                {
                                    "cvssData": {
                                        "baseScore": 7.5,
                                        "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N",
                                        "baseSeverity": "HIGH"
                                    }
                                }
                            ]
                        },
                        "weaknesses": [
                            {
                                "description": [
                                    {
                                        "lang": "en",
                                        "value": "CWE-79"
                                    }
                                ]
                            }
                        ],
                        "references": [
                            {
                                "url": "https://example.com/advisory",
                                "source": "example.com",
                                "tags": ["Vendor Advisory"]
                            }
                        ]
                    }
                }
            ]
        }

    def test_nvd_client_initialization(self):
        """Test NVD client can be initialized."""
        client = NVDAPIClient()
        
        assert client.base_url == "https://services.nvd.nist.gov/rest/json"
        assert client.rate_limit == 10
        assert client.timeout == 30
        assert client.client is not None

    async def test_nvd_client_context_manager(self):
        """Test NVD client as context manager."""
        async with NVDAPIClient() as client:
            assert client.client is not None
        
        # Client should be closed after context exit
        assert client.client.is_closed

    @patch('httpx.AsyncClient.get')
    async def test_nvd_client_get_cves_mocked(self, mock_get, sample_nvd_response):
        """Test NVD client CVE retrieval with proper mocking."""
        # Create a proper mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_nvd_response
        mock_response.raise_for_status.return_value = None
        
        # Make the get method return the mock response
        mock_get.return_value = mock_response
        
        async with NVDAPIClient() as client:
            result = await client.get_cves(results_per_page=1)
        
        assert result == sample_nvd_response
        assert result["totalResults"] == 1
        assert len(result["vulnerabilities"]) == 1
        
        # Verify the request was made
        mock_get.assert_called_once()

    @patch('httpx.AsyncClient.get')
    async def test_nvd_client_get_cve_by_id_mocked(self, mock_get, sample_nvd_response):
        """Test getting specific CVE by ID."""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = sample_nvd_response
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        async with NVDAPIClient() as client:
            result = await client.get_cve_by_id("CVE-2023-EXTERNAL")
        
        assert result is not None
        assert result["cve"]["id"] == "CVE-2023-EXTERNAL"

    @patch('httpx.AsyncClient.get')
    async def test_nvd_client_not_found(self, mock_get):
        """Test handling of CVE not found."""
        empty_response = {
            "resultsPerPage": 0,
            "startIndex": 0,
            "totalResults": 0,
            "vulnerabilities": []
        }
        
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = empty_response
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        async with NVDAPIClient() as client:
            result = await client.get_cve_by_id("CVE-9999-NONEXISTENT")
        
        assert result is None

    async def test_cve_ingestion_service_initialization(self, db_session):
        """Test CVE ingestion service initialization."""
        service = CVEIngestionService(db_session)
        assert service.db == db_session

    async def test_cve_ingestion_parse_cvss_data(self, db_session, sample_nvd_response):
        """Test CVSS data parsing."""
        service = CVEIngestionService(db_session)
        cve_data = sample_nvd_response["vulnerabilities"][0]["cve"]
        
        cvss_data = service._parse_cvss_data(cve_data)
        
        assert cvss_data["cvss_v3_score"] == 7.5
        assert cvss_data["cvss_v3_vector"] == "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N"
        assert cvss_data["cvss_v3_severity"] == "HIGH"

    async def test_cve_ingestion_parse_cwe_data(self, db_session, sample_nvd_response):
        """Test CWE data parsing."""
        service = CVEIngestionService(db_session)
        cve_data = sample_nvd_response["vulnerabilities"][0]["cve"]
        
        cwe_ids = service._parse_cwe_data(cve_data)
        
        assert cwe_ids == ["CWE-79"]

    async def test_cve_ingestion_parse_description(self, db_session, sample_nvd_response):
        """Test description parsing."""
        service = CVEIngestionService(db_session)
        cve_data = sample_nvd_response["vulnerabilities"][0]["cve"]
        
        description = service._parse_description(cve_data)
        
        assert description == "External integration test CVE"

    async def test_cve_ingestion_parse_dates(self, db_session, sample_nvd_response):
        """Test date parsing."""
        service = CVEIngestionService(db_session)
        cve_data = sample_nvd_response["vulnerabilities"][0]["cve"]
        
        published_date, last_modified_date = service._parse_dates(cve_data)
        
        assert published_date is not None
        assert last_modified_date is not None
        assert published_date.year == 2023
        assert published_date.month == 1
        assert published_date.day == 1

    async def test_cve_ingestion_create_cve(self, db_session, sample_nvd_response):
        """Test creating CVE from NVD data."""
        service = CVEIngestionService(db_session)
        cve_data = sample_nvd_response["vulnerabilities"][0]["cve"]
        
        cve = await service._create_or_update_cve(cve_data)
        
        assert cve.cve_id == "CVE-2023-EXTERNAL"
        assert cve.description == "External integration test CVE"
        assert cve.cvss_v3_score == 7.5
        assert cve.cvss_v3_severity == "HIGH"
        assert "CWE-79" in cve.cwe_ids

    async def test_cve_ingestion_update_existing_cve(self, db_session, sample_nvd_response):
        """Test updating existing CVE."""
        service = CVEIngestionService(db_session)
        cve_data = sample_nvd_response["vulnerabilities"][0]["cve"]
        
        # Create initial CVE
        cve = await service._create_or_update_cve(cve_data)
        await db_session.commit()
        
        # Update the data
        updated_data = cve_data.copy()
        updated_data["descriptions"][0]["value"] = "Updated external test CVE"
        updated_data["metrics"]["cvssMetricV31"][0]["cvssData"]["baseScore"] = 8.0
        
        # Update the CVE
        updated_cve = await service._create_or_update_cve(updated_data)
        
        assert updated_cve.cve_id == "CVE-2023-EXTERNAL"
        assert updated_cve.description == "Updated external test CVE"
        assert updated_cve.cvss_v3_score == 8.0

    async def test_cve_ingestion_single_ingest(self, db_session, sample_nvd_response):
        """Test ingesting a single CVE."""
        service = CVEIngestionService(db_session)
        cve_data = sample_nvd_response["vulnerabilities"][0]["cve"]

        cve = await service.ingest_cve(cve_data)

        # Store values before commit to avoid lazy loading issues
        cve_id = cve.cve_id
        description = cve.description

        await db_session.commit()

        assert cve_id == "CVE-2023-EXTERNAL"
        assert description == "External integration test CVE"

    async def test_cve_ingestion_bulk_ingest(self, db_session, sample_nvd_response):
        """Test bulk ingesting multiple CVEs."""
        service = CVEIngestionService(db_session)
        
        # Create multiple CVE data entries
        cve_list = []
        for i in range(3):
            cve_data = sample_nvd_response["vulnerabilities"][0]["cve"].copy()
            cve_data["id"] = f"CVE-2023-BULK-{i:03d}"
            cve_list.append(cve_data)
        
        processed_count = await service.bulk_ingest_cves(cve_list)
        
        assert processed_count == 3
        
        # Verify CVEs were created
        from sqlalchemy import select
        result = await db_session.execute(select(CVE))
        cves = result.scalars().all()
        
        cve_ids = [cve.cve_id for cve in cves]
        assert "CVE-2023-BULK-000" in cve_ids
        assert "CVE-2023-BULK-001" in cve_ids
        assert "CVE-2023-BULK-002" in cve_ids

    @patch('src.cve_feed_service.services.cve_ingestion_service.NVDAPIClient')
    async def test_cve_ingestion_bulk_import_integration(self, mock_nvd_client, db_session, sample_nvd_response):
        """Test bulk import integration with NVD client."""
        # Mock NVD client
        mock_client_instance = AsyncMock()
        mock_client_instance.get_cves_by_date_range.return_value = [
            {"cve": sample_nvd_response["vulnerabilities"][0]["cve"]}
        ]
        mock_nvd_client.return_value.__aenter__.return_value = mock_client_instance
        
        service = CVEIngestionService(db_session)
        
        processed_count = await service.perform_bulk_import(years=1)
        
        assert processed_count == 1
        
        # Verify the NVD client was called
        mock_client_instance.get_cves_by_date_range.assert_called_once()

    @patch('src.cve_feed_service.services.cve_ingestion_service.NVDAPIClient')
    async def test_cve_ingestion_incremental_update_integration(self, mock_nvd_client, db_session, sample_nvd_response):
        """Test incremental update integration with NVD client."""
        # Mock NVD client
        mock_client_instance = AsyncMock()
        mock_client_instance.get_cves_by_date_range.return_value = [
            {"cve": sample_nvd_response["vulnerabilities"][0]["cve"]}
        ]
        mock_nvd_client.return_value.__aenter__.return_value = mock_client_instance
        
        service = CVEIngestionService(db_session)
        
        processed_count = await service.perform_incremental_update(hours=24)
        
        assert processed_count == 1
        
        # Verify the NVD client was called with correct parameters
        mock_client_instance.get_cves_by_date_range.assert_called_once()
        call_args = mock_client_instance.get_cves_by_date_range.call_args
        assert call_args[1]["use_last_modified"] is True

    async def test_error_handling_missing_cve_id(self, db_session):
        """Test error handling for missing CVE ID."""
        service = CVEIngestionService(db_session)
        
        invalid_data = {"description": "Missing ID"}
        
        with pytest.raises(ValueError, match="CVE data missing ID"):
            await service._create_or_update_cve(invalid_data)

    async def test_error_handling_bulk_ingest_with_errors(self, db_session):
        """Test bulk ingestion with some invalid data."""
        service = CVEIngestionService(db_session)
        
        # Mix of valid and invalid data
        cve_list = [
            {"id": "CVE-2023-VALID", "descriptions": [{"lang": "en", "value": "Valid CVE"}]},
            {"invalid": "data"},  # Missing required fields
            {"id": "CVE-2023-VALID-2", "descriptions": [{"lang": "en", "value": "Another valid CVE"}]}
        ]
        
        processed_count = await service.bulk_ingest_cves(cve_list)
        
        # Should process only the valid ones
        assert processed_count == 2

    async def test_date_parsing_error_handling(self, db_session):
        """Test handling of invalid date formats."""
        service = CVEIngestionService(db_session)
        
        cve_data = {
            "published": "invalid-date",
            "lastModified": "also-invalid"
        }
        
        published_date, last_modified_date = service._parse_dates(cve_data)
        
        # Should handle errors gracefully
        assert published_date is None
        assert last_modified_date is None
