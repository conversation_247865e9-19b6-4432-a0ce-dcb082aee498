"""Integration tests for Application model and service only."""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.pool import StaticPool
from sqlalchemy import MetaData, Table, Column, String, Text, DateTime, UUID
from sqlalchemy.sql import func
from uuid import uuid4
from datetime import datetime

from src.cve_feed_service.services.application_service import ApplicationService
from src.cve_feed_service.schemas.application import ApplicationCreate, ApplicationUpdate


@pytest.mark.integration
class TestApplicationOnly:
    """Test Application functionality without complex model relationships."""

    async def test_application_service_with_manual_table(self):
        """Test ApplicationService with manually created table."""
        # Create in-memory SQLite database
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        # Manually create applications table to avoid model relationship issues
        metadata = MetaData()
        applications_table = Table(
            'applications',
            metadata,
            Column('id', String, primary_key=True),
            Column('name', String(255), nullable=False),
            <PERSON>umn('description', Text),
            Column('version', String(100)),
            Column('owner', String(255)),
            Column('environment', String(50)),
            Column('criticality', String(20)),
            Column('created_at', DateTime, default=func.now(), nullable=False),
            Column('updated_at', DateTime, default=func.now(), nullable=False),
            Column('deleted_at', DateTime),
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(metadata.create_all)
        
        async with AsyncSession(engine) as session:
            app_service = ApplicationService(session)
            
            # CREATE
            app_data = ApplicationCreate(
                name="Manual Table Test",
                description="Testing with manual table",
                environment="test",
                criticality="medium",
                owner="Test Team"
            )
            created_app = await app_service.create_application(app_data)
            
            assert created_app.name == "Manual Table Test"
            assert created_app.description == "Testing with manual table"
            assert created_app.environment == "test"
            assert created_app.criticality == "medium"
            assert created_app.owner == "Test Team"
            assert created_app.id is not None
            
            # READ
            retrieved_app = await app_service.get_application(created_app.id)
            assert retrieved_app is not None
            assert retrieved_app.id == created_app.id
            assert retrieved_app.name == created_app.name
            
            # LIST
            apps = await app_service.list_applications()
            assert len(apps) == 1
            assert apps[0].name == "Manual Table Test"
            
            # UPDATE
            update_data = ApplicationUpdate(
                description="Updated description",
                criticality="high"
            )
            updated_app = await app_service.update_application(created_app.id, update_data)
            
            assert updated_app is not None
            assert updated_app.description == "Updated description"
            assert updated_app.criticality == "high"
            assert updated_app.name == "Manual Table Test"  # Unchanged
            
            # DELETE
            delete_result = await app_service.delete_application(created_app.id)
            assert delete_result is True
            
            # Verify deletion (soft delete)
            deleted_app = await app_service.get_application(created_app.id)
            assert deleted_app is None
        
        await engine.dispose()

    def test_application_schemas_work(self):
        """Test that application schemas work correctly."""
        # Test ApplicationCreate
        app_data = {
            "name": "Schema Test App",
            "description": "Testing schemas",
            "environment": "test",
            "criticality": "low",
            "owner": "Schema Team"
        }
        app_create = ApplicationCreate(**app_data)
        
        assert app_create.name == "Schema Test App"
        assert app_create.description == "Testing schemas"
        assert app_create.environment == "test"
        assert app_create.criticality == "low"
        assert app_create.owner == "Schema Team"
        
        # Test ApplicationUpdate
        update_data = {
            "description": "Updated description",
            "criticality": "high"
        }
        app_update = ApplicationUpdate(**update_data)
        
        assert app_update.description == "Updated description"
        assert app_update.criticality == "high"
        assert app_update.name is None  # Not provided
        assert app_update.environment is None  # Not provided
        
        # Test validation
        with pytest.raises(ValueError):
            ApplicationCreate(name="")  # Empty name should fail
        
        with pytest.raises(ValueError):
            ApplicationCreate(name="x" * 300)  # Too long name should fail

    def test_application_service_import(self):
        """Test that ApplicationService can be imported and instantiated."""
        from src.cve_feed_service.services.application_service import ApplicationService
        
        # Mock session for testing
        class MockSession:
            pass
        
        mock_session = MockSession()
        service = ApplicationService(mock_session)
        
        # Verify service has expected methods
        assert hasattr(service, 'create_application')
        assert hasattr(service, 'get_application')
        assert hasattr(service, 'list_applications')
        assert hasattr(service, 'update_application')
        assert hasattr(service, 'delete_application')
        
        # Verify service stores session
        assert service.db == mock_session

    async def test_application_service_error_handling(self):
        """Test error handling in ApplicationService."""
        # Create in-memory SQLite database
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        # Create applications table
        metadata = MetaData()
        applications_table = Table(
            'applications',
            metadata,
            Column('id', String, primary_key=True),
            Column('name', String(255), nullable=False),
            Column('description', Text),
            Column('version', String(100)),
            Column('owner', String(255)),
            Column('environment', String(50)),
            Column('criticality', String(20)),
            Column('created_at', DateTime, default=func.now(), nullable=False),
            Column('updated_at', DateTime, default=func.now(), nullable=False),
            Column('deleted_at', DateTime),
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(metadata.create_all)
        
        async with AsyncSession(engine) as session:
            app_service = ApplicationService(session)
            
            # Test getting non-existent application
            non_existent_id = uuid4()
            result = await app_service.get_application(non_existent_id)
            assert result is None
            
            # Test deleting non-existent application
            delete_result = await app_service.delete_application(non_existent_id)
            assert delete_result is False
            
            # Test updating non-existent application
            update_data = ApplicationUpdate(description="Won't work")
            updated_app = await app_service.update_application(non_existent_id, update_data)
            assert updated_app is None
        
        await engine.dispose()

    async def test_application_service_pagination(self):
        """Test pagination in ApplicationService."""
        # Create in-memory SQLite database
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        # Create applications table
        metadata = MetaData()
        applications_table = Table(
            'applications',
            metadata,
            Column('id', String, primary_key=True),
            Column('name', String(255), nullable=False),
            Column('description', Text),
            Column('version', String(100)),
            Column('owner', String(255)),
            Column('environment', String(50)),
            Column('criticality', String(20)),
            Column('created_at', DateTime, default=func.now(), nullable=False),
            Column('updated_at', DateTime, default=func.now(), nullable=False),
            Column('deleted_at', DateTime),
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(metadata.create_all)
        
        async with AsyncSession(engine) as session:
            app_service = ApplicationService(session)
            
            # Create multiple applications
            for i in range(10):
                app_data = ApplicationCreate(
                    name=f"App {i:02d}",
                    environment="test",
                    criticality="low"
                )
                await app_service.create_application(app_data)
            
            # Test pagination
            page1 = await app_service.list_applications(skip=0, limit=3)
            assert len(page1) == 3
            
            page2 = await app_service.list_applications(skip=3, limit=3)
            assert len(page2) == 3
            
            page3 = await app_service.list_applications(skip=6, limit=3)
            assert len(page3) == 3
            
            page4 = await app_service.list_applications(skip=9, limit=3)
            assert len(page4) == 1
            
            # Verify no duplicates
            all_ids = set()
            for page in [page1, page2, page3, page4]:
                page_ids = {app.id for app in page}
                assert len(all_ids.intersection(page_ids)) == 0
                all_ids.update(page_ids)
            
            assert len(all_ids) == 10
        
        await engine.dispose()

    def test_basic_imports_work(self):
        """Test that basic imports work without model relationship issues."""
        # Test schema imports
        from src.cve_feed_service.schemas.application import ApplicationCreate, ApplicationUpdate, ApplicationResponse
        
        # Test service import
        from src.cve_feed_service.services.application_service import ApplicationService
        
        # Test core imports
        from src.cve_feed_service.core.config import Settings
        
        # If we get here, imports work
        assert True
