"""Comprehensive API endpoint integration tests."""

import pytest
from uuid import uuid4
from httpx import AsyncClient

from src.cve_feed_service.main import app
from src.cve_feed_service.models.user import UserRole


@pytest.mark.integration
class TestApplicationEndpoints:
    """Test application management endpoints."""

    async def test_create_application_success(self, async_client: AsyncClient):
        """Test successful application creation."""
        app_data = {
            "name": "API Test App",
            "description": "Testing API endpoints",
            "environment": "test",
            "criticality": "medium",
            "owner": "API Test Team"
        }
        
        response = await async_client.post("/api/v1/applications/", json=app_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "API Test App"
        assert data["environment"] == "test"
        assert data["criticality"] == "medium"
        assert "id" in data
        assert "created_at" in data
        assert "updated_at" in data

    async def test_create_application_validation_error(self, async_client: AsyncClient):
        """Test application creation with validation errors."""
        # Missing required fields
        app_data = {
            "description": "Missing name and environment"
        }
        
        response = await async_client.post("/api/v1/applications/", json=app_data)
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data

    async def test_create_application_duplicate_error(self, async_client: AsyncClient):
        """Test application creation with duplicate name in same environment."""
        app_data = {
            "name": "Duplicate Test App",
            "environment": "production",
            "description": "First app"
        }
        
        # Create first application
        response1 = await async_client.post("/api/v1/applications/", json=app_data)
        assert response1.status_code == 201
        
        # Try to create duplicate
        response2 = await async_client.post("/api/v1/applications/", json=app_data)
        assert response2.status_code == 400
        data = response2.json()
        assert "already exists" in data["detail"]

    async def test_list_applications(self, async_client: AsyncClient):
        """Test listing applications."""
        # Create test applications
        for i in range(3):
            app_data = {
                "name": f"List Test App {i}",
                "environment": "test" if i % 2 == 0 else "prod",
                "description": f"Test app {i}"
            }
            response = await async_client.post("/api/v1/applications/", json=app_data)
            assert response.status_code == 201
        
        # Test basic listing
        response = await async_client.get("/api/v1/applications/")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 3
        
        # Test pagination
        response = await async_client.get("/api/v1/applications/?skip=0&limit=2")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        
        # Test environment filtering
        response = await async_client.get("/api/v1/applications/?environment=test")
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 2  # At least 2 test environment apps
        for app in data:
            assert app["environment"] == "test"

    async def test_get_application_by_id(self, async_client: AsyncClient):
        """Test getting application by ID."""
        # Create test application
        app_data = {
            "name": "Get Test App",
            "environment": "test",
            "description": "Testing get endpoint"
        }
        
        create_response = await async_client.post("/api/v1/applications/", json=app_data)
        assert create_response.status_code == 201
        created_app = create_response.json()
        app_id = created_app["id"]
        
        # Get application by ID
        response = await async_client.get(f"/api/v1/applications/{app_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == app_id
        assert data["name"] == "Get Test App"
        assert data["environment"] == "test"

    async def test_get_application_not_found(self, async_client: AsyncClient):
        """Test getting non-existent application."""
        non_existent_id = str(uuid4())
        response = await async_client.get(f"/api/v1/applications/{non_existent_id}")
        
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"]

    async def test_update_application(self, async_client: AsyncClient):
        """Test updating application."""
        # Create test application
        app_data = {
            "name": "Update Test App",
            "environment": "test",
            "description": "Original description",
            "criticality": "low"
        }
        
        create_response = await async_client.post("/api/v1/applications/", json=app_data)
        assert create_response.status_code == 201
        created_app = create_response.json()
        app_id = created_app["id"]
        
        # Update application
        update_data = {
            "description": "Updated description",
            "criticality": "high"
        }
        
        response = await async_client.patch(f"/api/v1/applications/{app_id}", json=update_data)
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == app_id
        assert data["name"] == "Update Test App"  # Unchanged
        assert data["description"] == "Updated description"
        assert data["criticality"] == "high"

    async def test_update_application_not_found(self, async_client: AsyncClient):
        """Test updating non-existent application."""
        non_existent_id = str(uuid4())
        update_data = {"description": "Won't work"}
        
        response = await async_client.patch(f"/api/v1/applications/{non_existent_id}", json=update_data)
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"]

    async def test_delete_application(self, async_client: AsyncClient):
        """Test deleting application."""
        # Create test application
        app_data = {
            "name": "Delete Test App",
            "environment": "test",
            "description": "Will be deleted"
        }
        
        create_response = await async_client.post("/api/v1/applications/", json=app_data)
        assert create_response.status_code == 201
        created_app = create_response.json()
        app_id = created_app["id"]
        
        # Delete application
        response = await async_client.delete(f"/api/v1/applications/{app_id}")
        assert response.status_code == 204
        
        # Verify deletion
        get_response = await async_client.get(f"/api/v1/applications/{app_id}")
        assert get_response.status_code == 404

    async def test_delete_application_not_found(self, async_client: AsyncClient):
        """Test deleting non-existent application."""
        non_existent_id = str(uuid4())
        response = await async_client.delete(f"/api/v1/applications/{non_existent_id}")
        
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"]


@pytest.mark.integration
class TestCVEEndpoints:
    """Test CVE management endpoints."""

    async def test_list_cves(self, async_client: AsyncClient):
        """Test listing CVEs."""
        response = await async_client.get("/api/v1/cves/")
        
        assert response.status_code == 200
        data = response.json()
        assert "cves" in data
        assert "total" in data
        assert "limit" in data
        assert "offset" in data
        assert "has_more" in data
        assert isinstance(data["cves"], list)

    async def test_list_cves_with_pagination(self, async_client: AsyncClient):
        """Test CVE listing with pagination."""
        response = await async_client.get("/api/v1/cves/?limit=5&offset=0")
        
        assert response.status_code == 200
        data = response.json()
        assert data["limit"] == 5
        assert data["offset"] == 0
        assert len(data["cves"]) <= 5

    async def test_list_cves_with_filters(self, async_client: AsyncClient):
        """Test CVE listing with filters."""
        # Test severity filter
        response = await async_client.get("/api/v1/cves/?severity=HIGH")
        assert response.status_code == 200
        
        # Test date filters
        response = await async_client.get("/api/v1/cves/?published_after=2023-01-01")
        assert response.status_code == 200

    async def test_get_cve_by_id(self, async_client: AsyncClient):
        """Test getting CVE by ID."""
        # This will return 404 since we don't have test data
        response = await async_client.get("/api/v1/cves/CVE-2023-NONEXISTENT")
        assert response.status_code == 404

    async def test_get_tailored_feed(self, async_client: AsyncClient):
        """Test getting tailored CVE feed."""
        response = await async_client.get("/api/v1/cves/feed")
        
        assert response.status_code == 200
        data = response.json()
        assert "cves" in data
        assert "total" in data
        assert isinstance(data["cves"], list)

    async def test_get_tailored_feed_with_application(self, async_client: AsyncClient):
        """Test getting tailored CVE feed for specific application."""
        # Create test application first
        app_data = {
            "name": "CVE Feed Test App",
            "environment": "test",
            "description": "For testing CVE feed"
        }
        
        create_response = await async_client.post("/api/v1/applications/", json=app_data)
        assert create_response.status_code == 201
        app_id = create_response.json()["id"]
        
        # Get tailored feed for this application
        response = await async_client.get(f"/api/v1/cves/feed?application_id={app_id}")
        assert response.status_code == 200
        data = response.json()
        assert "cves" in data
        assert "total" in data


@pytest.mark.integration
class TestAPIValidationAndErrorHandling:
    """Test API validation and error handling."""

    async def test_invalid_uuid_format(self, async_client: AsyncClient):
        """Test endpoints with invalid UUID format."""
        response = await async_client.get("/api/v1/applications/invalid-uuid")
        assert response.status_code == 422

    async def test_invalid_query_parameters(self, async_client: AsyncClient):
        """Test endpoints with invalid query parameters."""
        # Negative skip
        response = await async_client.get("/api/v1/applications/?skip=-1")
        assert response.status_code == 422
        
        # Limit too high
        response = await async_client.get("/api/v1/applications/?limit=2000")
        assert response.status_code == 422

    async def test_malformed_json(self, async_client: AsyncClient):
        """Test endpoints with malformed JSON."""
        response = await async_client.post(
            "/api/v1/applications/",
            content="invalid json",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422

    async def test_missing_content_type(self, async_client: AsyncClient):
        """Test POST endpoints without proper content type."""
        response = await async_client.post(
            "/api/v1/applications/",
            content='{"name": "test"}',
            headers={"Content-Type": "text/plain"}
        )
        assert response.status_code == 422

    async def test_empty_request_body(self, async_client: AsyncClient):
        """Test POST endpoints with empty request body."""
        response = await async_client.post("/api/v1/applications/", json={})
        assert response.status_code == 422


@pytest.mark.integration
class TestAPIResponseFormats:
    """Test API response formats and structure."""

    async def test_application_response_structure(self, async_client: AsyncClient):
        """Test application response has correct structure."""
        app_data = {
            "name": "Structure Test App",
            "environment": "test",
            "description": "Testing response structure"
        }
        
        response = await async_client.post("/api/v1/applications/", json=app_data)
        assert response.status_code == 201
        
        data = response.json()
        required_fields = ["id", "name", "environment", "description", "created_at", "updated_at"]
        for field in required_fields:
            assert field in data, f"Missing required field: {field}"
        
        # Check data types
        assert isinstance(data["id"], str)
        assert isinstance(data["name"], str)
        assert isinstance(data["environment"], str)

    async def test_cve_feed_response_structure(self, async_client: AsyncClient):
        """Test CVE feed response has correct structure."""
        response = await async_client.get("/api/v1/cves/feed")
        assert response.status_code == 200
        
        data = response.json()
        required_fields = ["cves", "total", "limit", "offset", "has_more"]
        for field in required_fields:
            assert field in data, f"Missing required field: {field}"
        
        # Check data types
        assert isinstance(data["cves"], list)
        assert isinstance(data["total"], int)
        assert isinstance(data["limit"], int)
        assert isinstance(data["offset"], int)
        assert isinstance(data["has_more"], bool)

    async def test_error_response_structure(self, async_client: AsyncClient):
        """Test error responses have correct structure."""
        response = await async_client.get("/api/v1/applications/invalid-uuid")
        assert response.status_code == 422
        
        data = response.json()
        assert "detail" in data
        
        # For 404 errors
        non_existent_id = str(uuid4())
        response = await async_client.get(f"/api/v1/applications/{non_existent_id}")
        assert response.status_code == 404
        
        data = response.json()
        assert "detail" in data
        assert isinstance(data["detail"], str)
