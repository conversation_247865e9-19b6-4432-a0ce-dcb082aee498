"""Integration tests for services with real database."""

import pytest
from uuid import uuid4
from datetime import datetime

from src.cve_feed_service.services.auth_service import AuthService
from src.cve_feed_service.services.cve_service import CVEService
from src.cve_feed_service.services.application_service import ApplicationService
from src.cve_feed_service.schemas.auth import UserCreate
from src.cve_feed_service.schemas.cve import CVECreate
from src.cve_feed_service.schemas.application import ApplicationCreate


@pytest.mark.integration
class TestServiceIntegration:
    """Integration tests for services with real database."""

    @pytest.mark.asyncio
    async def test_auth_service_create_user_integration(self, db_session):
        """Test auth service user creation with real database."""
        # Arrange
        service = AuthService(db_session)
        unique_id = str(uuid4())[:8]
        user_data = UserCreate(
            username=f"testuser_{unique_id}",
            email=f"test_{unique_id}@example.com",
            password="testpassword123",
            full_name="Test User",
            role="security_analyst",
            is_active=True
        )

        # Act
        result = await service.create_user(user_data)
        
        # Assert before commit to avoid object expiry
        assert result is not None
        username = result.username  # Store before commit
        email = result.email
        user_id = result.id
        
        await db_session.commit()
        
        # Verify the user was created by querying again
        found_user = await service.get_user_by_username(username)
        assert found_user is not None
        assert found_user.email == email
        assert found_user.id == user_id

    @pytest.mark.asyncio
    async def test_auth_service_authenticate_user_integration(self, db_session):
        """Test auth service user authentication with real database."""
        # Arrange
        service = AuthService(db_session)
        unique_id = str(uuid4())[:8]
        username = f"authuser_{unique_id}"
        password = "testpassword123"
        
        user_data = UserCreate(
            username=username,
            email=f"auth_{unique_id}@example.com",
            password=password,
            full_name="Auth Test User",
            role="security_analyst"
        )

        # Create user first
        created_user = await service.create_user(user_data)
        assert created_user is not None
        await db_session.commit()

        # Act - authenticate with correct credentials
        auth_result = await service.authenticate_user(username, password)
        
        # Assert
        assert auth_result is not None
        assert auth_result.username == username

        # Test with wrong password
        wrong_auth = await service.authenticate_user(username, "wrongpassword")
        assert wrong_auth is None

    @pytest.mark.asyncio
    async def test_cve_service_create_cve_integration(self, db_session):
        """Test CVE service CVE creation with real database."""
        # Arrange
        service = CVEService(db_session)
        unique_id = str(uuid4())[:8]
        cve_data = CVECreate(
            cve_id=f"CVE-2024-{unique_id}",
            description=f"Test CVE description {unique_id}",
            published_date=datetime.utcnow(),
            last_modified_date=datetime.utcnow(),
            cvss_v3_score=7.5,
            cvss_v3_vector="CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N",
            cvss_v3_severity="HIGH"
        )

        # Act
        result = await service.create_cve(cve_data)
        
        # Assert before commit
        assert result is not None
        cve_id = result.cve_id  # Store before commit
        description = result.description
        
        await db_session.commit()
        
        # Verify by querying again
        found_cve = await service.get_cve_by_id(cve_id)
        assert found_cve is not None
        assert found_cve.description == description

    @pytest.mark.asyncio
    async def test_cve_service_list_cves_integration(self, db_session):
        """Test CVE service list CVEs with real database."""
        # Arrange
        service = CVEService(db_session)
        
        # Create multiple CVEs
        cve_ids = []
        for i in range(3):
            unique_id = str(uuid4())[:8]
            cve_data = CVECreate(
                cve_id=f"CVE-2024-{unique_id}",
                description=f"Test CVE {i} description",
                published_date=datetime.utcnow(),
                cvss_v3_score=5.0 + i,
                cvss_v3_severity="MEDIUM"
            )
            cve = await service.create_cve(cve_data)
            cve_ids.append(cve.cve_id)
        
        await db_session.commit()

        # Act
        cves, total = await service.list_cves(limit=10)
        
        # Assert
        assert isinstance(cves, list)
        assert isinstance(total, int)
        assert total >= 3  # At least our 3 CVEs
        
        # Check that our CVEs are in the results
        found_cve_ids = [cve.cve_id for cve in cves]
        for cve_id in cve_ids:
            assert cve_id in found_cve_ids

    @pytest.mark.asyncio
    async def test_application_service_create_application_integration(self, db_session):
        """Test application service application creation with real database."""
        # Arrange
        service = ApplicationService(db_session)
        unique_id = str(uuid4())[:8]
        app_data = ApplicationCreate(
            name=f"Test Application {unique_id}",
            description=f"Test application for integration testing {unique_id}",
            environment="test",
            criticality="medium",
            owner="Integration Test Team"
        )

        # Act
        result = await service.create_application(app_data)
        
        # Assert before commit
        assert result is not None
        app_name = result.name  # Store before commit
        app_id = result.id
        
        await db_session.commit()
        
        # Verify by querying again
        found_app = await service.get_application(app_id)
        assert found_app is not None
        assert found_app.name == app_name

    @pytest.mark.asyncio
    async def test_application_service_list_applications_integration(self, db_session):
        """Test application service list applications with real database."""
        # Arrange
        service = ApplicationService(db_session)
        
        # Create multiple applications
        app_names = []
        for i in range(3):
            unique_id = str(uuid4())[:8]
            app_data = ApplicationCreate(
                name=f"Integration App {i} {unique_id}",
                description=f"Integration test application {i}",
                environment="test" if i % 2 == 0 else "production",
                criticality="medium"
            )
            app = await service.create_application(app_data)
            app_names.append(app.name)
        
        await db_session.commit()

        # Act
        apps = await service.list_applications()
        
        # Assert
        assert isinstance(apps, list)
        assert len(apps) >= 3  # At least our 3 applications
        
        # Check that our applications are in the results
        found_app_names = [app.name for app in apps]
        for app_name in app_names:
            assert app_name in found_app_names

    @pytest.mark.asyncio
    async def test_cve_service_update_and_delete_integration(self, db_session):
        """Test CVE service update and delete operations with real database."""
        # Arrange
        service = CVEService(db_session)
        unique_id = str(uuid4())[:8]
        cve_data = CVECreate(
            cve_id=f"CVE-2024-{unique_id}",
            description="Original description",
            published_date=datetime.utcnow(),
            cvss_v3_score=5.0,
            cvss_v3_severity="MEDIUM"
        )

        # Create CVE
        created_cve = await service.create_cve(cve_data)
        cve_id = created_cve.cve_id
        await db_session.commit()

        # Act - Update CVE
        update_data = {
            "description": "Updated description",
            "cvss_v3_score": 8.0,
            "cvss_v3_severity": "HIGH"
        }
        updated_cve = await service.update_cve(cve_id, update_data)
        assert updated_cve is not None
        await db_session.commit()

        # Verify update
        found_cve = await service.get_cve_by_id(cve_id)
        assert found_cve is not None
        assert found_cve.description == "Updated description"
        assert found_cve.cvss_v3_score == 8.0

        # Act - Delete CVE
        delete_result = await service.delete_cve(cve_id)
        assert delete_result is True
        await db_session.commit()

        # Verify deletion (soft delete)
        deleted_cve = await service.get_cve_by_id(cve_id)
        assert deleted_cve is None  # Should not be found due to soft delete

    @pytest.mark.asyncio
    async def test_cross_service_integration(self, db_session):
        """Test integration between multiple services."""
        # Arrange
        auth_service = AuthService(db_session)
        app_service = ApplicationService(db_session)
        cve_service = CVEService(db_session)
        
        unique_id = str(uuid4())[:8]

        # Create a user
        user_data = UserCreate(
            username=f"crossuser_{unique_id}",
            email=f"cross_{unique_id}@example.com",
            password="testpassword123",
            full_name="Cross Service User",
            role="security_analyst"
        )
        user = await auth_service.create_user(user_data)
        user_id = user.id

        # Create an application
        app_data = ApplicationCreate(
            name=f"Cross Service App {unique_id}",
            description="Application for cross-service testing",
            environment="test",
            criticality="high"
        )
        app = await app_service.create_application(app_data)
        app_id = app.id

        # Create a CVE
        cve_data = CVECreate(
            cve_id=f"CVE-2024-{unique_id}",
            description="Cross-service test CVE",
            published_date=datetime.utcnow(),
            cvss_v3_score=9.0,
            cvss_v3_severity="CRITICAL"
        )
        cve = await cve_service.create_cve(cve_data)
        cve_id = cve.cve_id

        await db_session.commit()

        # Act & Assert - Verify all entities exist and can be retrieved
        found_user = await auth_service.get_user_by_username(f"crossuser_{unique_id}")
        assert found_user is not None
        assert found_user.id == user_id

        found_app = await app_service.get_application(app_id)
        assert found_app is not None
        assert found_app.criticality == "high"

        found_cve = await cve_service.get_cve_by_id(cve_id)
        assert found_cve is not None
        assert found_cve.cvss_v3_severity == "CRITICAL"
