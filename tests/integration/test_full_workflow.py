"""Integration tests for full application workflows."""

import pytest
from httpx import AsyncClient
from datetime import datetime, timedelta

from src.cve_feed_service.main import app


class TestFullWorkflow:
    """Test complete application workflows end-to-end."""

    @pytest.fixture
    async def authenticated_client(self, async_client):
        """Create an authenticated client with a test user."""
        # Create a test user
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "Test User"
        }
        
        response = await async_client.post("/api/v1/auth/users", json=user_data)
        assert response.status_code == 201
        
        # Login to get token
        login_data = {
            "username": "testuser",
            "password": "testpassword123"
        }
        
        response = await async_client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 200
        
        token_data = response.json()
        token = token_data["access_token"]
        
        # Set authorization header
        async_client.headers.update({"Authorization": f"Bearer {token}"})
        
        return async_client

    async def test_complete_application_management_workflow(self, authenticated_client):
        """Test complete application management workflow."""
        client = authenticated_client
        
        # 1. Create an application
        app_data = {
            "name": "Test Web Application",
            "description": "A test web application",
            "version": "1.0.0",
            "environment": "test",
            "criticality": "medium",
            "owner": "Test Team"
        }
        
        response = await client.post("/api/v1/applications/", json=app_data)
        assert response.status_code == 201
        
        app_response = response.json()
        app_id = app_response["id"]
        assert app_response["name"] == app_data["name"]
        
        # 2. List applications
        response = await client.get("/api/v1/applications/")
        assert response.status_code == 200
        
        apps = response.json()
        assert len(apps) >= 1
        assert any(app["id"] == app_id for app in apps)
        
        # 3. Get application details
        response = await client.get(f"/api/v1/applications/{app_id}")
        assert response.status_code == 200
        
        app_details = response.json()
        assert app_details["id"] == app_id
        assert app_details["name"] == app_data["name"]
        
        # 4. Update application
        update_data = {
            "description": "Updated test web application",
            "criticality": "high"
        }
        
        response = await client.put(f"/api/v1/applications/{app_id}", json=update_data)
        assert response.status_code == 200
        
        updated_app = response.json()
        assert updated_app["description"] == update_data["description"]
        assert updated_app["criticality"] == update_data["criticality"]
        
        # 5. Add components to application
        components_data = [
            {
                "name": "nginx",
                "version": "1.20.1",
                "vendor": "nginx",
                "component_type": "web_server",
                "description": "Web server component"
            },
            {
                "name": "nodejs",
                "version": "16.14.0",
                "vendor": "nodejs",
                "component_type": "runtime",
                "description": "JavaScript runtime"
            }
        ]
        
        component_ids = []
        for comp_data in components_data:
            response = await client.post(f"/api/v1/components/{app_id}/components", json=comp_data)
            assert response.status_code == 201
            
            component = response.json()
            component_ids.append(component["id"])
            assert component["name"] == comp_data["name"]
        
        # 6. List components for application
        response = await client.get(f"/api/v1/components/{app_id}/components")
        assert response.status_code == 200
        
        components = response.json()
        assert len(components) == 2
        
        # 7. Add CPE mappings to components
        for i, comp_id in enumerate(component_ids):
            cpe_data = {
                "cpe_string": f"cpe:2.3:a:{components_data[i]['vendor']}:{components_data[i]['name']}:{components_data[i]['version']}:*:*:*:*:*:*:*",
                "confidence": 1.0,
                "mapping_source": "manual"
            }
            
            response = await client.post(f"/api/v1/components/components/{comp_id}/cpe-mappings", json=cpe_data)
            assert response.status_code == 201
        
        # 8. Get CVE feed for application (should be empty initially)
        response = await client.get(f"/api/v1/cves/feed?application_id={app_id}")
        assert response.status_code == 200
        
        cve_feed = response.json()
        assert "cves" in cve_feed
        assert cve_feed["total"] >= 0
        
        # 9. Delete application
        response = await client.delete(f"/api/v1/applications/{app_id}")
        assert response.status_code == 200
        
        # 10. Verify application is deleted
        response = await client.get(f"/api/v1/applications/{app_id}")
        assert response.status_code == 404

    async def test_cve_management_workflow(self, authenticated_client):
        """Test CVE management workflow."""
        client = authenticated_client
        
        # 1. List CVEs (should work even if empty)
        response = await client.get("/api/v1/cves/")
        assert response.status_code == 200
        
        cves_response = response.json()
        assert "cves" in cves_response
        assert "total" in cves_response
        
        # 2. Get CVE feed with filters
        response = await client.get("/api/v1/cves/feed?severity=HIGH&limit=10")
        assert response.status_code == 200
        
        feed_response = response.json()
        assert "cves" in feed_response
        assert feed_response["limit"] == 10
        
        # 3. Search CVEs
        response = await client.get("/api/v1/cves/?search=nginx")
        assert response.status_code == 200

    async def test_user_management_workflow(self, authenticated_client):
        """Test user management workflow."""
        client = authenticated_client
        
        # 1. Get current user profile
        response = await client.get("/api/v1/auth/me")
        assert response.status_code == 200
        
        user_profile = response.json()
        assert user_profile["username"] == "testuser"
        assert user_profile["email"] == "<EMAIL>"
        
        # 2. Update user profile
        update_data = {
            "full_name": "Updated Test User"
        }
        
        response = await client.put(f"/api/v1/auth/users/{user_profile['id']}", json=update_data)
        assert response.status_code == 200
        
        updated_user = response.json()
        assert updated_user["full_name"] == update_data["full_name"]
        
        # 3. Change password
        password_data = {
            "old_password": "testpassword123",
            "new_password": "newpassword456"
        }
        
        response = await client.post("/api/v1/auth/change-password", json=password_data)
        assert response.status_code == 200
        
        # 4. Create API key
        api_key_data = {
            "name": "Test API Key",
            "description": "API key for testing",
            "expires_at": (datetime.utcnow() + timedelta(days=30)).isoformat()
        }
        
        response = await client.post("/api/v1/auth/api-keys", json=api_key_data)
        assert response.status_code == 201
        
        api_key_response = response.json()
        assert api_key_response["name"] == api_key_data["name"]
        assert "key" in api_key_response
        
        # 5. List API keys
        response = await client.get("/api/v1/auth/api-keys")
        assert response.status_code == 200
        
        api_keys = response.json()
        assert len(api_keys) >= 1
        assert any(key["name"] == api_key_data["name"] for key in api_keys)

    async def test_error_handling_workflow(self, authenticated_client):
        """Test error handling in various scenarios."""
        client = authenticated_client
        
        # 1. Try to access non-existent application
        response = await client.get("/api/v1/applications/00000000-0000-0000-0000-000000000000")
        assert response.status_code == 404
        
        # 2. Try to create application with invalid data
        invalid_app_data = {
            "name": "",  # Empty name should be invalid
            "environment": "invalid_env"
        }
        
        response = await client.post("/api/v1/applications/", json=invalid_app_data)
        assert response.status_code == 422  # Validation error
        
        # 3. Try to create duplicate application
        app_data = {
            "name": "Duplicate Test App",
            "environment": "test"
        }
        
        # Create first application
        response = await client.post("/api/v1/applications/", json=app_data)
        assert response.status_code == 201
        
        # Try to create duplicate
        response = await client.post("/api/v1/applications/", json=app_data)
        assert response.status_code == 400  # Conflict error
        
        # 4. Try to access protected endpoint without authentication
        client.headers.pop("Authorization", None)  # Remove auth header
        
        response = await client.get("/api/v1/applications/")
        assert response.status_code == 401  # Unauthorized

    async def test_pagination_and_filtering_workflow(self, authenticated_client):
        """Test pagination and filtering across endpoints."""
        client = authenticated_client
        
        # Create multiple applications for testing pagination
        app_ids = []
        for i in range(5):
            app_data = {
                "name": f"Test App {i}",
                "description": f"Test application {i}",
                "environment": "test" if i % 2 == 0 else "production",
                "criticality": "medium"
            }
            
            response = await client.post("/api/v1/applications/", json=app_data)
            assert response.status_code == 201
            
            app_response = response.json()
            app_ids.append(app_response["id"])
        
        # Test pagination
        response = await client.get("/api/v1/applications/?limit=2&skip=0")
        assert response.status_code == 200
        
        page1 = response.json()
        assert len(page1) == 2
        
        response = await client.get("/api/v1/applications/?limit=2&skip=2")
        assert response.status_code == 200
        
        page2 = response.json()
        assert len(page2) == 2
        
        # Test filtering by environment
        response = await client.get("/api/v1/applications/?environment=test")
        assert response.status_code == 200
        
        test_apps = response.json()
        assert all(app["environment"] == "test" for app in test_apps)
        
        response = await client.get("/api/v1/applications/?environment=production")
        assert response.status_code == 200
        
        prod_apps = response.json()
        assert all(app["environment"] == "production" for app in prod_apps)

    async def test_api_documentation_accessibility(self, async_client):
        """Test that API documentation is accessible."""
        # Test OpenAPI JSON
        response = await async_client.get("/api/v1/openapi.json")
        assert response.status_code == 200
        
        openapi_spec = response.json()
        assert "openapi" in openapi_spec
        assert "info" in openapi_spec
        assert "paths" in openapi_spec
        
        # Test Swagger UI
        response = await async_client.get("/api/v1/docs")
        assert response.status_code == 200
        
        # Test ReDoc
        response = await async_client.get("/api/v1/redoc")
        assert response.status_code == 200

    async def test_health_and_monitoring_endpoints(self, async_client):
        """Test health and monitoring endpoints."""
        # Test health endpoint
        response = await async_client.get("/health")
        assert response.status_code == 200
        
        health_data = response.json()
        assert health_data["status"] == "healthy"
        assert "version" in health_data
        
        # Test readiness endpoint
        response = await async_client.get("/ready")
        assert response.status_code == 200
        
        ready_data = response.json()
        assert ready_data["status"] == "ready"
        assert "database" in ready_data
        assert "timestamp" in ready_data
