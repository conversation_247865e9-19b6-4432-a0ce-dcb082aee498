"""Integration tests for database operations using actual SQLAlchemy models."""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.pool import StaticPool

from src.cve_feed_service.db.base import Base
from src.cve_feed_service.services.application_service import ApplicationService
from src.cve_feed_service.schemas.application import ApplicationCreate, ApplicationUpdate


@pytest.mark.integration
class TestDatabaseOperations:
    """Test database operations using actual SQLAlchemy models."""

    async def test_create_all_tables(self):
        """Test creating all tables using SQLAlchemy models."""
        # Create in-memory SQLite database
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        # Create all tables using the actual models
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        # Verify tables were created by checking metadata
        assert len(Base.metadata.tables) > 0
        assert "applications" in Base.metadata.tables
        assert "users" in Base.metadata.tables
        assert "api_keys" in Base.metadata.tables
        assert "cves" in Base.metadata.tables
        assert "cve_cpe_applicability" in Base.metadata.tables
        
        await engine.dispose()

    async def test_application_service_full_crud(self):
        """Test complete CRUD operations using ApplicationService."""
        # Create in-memory SQLite database
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        # Create all tables
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        async with AsyncSession(engine) as session:
            app_service = ApplicationService(session)
            
            # CREATE
            app_data = ApplicationCreate(
                name="Full CRUD Test",
                description="Testing complete CRUD operations",
                environment="test",
                criticality="medium",
                owner="Test Team"
            )
            created_app = await app_service.create_application(app_data)
            
            assert created_app.name == "Full CRUD Test"
            assert created_app.description == "Testing complete CRUD operations"
            assert created_app.environment == "test"
            assert created_app.criticality == "medium"
            assert created_app.owner == "Test Team"
            assert created_app.id is not None
            assert created_app.created_at is not None
            assert created_app.updated_at is not None
            
            # READ
            retrieved_app = await app_service.get_application(created_app.id)
            assert retrieved_app is not None
            assert retrieved_app.id == created_app.id
            assert retrieved_app.name == created_app.name
            
            # LIST
            apps = await app_service.list_applications()
            assert len(apps) == 1
            assert apps[0].name == "Full CRUD Test"
            
            # UPDATE
            update_data = ApplicationUpdate(
                description="Updated description",
                criticality="high"
            )
            updated_app = await app_service.update_application(created_app.id, update_data)
            
            assert updated_app is not None
            assert updated_app.description == "Updated description"
            assert updated_app.criticality == "high"
            assert updated_app.name == "Full CRUD Test"  # Unchanged
            # Note: updated_at should be different, but may be same due to test speed
            # The important thing is that the update worked
            
            # DELETE
            delete_result = await app_service.delete_application(created_app.id)
            assert delete_result is True
            
            # Verify deletion (soft delete)
            deleted_app = await app_service.get_application(created_app.id)
            assert deleted_app is None
            
            # Verify it doesn't appear in list
            apps_after_delete = await app_service.list_applications()
            assert len(apps_after_delete) == 0
        
        await engine.dispose()

    async def test_application_service_multiple_operations(self):
        """Test multiple application operations."""
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        async with AsyncSession(engine) as session:
            app_service = ApplicationService(session)
            
            # Create multiple applications
            apps_data = [
                ApplicationCreate(name="Prod App 1", environment="production", criticality="high"),
                ApplicationCreate(name="Prod App 2", environment="production", criticality="medium"),
                ApplicationCreate(name="Test App 1", environment="test", criticality="low"),
                ApplicationCreate(name="Test App 2", environment="test", criticality="high"),
                ApplicationCreate(name="Dev App 1", environment="development", criticality="low"),
            ]
            
            created_apps = []
            for app_data in apps_data:
                app = await app_service.create_application(app_data)
                created_apps.append(app)
            
            # Test listing all applications
            all_apps = await app_service.list_applications()
            assert len(all_apps) == 5
            
            # Test filtering by environment
            prod_apps = await app_service.list_applications(environment="production")
            assert len(prod_apps) == 2
            assert all(app.environment == "production" for app in prod_apps)
            
            test_apps = await app_service.list_applications(environment="test")
            assert len(test_apps) == 2
            assert all(app.environment == "test" for app in test_apps)
            
            dev_apps = await app_service.list_applications(environment="development")
            assert len(dev_apps) == 1
            assert dev_apps[0].environment == "development"
            
            # Test pagination
            page1 = await app_service.list_applications(skip=0, limit=2)
            assert len(page1) == 2
            
            page2 = await app_service.list_applications(skip=2, limit=2)
            assert len(page2) == 2
            
            page3 = await app_service.list_applications(skip=4, limit=2)
            assert len(page3) == 1
            
            # Verify no duplicates between pages
            page1_ids = {app.id for app in page1}
            page2_ids = {app.id for app in page2}
            page3_ids = {app.id for app in page3}
            
            assert len(page1_ids.intersection(page2_ids)) == 0
            assert len(page1_ids.intersection(page3_ids)) == 0
            assert len(page2_ids.intersection(page3_ids)) == 0
        
        await engine.dispose()

    async def test_application_duplicate_handling(self):
        """Test handling of duplicate application names."""
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        async with AsyncSession(engine) as session:
            app_service = ApplicationService(session)
            
            # Create first application
            app_data = ApplicationCreate(
                name="Duplicate Test",
                environment="production"
            )
            first_app = await app_service.create_application(app_data)
            assert first_app.name == "Duplicate Test"
            assert first_app.environment == "production"
            
            # Try to create duplicate in same environment (should fail)
            with pytest.raises(ValueError, match="already exists"):
                await app_service.create_application(app_data)
            
            # Create same name in different environment (should succeed)
            app_data_different_env = ApplicationCreate(
                name="Duplicate Test",
                environment="test"
            )
            second_app = await app_service.create_application(app_data_different_env)
            assert second_app.name == "Duplicate Test"
            assert second_app.environment == "test"
            assert second_app.id != first_app.id
            
            # Verify both exist
            all_apps = await app_service.list_applications()
            assert len(all_apps) == 2
            
            environments = {app.environment for app in all_apps}
            assert environments == {"production", "test"}
        
        await engine.dispose()

    async def test_application_soft_delete(self):
        """Test soft delete functionality."""
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        async with AsyncSession(engine) as session:
            app_service = ApplicationService(session)
            
            # Create application
            app_data = ApplicationCreate(
                name="Soft Delete Test",
                environment="test"
            )
            created_app = await app_service.create_application(app_data)
            app_id = created_app.id
            
            # Verify it exists
            retrieved_app = await app_service.get_application(app_id)
            assert retrieved_app is not None
            
            # Delete it
            delete_result = await app_service.delete_application(app_id)
            assert delete_result is True
            
            # Verify it's gone from normal queries
            deleted_app = await app_service.get_application(app_id)
            assert deleted_app is None
            
            # Verify it doesn't appear in list
            apps = await app_service.list_applications()
            assert len(apps) == 0
            
            # Create another app with same name (should work since first is soft-deleted)
            new_app_data = ApplicationCreate(
                name="Soft Delete Test",
                environment="test"
            )
            new_app = await app_service.create_application(new_app_data)
            assert new_app.name == "Soft Delete Test"
            assert new_app.id != app_id  # Different ID
        
        await engine.dispose()

    async def test_application_error_handling(self):
        """Test error handling in ApplicationService."""
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        async with AsyncSession(engine) as session:
            app_service = ApplicationService(session)
            
            # Test getting non-existent application
            from uuid import uuid4
            non_existent_id = uuid4()
            result = await app_service.get_application(non_existent_id)
            assert result is None
            
            # Test deleting non-existent application
            delete_result = await app_service.delete_application(non_existent_id)
            assert delete_result is False
            
            # Test updating non-existent application
            update_data = ApplicationUpdate(description="Won't work")
            updated_app = await app_service.update_application(non_existent_id, update_data)
            assert updated_app is None
        
        await engine.dispose()

    async def test_database_relationships_work(self):
        """Test that database relationships are properly configured."""
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
        )
        
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        # Test that we can create instances of all models
        from src.cve_feed_service.models.application import Application
        from src.cve_feed_service.models.user import User, APIKey, UserRole
        from src.cve_feed_service.models.cve import CVE, CVECPEApplicability
        from uuid import uuid4
        from datetime import datetime
        
        async with AsyncSession(engine) as session:
            # Create a user
            user = User(
                id=uuid4(),
                username="testuser",
                email="<EMAIL>",
                full_name="Test User",
                hashed_password="hashed_password",
                role=UserRole.IT_ADMIN,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            session.add(user)
            await session.commit()
            await session.refresh(user)
            
            # Create an API key for the user
            api_key = APIKey(
                id=uuid4(),
                user_id=str(user.id),
                name="Test API Key",
                key_hash="hashed_key",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            session.add(api_key)
            await session.commit()
            await session.refresh(api_key)
            
            # Create a CVE
            cve = CVE(
                id=uuid4(),
                cve_id="CVE-2023-0001",
                description="Test CVE",
                published_date=datetime.utcnow(),
                last_modified_date=datetime.utcnow(),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            session.add(cve)
            await session.commit()
            await session.refresh(cve)
            
            # Create CVE CPE applicability
            cve_cpe = CVECPEApplicability(
                id=uuid4(),
                cve_id=cve.cve_id,
                cpe_string="cpe:2.3:a:test:test:1.0:*:*:*:*:*:*:*",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            session.add(cve_cpe)
            await session.commit()
            await session.refresh(cve_cpe)
            
            # Verify relationships work
            assert user.id is not None
            assert api_key.user_id == str(user.id)
            assert cve.cve_id == "CVE-2023-0001"
            assert cve_cpe.cve_id == cve.cve_id
        
        await engine.dispose()
