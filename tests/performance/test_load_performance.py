"""Performance and load tests for CVE Feed Service."""

import pytest
import asyncio
import time
from httpx import AsyncClient
from concurrent.futures import ThreadPoolExecutor
from statistics import mean, median

from src.cve_feed_service.main import app


class TestPerformance:
    """Performance and load testing for the CVE Feed Service."""

    @pytest.fixture
    async def authenticated_client(self, async_client):
        """Create an authenticated client for performance tests."""
        # Create test user
        user_data = {
            "username": "perftest",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "Performance Test User"
        }
        
        response = await async_client.post("/api/v1/auth/users", json=user_data)
        assert response.status_code == 201
        
        # Login
        login_data = {
            "username": "perftest",
            "password": "testpassword123"
        }
        
        response = await async_client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 200
        
        token_data = response.json()
        token = token_data["access_token"]
        
        async_client.headers.update({"Authorization": f"Bearer {token}"})
        return async_client

    async def test_health_endpoint_performance(self, async_client):
        """Test health endpoint response time."""
        response_times = []
        
        for _ in range(100):
            start_time = time.time()
            response = await async_client.get("/health")
            end_time = time.time()
            
            assert response.status_code == 200
            response_times.append(end_time - start_time)
        
        avg_response_time = mean(response_times)
        median_response_time = median(response_times)
        max_response_time = max(response_times)
        
        print(f"\nHealth Endpoint Performance:")
        print(f"Average response time: {avg_response_time:.3f}s")
        print(f"Median response time: {median_response_time:.3f}s")
        print(f"Max response time: {max_response_time:.3f}s")
        
        # Assert performance requirements
        assert avg_response_time < 0.1  # Average should be under 100ms
        assert max_response_time < 0.5  # Max should be under 500ms

    async def test_authentication_performance(self, async_client):
        """Test authentication endpoint performance."""
        # Create test user first
        user_data = {
            "username": "authperftest",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "Auth Performance Test"
        }
        
        response = await async_client.post("/api/v1/auth/users", json=user_data)
        assert response.status_code == 201
        
        login_data = {
            "username": "authperftest",
            "password": "testpassword123"
        }
        
        response_times = []
        
        for _ in range(50):
            start_time = time.time()
            response = await async_client.post("/api/v1/auth/login", data=login_data)
            end_time = time.time()
            
            assert response.status_code == 200
            response_times.append(end_time - start_time)
        
        avg_response_time = mean(response_times)
        median_response_time = median(response_times)
        
        print(f"\nAuthentication Performance:")
        print(f"Average response time: {avg_response_time:.3f}s")
        print(f"Median response time: {median_response_time:.3f}s")
        
        # Authentication should be reasonably fast
        assert avg_response_time < 0.5  # Average should be under 500ms

    async def test_application_crud_performance(self, authenticated_client):
        """Test application CRUD operations performance."""
        client = authenticated_client
        
        # Test application creation performance
        create_times = []
        app_ids = []
        
        for i in range(20):
            app_data = {
                "name": f"Perf Test App {i}",
                "description": f"Performance test application {i}",
                "environment": "test",
                "criticality": "medium"
            }
            
            start_time = time.time()
            response = await client.post("/api/v1/applications/", json=app_data)
            end_time = time.time()
            
            assert response.status_code == 201
            app_response = response.json()
            app_ids.append(app_response["id"])
            create_times.append(end_time - start_time)
        
        # Test application listing performance
        list_times = []
        
        for _ in range(10):
            start_time = time.time()
            response = await client.get("/api/v1/applications/")
            end_time = time.time()
            
            assert response.status_code == 200
            list_times.append(end_time - start_time)
        
        # Test application retrieval performance
        get_times = []
        
        for app_id in app_ids[:10]:  # Test first 10
            start_time = time.time()
            response = await client.get(f"/api/v1/applications/{app_id}")
            end_time = time.time()
            
            assert response.status_code == 200
            get_times.append(end_time - start_time)
        
        print(f"\nApplication CRUD Performance:")
        print(f"Average create time: {mean(create_times):.3f}s")
        print(f"Average list time: {mean(list_times):.3f}s")
        print(f"Average get time: {mean(get_times):.3f}s")
        
        # Performance assertions
        assert mean(create_times) < 0.2  # Create should be under 200ms
        assert mean(list_times) < 0.1    # List should be under 100ms
        assert mean(get_times) < 0.1     # Get should be under 100ms

    async def test_concurrent_requests_performance(self, authenticated_client):
        """Test performance under concurrent load."""
        client = authenticated_client
        
        async def make_request():
            """Make a single request and return response time."""
            start_time = time.time()
            response = await client.get("/health")
            end_time = time.time()
            return end_time - start_time, response.status_code
        
        # Test with different concurrency levels
        concurrency_levels = [1, 5, 10, 20]
        
        for concurrency in concurrency_levels:
            print(f"\nTesting with {concurrency} concurrent requests:")
            
            # Create tasks for concurrent execution
            tasks = [make_request() for _ in range(concurrency * 5)]  # 5 requests per "user"
            
            start_time = time.time()
            results = await asyncio.gather(*tasks)
            total_time = time.time() - start_time
            
            response_times = [result[0] for result in results]
            status_codes = [result[1] for result in results]
            
            # Calculate metrics
            avg_response_time = mean(response_times)
            max_response_time = max(response_times)
            success_rate = sum(1 for code in status_codes if code == 200) / len(status_codes)
            throughput = len(tasks) / total_time
            
            print(f"  Average response time: {avg_response_time:.3f}s")
            print(f"  Max response time: {max_response_time:.3f}s")
            print(f"  Success rate: {success_rate:.2%}")
            print(f"  Throughput: {throughput:.1f} requests/second")
            
            # Performance assertions
            assert success_rate >= 0.95  # At least 95% success rate
            assert avg_response_time < 1.0  # Average should be under 1 second
            assert max_response_time < 2.0  # Max should be under 2 seconds

    async def test_database_query_performance(self, authenticated_client):
        """Test database query performance with larger datasets."""
        client = authenticated_client
        
        # Create a larger dataset
        print("\nCreating test dataset...")
        app_ids = []
        
        for i in range(50):
            app_data = {
                "name": f"DB Perf Test App {i}",
                "description": f"Database performance test application {i}",
                "environment": "test" if i % 2 == 0 else "production",
                "criticality": "medium"
            }
            
            response = await client.post("/api/v1/applications/", json=app_data)
            assert response.status_code == 201
            app_response = response.json()
            app_ids.append(app_response["id"])
        
        # Test pagination performance
        pagination_times = []
        
        for skip in range(0, 50, 10):
            start_time = time.time()
            response = await client.get(f"/api/v1/applications/?limit=10&skip={skip}")
            end_time = time.time()
            
            assert response.status_code == 200
            pagination_times.append(end_time - start_time)
        
        # Test filtering performance
        filter_times = []
        
        for env in ["test", "production"]:
            start_time = time.time()
            response = await client.get(f"/api/v1/applications/?environment={env}")
            end_time = time.time()
            
            assert response.status_code == 200
            filter_times.append(end_time - start_time)
        
        print(f"\nDatabase Query Performance:")
        print(f"Average pagination time: {mean(pagination_times):.3f}s")
        print(f"Average filter time: {mean(filter_times):.3f}s")
        
        # Database queries should be efficient
        assert mean(pagination_times) < 0.2  # Pagination should be under 200ms
        assert mean(filter_times) < 0.2      # Filtering should be under 200ms

    async def test_memory_usage_stability(self, authenticated_client):
        """Test memory usage stability under load."""
        import psutil
        import os
        
        client = authenticated_client
        process = psutil.Process(os.getpid())
        
        # Get initial memory usage
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Perform many operations
        for i in range(100):
            # Create application
            app_data = {
                "name": f"Memory Test App {i}",
                "description": f"Memory test application {i}",
                "environment": "test"
            }
            
            response = await client.post("/api/v1/applications/", json=app_data)
            assert response.status_code == 201
            
            # List applications
            response = await client.get("/api/v1/applications/")
            assert response.status_code == 200
            
            # Check memory every 20 iterations
            if i % 20 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_increase = current_memory - initial_memory
                
                print(f"Iteration {i}: Memory usage: {current_memory:.1f}MB (+{memory_increase:.1f}MB)")
                
                # Memory shouldn't grow excessively
                assert memory_increase < 100  # Should not increase by more than 100MB

    async def test_api_rate_limiting_behavior(self, authenticated_client):
        """Test API behavior under rapid requests."""
        client = authenticated_client
        
        # Make rapid requests to test rate limiting behavior
        rapid_request_times = []
        
        for _ in range(100):
            start_time = time.time()
            response = await client.get("/health")
            end_time = time.time()
            
            rapid_request_times.append(end_time - start_time)
            
            # Small delay to avoid overwhelming the system
            await asyncio.sleep(0.01)
        
        # Analyze response time distribution
        avg_time = mean(rapid_request_times)
        p95_time = sorted(rapid_request_times)[int(0.95 * len(rapid_request_times))]
        p99_time = sorted(rapid_request_times)[int(0.99 * len(rapid_request_times))]
        
        print(f"\nRapid Request Performance:")
        print(f"Average response time: {avg_time:.3f}s")
        print(f"95th percentile: {p95_time:.3f}s")
        print(f"99th percentile: {p99_time:.3f}s")
        
        # Performance should remain stable under rapid requests
        assert avg_time < 0.2   # Average should stay under 200ms
        assert p95_time < 0.5   # 95% should be under 500ms
        assert p99_time < 1.0   # 99% should be under 1 second

    async def test_large_response_handling(self, authenticated_client):
        """Test handling of large response payloads."""
        client = authenticated_client
        
        # Create many applications to test large response handling
        for i in range(100):
            app_data = {
                "name": f"Large Response Test App {i}",
                "description": f"Large response test application {i} with a very long description that contains lots of text to make the response payload larger and test how the system handles bigger responses",
                "environment": "test",
                "criticality": "medium"
            }
            
            response = await client.post("/api/v1/applications/", json=app_data)
            assert response.status_code == 201
        
        # Test large response retrieval
        start_time = time.time()
        response = await client.get("/api/v1/applications/?limit=100")
        end_time = time.time()
        
        assert response.status_code == 200
        response_data = response.json()
        
        response_time = end_time - start_time
        response_size = len(str(response_data))
        
        print(f"\nLarge Response Handling:")
        print(f"Response time: {response_time:.3f}s")
        print(f"Response size: {response_size} characters")
        print(f"Applications returned: {len(response_data)}")
        
        # Large responses should still be handled efficiently
        assert response_time < 1.0  # Should handle large responses under 1 second
        assert len(response_data) >= 100  # Should return the requested data
