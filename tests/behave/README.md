# Behavior-Driven Development (BDD) Tests for CVE Feed Service

This directory contains Behavior-Driven Development (BDD) tests for the CVE Feed Service, implementing the Gherkin syntax and BDD methodology to ensure the system meets business requirements through natural language scenarios.

## Overview

BDD tests bridge the gap between technical implementation and business requirements by using natural language scenarios that describe system behavior from the user's perspective.

## Structure

```
tests/behave/
├── README.md                    # This documentation
├── behave.ini                   # Behave configuration
├── environment.py               # Test environment setup
├── test_bdd_runner.py          # BDD test runner (pytest-compatible)
├── features/                   # Gherkin feature files
│   ├── application_management.feature
│   ├── authentication.feature
│   ├── cve_management.feature
│   └── data_ingestion.feature
└── steps/                      # Step definitions
    ├── application_steps.py
    ├── auth_steps.py
    ├── cve_steps.py
    └── ingestion_steps.py
```

## Feature Files

### 1. Application Management (`application_management.feature`)
Tests for managing applications in the CVE feed service:
- Creating applications with various field combinations
- Listing and filtering applications
- Pagination support
- Duplicate prevention
- Update and delete operations
- Authorization checks

### 2. Authentication (`authentication.feature`)
Tests for user authentication and authorization:
- User registration and login
- Token-based authentication
- Role-based access control
- Password management
- API key generation and usage
- Session management

### 3. CVE Management (`cve_management.feature`)
Tests for CVE data access and management:
- Listing CVEs with filtering and pagination
- Retrieving specific CVE details
- Tailored CVE feeds for applications
- Search functionality
- CVE statistics and reporting
- Data export capabilities

### 4. Data Ingestion (`data_ingestion.feature`)
Tests for CVE data ingestion from NVD:
- Manual and automatic ingestion
- Bulk import operations
- Incremental updates
- Rate limiting handling
- Error recovery
- Data validation

## Running BDD Tests

### Using the BDD Test Runner (Current Implementation)
```bash
# Run all BDD scenarios
nix-shell --run "python -m pytest tests/behave/test_bdd_runner.py -v"

# Run specific BDD test
nix-shell --run "python -m pytest tests/behave/test_bdd_runner.py::TestBDDScenarios::test_application_creation_scenario -v"
```

### Using Behave (When Available)
```bash
# Run all features
nix-shell --run "behave tests/behave/features/"

# Run specific feature
nix-shell --run "behave tests/behave/features/application_management.feature"

# Run with specific tags
nix-shell --run "behave tests/behave/features/ --tags=@authentication"
```

## BDD Test Results

Current BDD test status:
- ✅ Application Creation Scenario
- ✅ Application Listing Scenario  
- ✅ CVE Listing Scenario
- ✅ Unauthorized Access Scenario
- ✅ Application Filtering Scenario

All 5 BDD scenarios are passing, demonstrating that the core functionality meets the specified business requirements.

## Gherkin Syntax Examples

### Basic Scenario Structure
```gherkin
Feature: Application Management
  As a security administrator
  I want to manage applications in the CVE feed service
  So that I can track vulnerabilities for my organization's applications

  Scenario: Create a new application
    Given I am an authenticated user
    When I create an application with the following details:
      | name        | Test Application      |
      | environment | production           |
      | description | A test application   |
    Then the application should be created successfully
    And the response should contain the application details
```

### Scenario with Data Tables
```gherkin
  Scenario: Filter applications by environment
    Given I am an authenticated user
    And the following applications exist:
      | name     | environment | criticality |
      | App One  | production  | high        |
      | App Two  | development | medium      |
    When I request applications filtered by "production" environment
    Then I should receive 1 application
    And the application should be in "production" environment
```

## Step Definitions

Step definitions translate Gherkin steps into executable code:

```python
@given('I am an authenticated user')
def step_authenticated_user(context):
    """Set up an authenticated user."""
    # Implementation details...

@when('I create an application with the following details')
def step_create_application_with_details(context):
    """Create an application with specified details."""
    # Implementation details...

@then('the application should be created successfully')
def step_application_created_successfully(context):
    """Verify application was created successfully."""
    # Implementation details...
```

## Benefits of BDD Testing

1. **Business-Readable Tests**: Tests are written in natural language that stakeholders can understand
2. **Requirements Validation**: Ensures the system meets actual business needs
3. **Living Documentation**: Feature files serve as up-to-date documentation
4. **Collaboration**: Facilitates communication between developers, testers, and business analysts
5. **Regression Prevention**: Catches changes that break expected behavior

## Integration with CI/CD

BDD tests can be integrated into the CI/CD pipeline:

```yaml
# Example GitHub Actions workflow
- name: Run BDD Tests
  run: |
    nix-shell --run "python -m pytest tests/behave/test_bdd_runner.py -v"
```

## Future Enhancements

1. **Full Behave Integration**: Install and configure the behave package for native Gherkin execution
2. **Visual Reports**: Generate HTML reports showing BDD test results
3. **Parallel Execution**: Run BDD scenarios in parallel for faster feedback
4. **Environment-Specific Tests**: Create environment-specific BDD scenarios
5. **Performance BDD**: Add performance-focused BDD scenarios

## Best Practices

1. **Clear Scenarios**: Write scenarios that clearly describe business value
2. **Reusable Steps**: Create reusable step definitions to avoid duplication
3. **Data Management**: Use proper test data setup and cleanup
4. **Meaningful Names**: Use descriptive names for scenarios and steps
5. **Regular Updates**: Keep feature files synchronized with system changes

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure PYTHONPATH includes the src directory
2. **Database Issues**: Verify test database setup and cleanup
3. **Authentication**: Check that authentication steps match the API implementation
4. **Async Operations**: Ensure proper async/await handling in step definitions

### Debug Mode
```bash
# Run with verbose output
nix-shell --run "python -m pytest tests/behave/test_bdd_runner.py -v -s"
```

This BDD testing framework provides a solid foundation for ensuring the CVE Feed Service meets business requirements while maintaining high code quality and test coverage.
