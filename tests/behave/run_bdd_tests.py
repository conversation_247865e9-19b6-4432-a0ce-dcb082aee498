#!/usr/bin/env python3
"""
BDD Test Runner Script

This script runs all BDD tests and generates a comprehensive report
showing which business scenarios are passing or failing.
"""

import subprocess
import sys
import json
from datetime import datetime
from pathlib import Path


def run_bdd_tests():
    """Run BDD tests and capture results."""
    print("🧪 CVE Feed Service - BDD Test Suite")
    print("=" * 50)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run the BDD tests
    cmd = [
        "python", "-m", "pytest",
        "tests/behave/test_bdd_runner.py",
        "-v", "--tb=short"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=Path.cwd())
        
        # Print test output
        print("📋 Test Execution Output:")
        print("-" * 30)
        print(result.stdout)
        
        if result.stderr:
            print("⚠️  Warnings/Errors:")
            print("-" * 20)
            print(result.stderr)
        
        # Generate summary report
        generate_summary_report(result.returncode)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Error running BDD tests: {e}")
        return False


def generate_summary_report(return_code):
    """Generate a summary report of BDD test results."""
    print("\n📊 BDD Test Summary Report")
    print("=" * 40)
    
    # Define the BDD scenarios we're testing
    scenarios = [
        {
            "name": "Application Creation",
            "description": "Users can create new applications with proper validation",
            "business_value": "Enables tracking of organizational applications"
        },
        {
            "name": "Application Listing", 
            "description": "Users can list and filter applications",
            "business_value": "Provides visibility into all tracked applications"
        },
        {
            "name": "CVE Listing",
            "description": "Users can access CVE vulnerability information",
            "business_value": "Enables security assessment and risk management"
        },
        {
            "name": "Access Control",
            "description": "System handles unauthorized access appropriately",
            "business_value": "Ensures data security and proper access controls"
        },
        {
            "name": "Application Filtering",
            "description": "Users can filter applications by environment",
            "business_value": "Enables environment-specific vulnerability tracking"
        }
    ]
    
    # Status based on return code
    overall_status = "✅ PASSED" if return_code == 0 else "❌ FAILED"
    
    print(f"Overall Status: {overall_status}")
    print(f"Total Scenarios: {len(scenarios)}")
    print()
    
    print("📋 Business Scenarios Status:")
    print("-" * 35)
    
    for i, scenario in enumerate(scenarios, 1):
        # For this demo, we'll assume all passed if return_code is 0
        status = "✅ PASS" if return_code == 0 else "❌ FAIL"
        print(f"{i}. {scenario['name']}: {status}")
        print(f"   Description: {scenario['description']}")
        print(f"   Business Value: {scenario['business_value']}")
        print()
    
    # Coverage information
    print("📈 BDD Coverage Areas:")
    print("-" * 25)
    coverage_areas = [
        "✅ Application Management",
        "✅ CVE Data Access", 
        "✅ User Authentication (Basic)",
        "✅ Data Filtering & Pagination",
        "⏳ Data Ingestion (Planned)",
        "⏳ Advanced Authentication (Planned)",
        "⏳ Component Management (Planned)"
    ]
    
    for area in coverage_areas:
        print(f"  {area}")
    
    print()
    print("🎯 BDD Testing Benefits Achieved:")
    print("-" * 35)
    benefits = [
        "Business-readable test scenarios",
        "Requirements validation through natural language",
        "Living documentation of system behavior", 
        "Stakeholder-friendly test reports",
        "Behavior-focused testing approach"
    ]
    
    for benefit in benefits:
        print(f"  ✅ {benefit}")
    
    print()
    print("🔮 Next Steps for BDD Enhancement:")
    print("-" * 40)
    next_steps = [
        "Install and configure full Behave framework",
        "Add more complex authentication scenarios",
        "Implement data ingestion BDD tests",
        "Create component management scenarios",
        "Add performance-focused BDD tests",
        "Generate HTML BDD reports",
        "Integrate with CI/CD pipeline"
    ]
    
    for step in next_steps:
        print(f"  📋 {step}")


def main():
    """Main entry point."""
    success = run_bdd_tests()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All BDD scenarios passed! System behavior meets business requirements.")
    else:
        print("⚠️  Some BDD scenarios failed. Review the output above for details.")
    
    print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
