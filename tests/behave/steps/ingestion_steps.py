"""Step definitions for data ingestion features."""

from unittest.mock import AsyncMock, patch
from datetime import datetime
from behave import given, when, then

from tests.behave.environment import get_context, run_async


@given('I am an authenticated admin user')
def step_authenticated_admin_user(context):
    """Set up an authenticated admin user."""
    behave_context = get_context(context)
    
    async def create_admin_user():
        user_data = {
            "username": "admin",
            "email": "<EMAIL>",
            "full_name": "Admin User",
            "password": "AdminPassword123!",
            "role": "IT_ADMIN"
        }
        
        # Register admin user
        response = await behave_context.client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201
        
        # Login to get token
        login_data = {
            "username": "admin",
            "password": "AdminPassword123!"
        }
        response = await behave_context.client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 200
        
        token_data = response.json()
        behave_context.auth_token = token_data["access_token"]
        behave_context.current_user = {**user_data, "role": "IT_ADMIN"}
        
        # Set authorization header
        behave_context.client.headers.update({
            "Authorization": f"Bearer {behave_context.auth_token}"
        })
    
    run_async(context, create_admin_user())


@given('the NVD API is available')
def step_nvd_api_available(context):
    """Mock NVD API as available."""
    behave_context = get_context(context)
    behave_context.test_data['nvd_api_available'] = True


@given('the NVD API returns sample CVE data')
def step_nvd_api_returns_sample_data(context):
    """Mock NVD API to return sample CVE data."""
    behave_context = get_context(context)
    
    # Convert table to list of CVE data
    sample_cves = []
    for row in context.table:
        cve_data = {
            "cve": {
                "id": row['cve_id'],
                "descriptions": [{"lang": "en", "value": row['description']}],
                "published": datetime.utcnow().isoformat() + "Z",
                "lastModified": datetime.utcnow().isoformat() + "Z",
                "metrics": {
                    "cvssMetricV31": [{
                        "cvssData": {
                            "baseScore": float(row['cvss_v3_score']),
                            "baseSeverity": row['severity'],
                            "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H"
                        }
                    }]
                }
            }
        }
        sample_cves.append(cve_data)
    
    behave_context.test_data['sample_nvd_data'] = sample_cves


@given('existing CVEs in the database')
def step_existing_cves_in_database(context):
    """Create existing CVEs in the database."""
    behave_context = get_context(context)
    
    async def create_existing_cves():
        from src.cve_feed_service.services.cve_service import CVEService
        from src.cve_feed_service.schemas.cve import CVECreate
        
        cve_service = CVEService(behave_context.db_session)
        
        for row in context.table:
            last_modified = datetime.strptime(row['last_modified'], '%Y-%m-%d')
            
            cve_data = CVECreate(
                cve_id=row['cve_id'],
                description=f"Existing CVE {row['cve_id']}",
                last_modified_date=last_modified,
                published_date=last_modified,
                cvss_v3_score=5.0,
                cvss_v3_severity="MEDIUM"
            )
            
            cve = await cve_service.create_cve(cve_data)
            behave_context.created_entities[f'existing_cve_{row["cve_id"]}'] = cve
        
        await behave_context.db_session.commit()
    
    run_async(context, create_existing_cves())


@given('the NVD API returns updated CVE data')
def step_nvd_api_returns_updated_data(context):
    """Mock NVD API to return updated CVE data."""
    behave_context = get_context(context)
    
    # Convert table to list of updated CVE data
    updated_cves = []
    for row in context.table:
        last_modified = datetime.strptime(row['last_modified'], '%Y-%m-%d')
        
        cve_data = {
            "cve": {
                "id": row['cve_id'],
                "descriptions": [{"lang": "en", "value": f"Updated {row['cve_id']}"}],
                "published": "2023-01-01T10:00:00.000Z",
                "lastModified": last_modified.isoformat() + "Z",
                "metrics": {
                    "cvssMetricV31": [{
                        "cvssData": {
                            "baseScore": float(row['cvss_v3_score']),
                            "baseSeverity": "HIGH",
                            "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H"
                        }
                    }]
                }
            }
        }
        updated_cves.append(cve_data)
    
    behave_context.test_data['updated_nvd_data'] = updated_cves


@given('the NVD API has rate limiting enabled')
def step_nvd_api_rate_limiting(context):
    """Mock NVD API with rate limiting."""
    behave_context = get_context(context)
    behave_context.test_data['nvd_rate_limiting'] = True


@given('I have a large batch of CVEs to import')
def step_large_batch_cves(context):
    """Set up a large batch of CVEs for import."""
    behave_context = get_context(context)
    
    # Create a large batch of mock CVE data
    large_batch = []
    for i in range(100):  # 100 CVEs
        cve_data = {
            "cve": {
                "id": f"CVE-2023-BATCH-{i:03d}",
                "descriptions": [{"lang": "en", "value": f"Batch CVE {i}"}],
                "published": datetime.utcnow().isoformat() + "Z",
                "lastModified": datetime.utcnow().isoformat() + "Z",
                "metrics": {
                    "cvssMetricV31": [{
                        "cvssData": {
                            "baseScore": 5.0 + (i % 5),
                            "baseSeverity": "MEDIUM",
                            "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L"
                        }
                    }]
                }
            }
        }
        large_batch.append(cve_data)
    
    behave_context.test_data['large_batch_cves'] = large_batch


@given('the NVD API is temporarily unavailable')
def step_nvd_api_unavailable(context):
    """Mock NVD API as temporarily unavailable."""
    behave_context = get_context(context)
    behave_context.test_data['nvd_api_unavailable'] = True


@when('I trigger a manual CVE data ingestion')
def step_trigger_manual_ingestion(context):
    """Trigger manual CVE data ingestion."""
    behave_context = get_context(context)
    
    async def trigger_ingestion():
        response = await behave_context.client.post("/api/v1/admin/ingest/manual")
        behave_context.response = response
    
    run_async(context, trigger_ingestion())


@when('I perform a bulk CVE import')
def step_perform_bulk_import(context):
    """Perform bulk CVE import with mocked data."""
    behave_context = get_context(context)
    
    async def bulk_import():
        with patch('src.cve_feed_service.services.cve_ingestion_service.NVDAPIClient') as mock_client:
            # Mock the NVD client to return our sample data
            mock_instance = AsyncMock()
            mock_instance.get_cves_by_date_range.return_value = behave_context.test_data.get('sample_nvd_data', [])
            mock_client.return_value.__aenter__.return_value = mock_instance
            
            response = await behave_context.client.post("/api/v1/admin/ingest/bulk", json={"years": 1})
            behave_context.response = response
    
    run_async(context, bulk_import())


@when('I perform an incremental CVE update')
def step_perform_incremental_update(context):
    """Perform incremental CVE update."""
    behave_context = get_context(context)
    
    async def incremental_update():
        with patch('src.cve_feed_service.services.cve_ingestion_service.NVDAPIClient') as mock_client:
            # Mock the NVD client to return updated data
            mock_instance = AsyncMock()
            mock_instance.get_cves_by_date_range.return_value = behave_context.test_data.get('updated_nvd_data', [])
            mock_client.return_value.__aenter__.return_value = mock_instance
            
            response = await behave_context.client.post("/api/v1/admin/ingest/incremental", json={"hours": 24})
            behave_context.response = response
    
    run_async(context, incremental_update())


@when('I start the bulk import process')
def step_start_bulk_import_process(context):
    """Start the bulk import process."""
    behave_context = get_context(context)
    
    async def start_bulk_import():
        with patch('src.cve_feed_service.services.cve_ingestion_service.NVDAPIClient') as mock_client:
            # Mock rate limiting behavior
            mock_instance = AsyncMock()
            mock_instance.get_cves_by_date_range.return_value = behave_context.test_data.get('large_batch_cves', [])
            mock_client.return_value.__aenter__.return_value = mock_instance
            
            response = await behave_context.client.post("/api/v1/admin/ingest/bulk", json={"years": 1})
            behave_context.response = response
    
    run_async(context, start_bulk_import())


@when('I attempt to import CVE data')
def step_attempt_import_cve_data(context):
    """Attempt to import CVE data."""
    behave_context = get_context(context)
    
    async def attempt_import():
        if behave_context.test_data.get('nvd_api_unavailable'):
            # Mock API unavailable
            with patch('src.cve_feed_service.services.cve_ingestion_service.NVDAPIClient') as mock_client:
                mock_instance = AsyncMock()
                mock_instance.get_cves_by_date_range.side_effect = Exception("API Unavailable")
                mock_client.return_value.__aenter__.return_value = mock_instance
                
                response = await behave_context.client.post("/api/v1/admin/ingest/manual")
                behave_context.response = response
        else:
            response = await behave_context.client.post("/api/v1/admin/ingest/manual")
            behave_context.response = response
    
    run_async(context, attempt_import())


@then('the ingestion process should start successfully')
def step_ingestion_starts_successfully(context):
    """Verify ingestion process starts successfully."""
    behave_context = get_context(context)
    assert behave_context.response.status_code in [200, 202]  # OK or Accepted


@then('I should receive a confirmation message')
def step_receive_confirmation_message(context):
    """Verify we receive a confirmation message."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    assert 'message' in response_data or 'status' in response_data


@then('the process should run in the background')
def step_process_runs_in_background(context):
    """Verify process runs in background."""
    behave_context = get_context(context)
    # For async processes, we typically get a 202 Accepted status
    assert behave_context.response.status_code in [200, 202]


@then('all {count:d} CVEs should be imported successfully')
def step_all_cves_imported_successfully(context, count):
    """Verify all CVEs were imported successfully."""
    behave_context = get_context(context)
    
    # Check response indicates success
    assert behave_context.response.status_code in [200, 202]
    
    # Verify CVEs exist in database
    async def verify_cves_in_db():
        from sqlalchemy import select
        from src.cve_feed_service.models.cve import CVE
        
        result = await behave_context.db_session.execute(select(CVE))
        cves = result.scalars().all()
        
        # Should have at least the imported CVEs
        assert len(cves) >= count
    
    run_async(context, verify_cves_in_db())


@then('the CVEs should be stored in the database')
def step_cves_stored_in_database(context):
    """Verify CVEs are stored in the database."""
    behave_context = get_context(context)
    
    async def verify_cves_stored():
        from sqlalchemy import select
        from src.cve_feed_service.models.cve import CVE
        
        result = await behave_context.db_session.execute(select(CVE))
        cves = result.scalars().all()
        
        assert len(cves) > 0
        
        # Verify CVE structure
        for cve in cves:
            assert cve.cve_id is not None
            assert cve.description is not None
    
    run_async(context, verify_cves_stored())


@then('each CVE should have complete metadata')
def step_cves_have_complete_metadata(context):
    """Verify CVEs have complete metadata."""
    behave_context = get_context(context)
    
    async def verify_complete_metadata():
        from sqlalchemy import select
        from src.cve_feed_service.models.cve import CVE
        
        result = await behave_context.db_session.execute(select(CVE))
        cves = result.scalars().all()
        
        for cve in cves:
            assert cve.cve_id is not None
            assert cve.description is not None
            assert cve.published_date is not None
            assert cve.last_modified_date is not None
    
    run_async(context, verify_complete_metadata())


@then('the existing CVE "{cve_id}" should be updated')
def step_existing_cve_updated(context, cve_id):
    """Verify existing CVE was updated."""
    behave_context = get_context(context)
    
    async def verify_cve_updated():
        from sqlalchemy import select
        from src.cve_feed_service.models.cve import CVE
        
        result = await behave_context.db_session.execute(
            select(CVE).where(CVE.cve_id == cve_id)
        )
        cve = result.scalar_one_or_none()
        
        assert cve is not None
        # Verify it was updated (description should contain "Updated")
        assert "Updated" in cve.description
    
    run_async(context, verify_cve_updated())


@then('the new CVE "{cve_id}" should be added')
def step_new_cve_added(context, cve_id):
    """Verify new CVE was added."""
    behave_context = get_context(context)
    
    async def verify_new_cve():
        from sqlalchemy import select
        from src.cve_feed_service.models.cve import CVE
        
        result = await behave_context.db_session.execute(
            select(CVE).where(CVE.cve_id == cve_id)
        )
        cve = result.scalar_one_or_none()
        
        assert cve is not None
    
    run_async(context, verify_new_cve())


@then('the system should respect the rate limits')
def step_system_respects_rate_limits(context):
    """Verify system respects rate limits."""
    behave_context = get_context(context)
    # For this test, we just verify the process completed without errors
    assert behave_context.response.status_code in [200, 202]


@then('the import should continue without errors')
def step_import_continues_without_errors(context):
    """Verify import continues without errors."""
    behave_context = get_context(context)
    assert behave_context.response.status_code in [200, 202]


@then('all CVEs should eventually be imported')
def step_all_cves_eventually_imported(context):
    """Verify all CVEs are eventually imported."""
    behave_context = get_context(context)
    
    async def verify_all_imported():
        from sqlalchemy import select
        from src.cve_feed_service.models.cve import CVE
        
        result = await behave_context.db_session.execute(select(CVE))
        cves = result.scalars().all()
        
        # Should have imported some CVEs
        assert len(cves) > 0
    
    run_async(context, verify_all_imported())


@then('the system should handle the error gracefully')
def step_system_handles_error_gracefully(context):
    """Verify system handles errors gracefully."""
    behave_context = get_context(context)
    # Should get an error response, but not crash
    assert behave_context.response.status_code in [400, 500, 502, 503]


@then('I should receive an appropriate error message if retries fail')
def step_receive_appropriate_error_message(context):
    """Verify we receive appropriate error message."""
    behave_context = get_context(context)
    
    if behave_context.response.status_code >= 400:
        response_data = behave_context.response.json()
        assert 'detail' in response_data or 'message' in response_data
