"""Step definitions for authentication and authorization features."""

import jwt
from datetime import datetime, timedelta
from behave import given, when, then

from tests.behave.environment import get_context, run_async


@when('I register a new user with the following details')
def step_register_user_with_details(context):
    """Register a new user with specified details."""
    behave_context = get_context(context)
    
    # Convert table to dictionary
    user_data = {}
    for row in context.table:
        user_data[row['username']] = row['testuser']
    
    async def register_user():
        response = await behave_context.client.post("/api/v1/auth/register", json=user_data)
        behave_context.response = response
        
        if response.status_code == 201:
            user_response = response.json()
            behave_context.created_entities['user'] = user_response
    
    run_async(context, register_user())


@given('a user "{username}" already exists')
def step_user_already_exists(context, username):
    """Create a user that already exists."""
    behave_context = get_context(context)
    
    user_data = {
        "username": username,
        "email": f"{username}@example.com",
        "full_name": f"Existing User {username}",
        "password": "ExistingPassword123!",
        "role": "SECURITY_ANALYST"
    }
    
    async def create_existing_user():
        response = await behave_context.client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201
        behave_context.created_entities[f'user_{username}'] = response.json()
    
    run_async(context, create_existing_user())


@when('I try to register a new user with username "{username}"')
def step_try_register_duplicate_user(context, username):
    """Try to register a user with duplicate username."""
    behave_context = get_context(context)
    
    user_data = {
        "username": username,
        "email": f"new{username}@example.com",
        "full_name": f"New User {username}",
        "password": "NewPassword123!",
        "role": "SECURITY_ANALYST"
    }
    
    async def register_duplicate():
        response = await behave_context.client.post("/api/v1/auth/register", json=user_data)
        behave_context.response = response
    
    run_async(context, register_duplicate())


@when('I try to register a user with email "{email}"')
def step_try_register_invalid_email(context, email):
    """Try to register a user with invalid email."""
    behave_context = get_context(context)
    
    user_data = {
        "username": "testuser",
        "email": email,
        "full_name": "Test User",
        "password": "ValidPassword123!",
        "role": "SECURITY_ANALYST"
    }
    
    async def register_invalid_email():
        response = await behave_context.client.post("/api/v1/auth/register", json=user_data)
        behave_context.response = response
    
    run_async(context, register_invalid_email())


@when('I try to register a user with password "{password}"')
def step_try_register_weak_password(context, password):
    """Try to register a user with weak password."""
    behave_context = get_context(context)
    
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "password": password,
        "role": "SECURITY_ANALYST"
    }
    
    async def register_weak_password():
        response = await behave_context.client.post("/api/v1/auth/register", json=user_data)
        behave_context.response = response
    
    run_async(context, register_weak_password())


@given('a user "{username}" exists with password "{password}"')
def step_user_exists_with_password(context, username, password):
    """Create a user with specific password."""
    behave_context = get_context(context)
    
    user_data = {
        "username": username,
        "email": f"{username}@example.com",
        "full_name": f"User {username}",
        "password": password,
        "role": "SECURITY_ANALYST"
    }
    
    async def create_user_with_password():
        response = await behave_context.client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201
        behave_context.created_entities[f'user_{username}'] = {
            **response.json(),
            'password': password  # Store for login tests
        }
    
    run_async(context, create_user_with_password())


@when('I login with username "{username}" and password "{password}"')
def step_login_with_credentials(context, username, password):
    """Login with specified credentials."""
    behave_context = get_context(context)
    
    login_data = {
        "username": username,
        "password": password
    }
    
    async def login():
        response = await behave_context.client.post("/api/v1/auth/login", data=login_data)
        behave_context.response = response
        
        if response.status_code == 200:
            token_data = response.json()
            behave_context.auth_token = token_data["access_token"]
            behave_context.client.headers.update({
                "Authorization": f"Bearer {behave_context.auth_token}"
            })
    
    run_async(context, login())


@given('I am an authenticated user with role "{role}"')
def step_authenticated_user_with_role(context, role):
    """Set up an authenticated user with specific role."""
    behave_context = get_context(context)
    
    async def create_auth_user_with_role():
        user_data = {
            "username": f"user_{role.lower()}",
            "email": f"{role.lower()}@example.com",
            "full_name": f"User with {role} role",
            "password": "RolePassword123!",
            "role": role
        }
        
        # Register user
        response = await behave_context.client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201
        
        # Login to get token
        login_data = {
            "username": user_data["username"],
            "password": user_data["password"]
        }
        response = await behave_context.client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 200
        
        token_data = response.json()
        behave_context.auth_token = token_data["access_token"]
        behave_context.current_user = {**user_data, "role": role}
        
        # Set authorization header
        behave_context.client.headers.update({
            "Authorization": f"Bearer {behave_context.auth_token}"
        })
    
    run_async(context, create_auth_user_with_role())


@when('I access a protected endpoint with my token')
def step_access_protected_endpoint(context):
    """Access a protected endpoint with authentication token."""
    behave_context = get_context(context)
    
    async def access_protected():
        response = await behave_context.client.get("/api/v1/applications/")
        behave_context.response = response
    
    run_async(context, access_protected())


@when('I try to access a protected endpoint without a token')
def step_access_protected_without_token(context):
    """Try to access a protected endpoint without token."""
    behave_context = get_context(context)
    
    # Remove authorization header
    if "Authorization" in behave_context.client.headers:
        del behave_context.client.headers["Authorization"]
    
    async def access_without_token():
        response = await behave_context.client.get("/api/v1/applications/")
        behave_context.response = response
    
    run_async(context, access_without_token())


@given('I have an expired authentication token')
def step_have_expired_token(context):
    """Set up an expired authentication token."""
    behave_context = get_context(context)
    
    # Create an expired token
    expired_payload = {
        "sub": "testuser",
        "exp": datetime.utcnow() - timedelta(hours=1)  # Expired 1 hour ago
    }
    
    expired_token = jwt.encode(expired_payload, "test-secret-key", algorithm="HS256")
    behave_context.auth_token = expired_token
    
    behave_context.client.headers.update({
        "Authorization": f"Bearer {expired_token}"
    })


@given('I have an invalid authentication token')
def step_have_invalid_token(context):
    """Set up an invalid authentication token."""
    behave_context = get_context(context)
    
    invalid_token = "invalid.token.here"
    behave_context.auth_token = invalid_token
    
    behave_context.client.headers.update({
        "Authorization": f"Bearer {invalid_token}"
    })


@when('I try to access a protected endpoint with the expired token')
def step_access_with_expired_token(context):
    """Try to access protected endpoint with expired token."""
    behave_context = get_context(context)
    
    async def access_with_expired():
        response = await behave_context.client.get("/api/v1/applications/")
        behave_context.response = response
    
    run_async(context, access_with_expired())


@when('I try to access a protected endpoint with the invalid token')
def step_access_with_invalid_token(context):
    """Try to access protected endpoint with invalid token."""
    behave_context = get_context(context)
    
    async def access_with_invalid():
        response = await behave_context.client.get("/api/v1/applications/")
        behave_context.response = response
    
    run_async(context, access_with_invalid())


@then('the user should be created successfully')
def step_user_created_successfully(context):
    """Verify user was created successfully."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 201


@then('the password should not be visible in the response')
def step_password_not_visible(context):
    """Verify password is not in response."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    assert 'password' not in response_data


@then('the registration should fail with conflict error')
def step_registration_fails_conflict(context):
    """Verify registration fails with conflict error."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 400


@then('the error message should indicate duplicate username')
def step_error_indicates_duplicate_username(context):
    """Verify error indicates duplicate username."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    assert 'detail' in response_data
    assert 'already exists' in response_data['detail'].lower()


@then('the error message should indicate invalid email format')
def step_error_indicates_invalid_email(context):
    """Verify error indicates invalid email format."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    assert 'detail' in response_data


@then('the error message should indicate password requirements')
def step_error_indicates_password_requirements(context):
    """Verify error indicates password requirements."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    assert 'detail' in response_data


@then('the login should be successful')
def step_login_successful(context):
    """Verify login was successful."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 200


@then('I should receive an access token')
def step_receive_access_token(context):
    """Verify we receive an access token."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    assert 'access_token' in response_data
    assert response_data['access_token'] is not None


@then('the token should be valid for API access')
def step_token_valid_for_api(context):
    """Verify token is valid for API access."""
    behave_context = get_context(context)
    assert behave_context.auth_token is not None


@then('the login should fail with unauthorized error')
def step_login_fails_unauthorized(context):
    """Verify login fails with unauthorized error."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 401


@then('I should not receive an access token')
def step_no_access_token(context):
    """Verify we don't receive an access token."""
    behave_context = get_context(context)
    if behave_context.response.status_code == 200:
        response_data = behave_context.response.json()
        assert 'access_token' not in response_data
    # If status is not 200, then we definitely didn't get a token


@then('the request should be successful')
def step_request_successful(context):
    """Verify request was successful."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 200


@then('I should receive the requested data')
def step_receive_requested_data(context):
    """Verify we receive the requested data."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    assert response_data is not None
