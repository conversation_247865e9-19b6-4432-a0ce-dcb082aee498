"""Step definitions for application management features."""

import json
import uuid
from behave import given, when, then
from behave.api.async_step import async_run_until_complete

from tests.behave.environment import get_context, run_async


@given('the CVE feed service is running')
def step_service_running(context):
    """Ensure the CVE feed service is running."""
    behave_context = get_context(context)
    assert behave_context.client is not None


@given('I have a clean database')
def step_clean_database(context):
    """Ensure we have a clean database."""
    behave_context = get_context(context)
    assert behave_context.db_session is not None


@given('I am an authenticated user')
def step_authenticated_user(context):
    """Set up an authenticated user."""
    behave_context = get_context(context)
    
    # Create a test user and get auth token
    async def create_auth_user():
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "full_name": "Test User",
            "password": "TestPassword123!",
            "role": "SECURITY_ANALYST"
        }
        
        # Register user
        response = await behave_context.client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201
        
        # Login to get token
        login_data = {
            "username": "testuser",
            "password": "TestPassword123!"
        }
        response = await behave_context.client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 200
        
        token_data = response.json()
        behave_context.auth_token = token_data["access_token"]
        behave_context.current_user = user_data
        
        # Set authorization header for future requests
        behave_context.client.headers.update({
            "Authorization": f"Bearer {behave_context.auth_token}"
        })
    
    run_async(context, create_auth_user())


@given('I am not authenticated')
def step_not_authenticated(context):
    """Ensure user is not authenticated."""
    behave_context = get_context(context)
    behave_context.auth_token = None
    behave_context.current_user = None
    
    # Remove authorization header
    if "Authorization" in behave_context.client.headers:
        del behave_context.client.headers["Authorization"]


@when('I create an application with the following details')
def step_create_application_with_details(context):
    """Create an application with specified details."""
    behave_context = get_context(context)
    
    # Convert table to dictionary
    app_data = {}
    for row in context.table:
        app_data[row['name']] = row['Test Application']
    
    async def create_app():
        response = await behave_context.client.post("/api/v1/applications/", json=app_data)
        behave_context.response = response
        
        if response.status_code == 201:
            app_data_response = response.json()
            behave_context.created_entities['application'] = app_data_response
    
    run_async(context, create_app())


@when('I create an application with only required fields')
def step_create_application_minimal(context):
    """Create an application with only required fields."""
    behave_context = get_context(context)
    
    # Convert table to dictionary
    app_data = {}
    for row in context.table:
        app_data[row['name']] = row['Minimal App']
    
    async def create_app():
        response = await behave_context.client.post("/api/v1/applications/", json=app_data)
        behave_context.response = response
        
        if response.status_code == 201:
            app_data_response = response.json()
            behave_context.created_entities['application'] = app_data_response
    
    run_async(context, create_app())


@when('I try to create an application without a name')
def step_create_application_no_name(context):
    """Try to create an application without a name."""
    behave_context = get_context(context)
    
    app_data = {
        "environment": "test",
        "description": "Missing name"
    }
    
    async def create_app():
        response = await behave_context.client.post("/api/v1/applications/", json=app_data)
        behave_context.response = response
    
    run_async(context, create_app())


@given('an application "{app_name}" exists in "{environment}" environment')
def step_application_exists(context, app_name, environment):
    """Create an application that exists."""
    behave_context = get_context(context)
    
    app_data = {
        "name": app_name,
        "environment": environment,
        "description": f"Existing application {app_name}"
    }
    
    async def create_existing_app():
        response = await behave_context.client.post("/api/v1/applications/", json=app_data)
        assert response.status_code == 201
        
        app_response = response.json()
        behave_context.created_entities[f'app_{app_name}_{environment}'] = app_response
    
    run_async(context, create_existing_app())


@when('I try to create another application "{app_name}" in "{environment}" environment')
def step_create_duplicate_application(context, app_name, environment):
    """Try to create a duplicate application."""
    behave_context = get_context(context)
    
    app_data = {
        "name": app_name,
        "environment": environment,
        "description": "Duplicate application"
    }
    
    async def create_duplicate():
        response = await behave_context.client.post("/api/v1/applications/", json=app_data)
        behave_context.response = response
    
    run_async(context, create_duplicate())


@when('I create an application "{app_name}" in "{environment}" environment')
def step_create_application_different_env(context, app_name, environment):
    """Create an application in a different environment."""
    behave_context = get_context(context)
    
    app_data = {
        "name": app_name,
        "environment": environment,
        "description": f"Application {app_name} in {environment}"
    }
    
    async def create_app():
        response = await behave_context.client.post("/api/v1/applications/", json=app_data)
        behave_context.response = response
        
        if response.status_code == 201:
            app_response = response.json()
            behave_context.created_entities[f'app_{app_name}_{environment}'] = app_response
    
    run_async(context, create_app())


@given('the following applications exist')
def step_multiple_applications_exist(context):
    """Create multiple applications from table."""
    behave_context = get_context(context)
    
    async def create_multiple_apps():
        for row in context.table:
            app_data = {
                "name": row['name'],
                "environment": row['environment'],
                "criticality": row.get('criticality', 'medium'),
                "description": f"Test application {row['name']}"
            }
            
            response = await behave_context.client.post("/api/v1/applications/", json=app_data)
            assert response.status_code == 201
            
            app_response = response.json()
            behave_context.created_entities[f"app_{row['name']}"] = app_response
    
    run_async(context, create_multiple_apps())


@when('I request the list of all applications')
def step_list_all_applications(context):
    """Request the list of all applications."""
    behave_context = get_context(context)
    
    async def list_apps():
        response = await behave_context.client.get("/api/v1/applications/")
        behave_context.response = response
    
    run_async(context, list_apps())


@when('I request applications filtered by "{environment}" environment')
def step_list_applications_filtered(context, environment):
    """Request applications filtered by environment."""
    behave_context = get_context(context)
    
    async def list_filtered_apps():
        response = await behave_context.client.get(f"/api/v1/applications/?environment={environment}")
        behave_context.response = response
    
    run_async(context, list_filtered_apps())


@given('{count:d} applications exist in the system')
def step_multiple_apps_exist_count(context, count):
    """Create a specific number of applications."""
    behave_context = get_context(context)
    
    async def create_apps():
        for i in range(count):
            app_data = {
                "name": f"App {i+1:02d}",
                "environment": "test",
                "description": f"Test application number {i+1}"
            }
            
            response = await behave_context.client.post("/api/v1/applications/", json=app_data)
            assert response.status_code == 201
    
    run_async(context, create_apps())


@when('I request the first page with {per_page:d} applications per page')
def step_request_first_page(context, per_page):
    """Request the first page of applications."""
    behave_context = get_context(context)
    
    async def get_first_page():
        response = await behave_context.client.get(f"/api/v1/applications/?skip=0&limit={per_page}")
        behave_context.response = response
        behave_context.test_data['first_page'] = response.json() if response.status_code == 200 else None
    
    run_async(context, get_first_page())


@when('I request the second page with {per_page:d} applications per page')
def step_request_second_page(context, per_page):
    """Request the second page of applications."""
    behave_context = get_context(context)
    
    async def get_second_page():
        response = await behave_context.client.get(f"/api/v1/applications/?skip={per_page}&limit={per_page}")
        behave_context.response = response
        behave_context.test_data['second_page'] = response.json() if response.status_code == 200 else None
    
    run_async(context, get_second_page())


@then('the application should be created successfully')
def step_application_created_successfully(context):
    """Verify application was created successfully."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 201


@then('the response should contain the application details')
def step_response_contains_application_details(context):
    """Verify response contains application details."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    
    assert 'id' in response_data
    assert 'name' in response_data
    assert 'environment' in response_data
    assert 'created_at' in response_data
    assert 'updated_at' in response_data


@then('the application should have a unique ID')
def step_application_has_unique_id(context):
    """Verify application has a unique ID."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    
    app_id = response_data['id']
    assert app_id is not None
    
    # Verify it's a valid UUID
    try:
        uuid.UUID(app_id)
    except ValueError:
        assert False, f"Application ID {app_id} is not a valid UUID"


@then('the request should fail with validation error')
def step_request_fails_validation(context):
    """Verify request fails with validation error."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 422


@then('the error message should indicate missing required field')
def step_error_indicates_missing_field(context):
    """Verify error message indicates missing required field."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    
    assert 'detail' in response_data
    # The exact error format may vary, but should indicate validation error
    assert isinstance(response_data['detail'], (str, list))


@then('the request should fail with conflict error')
def step_request_fails_conflict(context):
    """Verify request fails with conflict error."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 400


@then('the error message should indicate duplicate application')
def step_error_indicates_duplicate(context):
    """Verify error message indicates duplicate application."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    
    assert 'detail' in response_data
    assert 'already exists' in response_data['detail'].lower()


@then('both applications should exist with different IDs')
def step_both_applications_exist(context):
    """Verify both applications exist with different IDs."""
    behave_context = get_context(context)
    
    # Get the created applications from context
    apps = [entity for key, entity in behave_context.created_entities.items() if key.startswith('app_')]
    assert len(apps) >= 2
    
    # Verify they have different IDs
    ids = [app['id'] for app in apps]
    assert len(set(ids)) == len(ids), "Applications should have unique IDs"


@then('I should receive all {count:d} applications')
def step_receive_all_applications(context, count):
    """Verify we receive the expected number of applications."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 200
    
    response_data = behave_context.response.json()
    assert len(response_data) == count


@then('I should receive {count:d} applications')
def step_receive_count_applications(context, count):
    """Verify we receive the expected number of applications."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 200
    
    response_data = behave_context.response.json()
    assert len(response_data) == count


@then('I should receive {count:d} different applications')
def step_receive_different_applications(context, count):
    """Verify we receive different applications than the first page."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 200
    
    first_page = behave_context.test_data.get('first_page', [])
    second_page = behave_context.response.json()
    
    assert len(second_page) == count
    
    # Verify no overlap between pages
    first_page_ids = {app['id'] for app in first_page}
    second_page_ids = {app['id'] for app in second_page}
    
    overlap = first_page_ids.intersection(second_page_ids)
    assert len(overlap) == 0, "Pages should not have overlapping applications"


@then('the request should fail with unauthorized error')
def step_request_fails_unauthorized(context):
    """Verify request fails with unauthorized error."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 401


@when('I try to create an application')
def step_try_create_application(context):
    """Try to create an application."""
    behave_context = get_context(context)

    app_data = {
        "name": "Unauthorized Test",
        "environment": "test"
    }

    async def try_create():
        response = await behave_context.client.post("/api/v1/applications/", json=app_data)
        behave_context.response = response

    run_async(context, try_create())


@when('I try to list applications')
def step_try_list_applications(context):
    """Try to list applications."""
    behave_context = get_context(context)

    async def try_list():
        response = await behave_context.client.get("/api/v1/applications/")
        behave_context.response = response

    run_async(context, try_list())
