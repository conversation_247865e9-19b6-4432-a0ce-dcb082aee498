"""Step definitions for CVE management features."""

from datetime import datetime
from behave import given, when, then

from tests.behave.environment import get_context, run_async


@given('the following CVEs exist in the system')
def step_cves_exist_in_system(context):
    """Create CVEs that exist in the system."""
    behave_context = get_context(context)
    
    async def create_cves():
        from src.cve_feed_service.services.cve_service import CVEService
        from src.cve_feed_service.schemas.cve import CVECreate
        
        cve_service = CVEService(behave_context.db_session)
        
        for row in context.table:
            cve_data = CVECreate(
                cve_id=row['cve_id'],
                description=f"Test CVE {row['cve_id']}",
                cvss_v3_score=float(row.get('cvss_v3_score', 0.0)),
                cvss_v3_severity=row.get('cvss_v3_severity', 'MEDIUM'),
                published_date=datetime.utcnow(),
                last_modified_date=datetime.utcnow()
            )
            
            cve = await cve_service.create_cve(cve_data)
            behave_context.created_entities[f'cve_{row["cve_id"]}'] = cve
        
        await behave_context.db_session.commit()
    
    run_async(context, create_cves())


@given('{count:d} CVEs exist in the system')
def step_count_cves_exist(context, count):
    """Create a specific number of CVEs."""
    behave_context = get_context(context)
    
    async def create_count_cves():
        from src.cve_feed_service.services.cve_service import CVEService
        from src.cve_feed_service.schemas.cve import CVECreate
        
        cve_service = CVEService(behave_context.db_session)
        
        for i in range(count):
            cve_data = CVECreate(
                cve_id=f"CVE-2023-{i+1:04d}",
                description=f"Test CVE number {i+1}",
                cvss_v3_score=5.0 + (i % 5),  # Vary scores
                cvss_v3_severity="MEDIUM",
                published_date=datetime.utcnow(),
                last_modified_date=datetime.utcnow()
            )
            
            await cve_service.create_cve(cve_data)
        
        await behave_context.db_session.commit()
    
    run_async(context, create_count_cves())


@given('a CVE "{cve_id}" exists with the following details')
def step_cve_exists_with_details(context, cve_id):
    """Create a CVE with specific details."""
    behave_context = get_context(context)
    
    # Convert table to dictionary
    cve_details = {}
    for row in context.table:
        cve_details[row['description']] = row['A specific test vulnerability']
    
    async def create_detailed_cve():
        from src.cve_feed_service.services.cve_service import CVEService
        from src.cve_feed_service.schemas.cve import CVECreate
        
        cve_service = CVEService(behave_context.db_session)
        
        cve_data = CVECreate(
            cve_id=cve_id,
            description=cve_details.get('description', f"Test CVE {cve_id}"),
            cvss_v3_score=float(cve_details.get('cvss_v3_score', 5.0)),
            cvss_v3_vector=cve_details.get('cvss_v3_vector', 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:L/A:L'),
            cvss_v3_severity=cve_details.get('cvss_v3_severity', 'MEDIUM'),
            published_date=datetime.utcnow(),
            last_modified_date=datetime.utcnow()
        )
        
        cve = await cve_service.create_cve(cve_data)
        behave_context.created_entities[f'cve_{cve_id}'] = cve
        await behave_context.db_session.commit()
    
    run_async(context, create_detailed_cve())


@given('an application "{app_name}" exists with ID stored as "{id_key}"')
def step_application_exists_with_stored_id(context, app_name, id_key):
    """Create an application and store its ID."""
    behave_context = get_context(context)
    
    app_data = {
        "name": app_name,
        "environment": "test",
        "description": f"Application {app_name} for CVE testing"
    }
    
    async def create_app_with_stored_id():
        response = await behave_context.client.post("/api/v1/applications/", json=app_data)
        assert response.status_code == 201
        
        app_response = response.json()
        behave_context.test_data[id_key] = app_response['id']
        behave_context.created_entities[f'app_{app_name}'] = app_response
    
    run_async(context, create_app_with_stored_id())


@given('CVEs exist with different publication dates')
def step_cves_with_different_dates(context):
    """Create CVEs with different publication dates."""
    behave_context = get_context(context)
    
    async def create_dated_cves():
        from src.cve_feed_service.services.cve_service import CVEService
        from src.cve_feed_service.schemas.cve import CVECreate
        
        cve_service = CVEService(behave_context.db_session)
        
        for row in context.table:
            pub_date = datetime.strptime(row['published_date'], '%Y-%m-%d')
            
            cve_data = CVECreate(
                cve_id=row['cve_id'],
                description=f"CVE published on {row['published_date']}",
                published_date=pub_date,
                last_modified_date=pub_date,
                cvss_v3_score=5.0,
                cvss_v3_severity="MEDIUM"
            )
            
            cve = await cve_service.create_cve(cve_data)
            behave_context.created_entities[f'cve_{row["cve_id"]}'] = cve
        
        await behave_context.db_session.commit()
    
    run_async(context, create_dated_cves())


@when('I request the list of all CVEs')
def step_request_all_cves(context):
    """Request the list of all CVEs."""
    behave_context = get_context(context)
    
    async def list_all_cves():
        response = await behave_context.client.get("/api/v1/cves/")
        behave_context.response = response
    
    run_async(context, list_all_cves())


@when('I request CVEs filtered by "{severity}" severity')
def step_request_cves_by_severity(context, severity):
    """Request CVEs filtered by severity."""
    behave_context = get_context(context)
    
    async def list_cves_by_severity():
        response = await behave_context.client.get(f"/api/v1/cves/?severity={severity}")
        behave_context.response = response
    
    run_async(context, list_cves_by_severity())


@when('I request the first page with {per_page:d} CVEs per page')
def step_request_first_page_cves(context, per_page):
    """Request the first page of CVEs."""
    behave_context = get_context(context)
    
    async def get_first_page_cves():
        response = await behave_context.client.get(f"/api/v1/cves/?limit={per_page}&offset=0")
        behave_context.response = response
        behave_context.test_data['first_page_cves'] = response.json() if response.status_code == 200 else None
    
    run_async(context, get_first_page_cves())


@when('I request the third page with {per_page:d} CVEs per page')
def step_request_third_page_cves(context, per_page):
    """Request the third page of CVEs."""
    behave_context = get_context(context)
    
    async def get_third_page_cves():
        offset = per_page * 2  # Third page
        response = await behave_context.client.get(f"/api/v1/cves/?limit={per_page}&offset={offset}")
        behave_context.response = response
    
    run_async(context, get_third_page_cves())


@when('I request the CVE "{cve_id}"')
def step_request_specific_cve(context, cve_id):
    """Request a specific CVE by ID."""
    behave_context = get_context(context)
    
    async def get_specific_cve():
        response = await behave_context.client.get(f"/api/v1/cves/{cve_id}")
        behave_context.response = response
    
    run_async(context, get_specific_cve())


@when('I request the tailored CVE feed')
def step_request_tailored_feed(context):
    """Request the tailored CVE feed."""
    behave_context = get_context(context)
    
    async def get_tailored_feed():
        response = await behave_context.client.get("/api/v1/cves/feed")
        behave_context.response = response
    
    run_async(context, get_tailored_feed())


@when('I request the tailored CVE feed for application "{id_key}"')
def step_request_tailored_feed_for_app(context, id_key):
    """Request tailored CVE feed for specific application."""
    behave_context = get_context(context)
    app_id = behave_context.test_data.get(id_key)
    
    async def get_tailored_feed_for_app():
        response = await behave_context.client.get(f"/api/v1/cves/feed?application_id={app_id}")
        behave_context.response = response
    
    run_async(context, get_tailored_feed_for_app())


@when('I request CVEs published after "{date_str}"')
def step_request_cves_after_date(context, date_str):
    """Request CVEs published after a specific date."""
    behave_context = get_context(context)
    
    async def get_cves_after_date():
        response = await behave_context.client.get(f"/api/v1/cves/?published_after={date_str}")
        behave_context.response = response
    
    run_async(context, get_cves_after_date())


@when('I search for CVEs containing "{keyword}"')
def step_search_cves_by_keyword(context, keyword):
    """Search for CVEs containing a keyword."""
    behave_context = get_context(context)
    
    async def search_cves():
        response = await behave_context.client.get(f"/api/v1/cves/?search={keyword}")
        behave_context.response = response
    
    run_async(context, search_cves())


@when('I request CVE statistics')
def step_request_cve_statistics(context):
    """Request CVE statistics."""
    behave_context = get_context(context)
    
    async def get_cve_stats():
        response = await behave_context.client.get("/api/v1/cves/statistics")
        behave_context.response = response
    
    run_async(context, get_cve_stats())


@then('I should receive all {count:d} CVEs')
def step_receive_all_cves(context, count):
    """Verify we receive the expected number of CVEs."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 200
    
    response_data = behave_context.response.json()
    cves = response_data.get('cves', response_data)  # Handle different response formats
    assert len(cves) == count


@then('each CVE should contain complete vulnerability details')
def step_cves_contain_complete_details(context):
    """Verify CVEs contain complete details."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    cves = response_data.get('cves', response_data)
    
    for cve in cves:
        assert 'cve_id' in cve
        assert 'description' in cve
        assert 'published_date' in cve


@then('all returned CVEs should have "{severity}" severity')
def step_all_cves_have_severity(context, severity):
    """Verify all returned CVEs have the specified severity."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    cves = response_data.get('cves', response_data)
    
    for cve in cves:
        assert cve.get('cvss_v3_severity') == severity


@then('I should receive {count:d} CVEs')
def step_receive_count_cves(context, count):
    """Verify we receive the expected number of CVEs."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 200
    
    response_data = behave_context.response.json()
    cves = response_data.get('cves', response_data)
    assert len(cves) == count


@then('the response should indicate there are more results')
def step_response_indicates_more_results(context):
    """Verify response indicates there are more results."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    
    # Check for pagination indicators
    assert response_data.get('has_more', True) is True or response_data.get('total', 0) > len(response_data.get('cves', []))


@then('the response should indicate this is the last page')
def step_response_indicates_last_page(context):
    """Verify response indicates this is the last page."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    
    # Check for pagination indicators
    assert response_data.get('has_more', False) is False


@then('I should receive the CVE details')
def step_receive_cve_details(context):
    """Verify we receive CVE details."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 200
    
    response_data = behave_context.response.json()
    assert 'cve_id' in response_data


@then('the CVE description should be "{description}"')
def step_cve_description_should_be(context, description):
    """Verify CVE description matches expected value."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    assert response_data.get('description') == description


@then('the CVSS v3 score should be {score:f}')
def step_cvss_score_should_be(context, score):
    """Verify CVSS v3 score matches expected value."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    assert response_data.get('cvss_v3_score') == score


@then('the request should fail with not found error')
def step_request_fails_not_found(context):
    """Verify request fails with not found error."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 404


@then('I should receive a list of relevant CVEs')
def step_receive_relevant_cves(context):
    """Verify we receive relevant CVEs."""
    behave_context = get_context(context)
    assert behave_context.response.status_code == 200
    
    response_data = behave_context.response.json()
    assert 'cves' in response_data
    assert isinstance(response_data['cves'], list)


@then('the response should include pagination information')
def step_response_includes_pagination(context):
    """Verify response includes pagination information."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    
    # Check for common pagination fields
    pagination_fields = ['limit', 'offset', 'total', 'has_more']
    assert any(field in response_data for field in pagination_fields)


@then('the response should include total count')
def step_response_includes_total_count(context):
    """Verify response includes total count."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    assert 'total' in response_data


@then('the CVEs should be "{cve_id1}" and "{cve_id2}"')
def step_cves_should_be_specific_ids(context, cve_id1, cve_id2):
    """Verify CVEs match specific IDs."""
    behave_context = get_context(context)
    response_data = behave_context.response.json()
    cves = response_data.get('cves', response_data)
    
    cve_ids = [cve['cve_id'] for cve in cves]
    assert cve_id1 in cve_ids
    assert cve_id2 in cve_ids
