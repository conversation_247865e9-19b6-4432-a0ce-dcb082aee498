[behave]
paths = tests/behave/features
step_definitions = tests/behave/steps
format = pretty
show_timings = true
show_skipped = false
logging_level = INFO
logging_format = %(levelname)s:%(name)s:%(message)s

# Advanced configuration
default_tags = ~@wip,~@manual
junit = true
junit_directory = reports/junit
summary = true
snippets = true

# Tag expressions for different test suites
# Run with: behave --tags=@smoke
# Run with: behave --tags=@regression
# Run with: behave --tags="@api and not @slow"

# Formatters
format = pretty
outfiles = reports/behave-pretty.txt

# Additional formatters for CI/CD
# format = json
# outfile = reports/behave-results.json

# Performance testing
# format = progress
# show_timings = true
