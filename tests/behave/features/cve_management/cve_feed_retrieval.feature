Feature: CVE Feed Retrieval
  As a security analyst
  I want to retrieve tailored CVE feeds based on my application components
  So that I can focus on vulnerabilities that actually affect my systems

  Background:
    Given I am logged in as a security analyst
    And the system contains CVE data from the last 6 months
    And I have applications with registered components
    And I am on the CVE feed page

  @api @cve-feed @critical
  Scenario: Retrieve default CVE feed successfully
    When I request the CVE feed without any filters
    Then the API should respond with status code 200
    And I should receive a paginated list of CVEs
    And the response should include:
      | field     | description                    |
      | cves      | Array of CVE objects           |
      | total     | Total number of CVEs           |
      | limit     | Number of CVEs per page        |
      | offset    | Current page offset            |
      | has_more  | Boolean indicating more pages  |
    And CVEs should be sorted by published date (newest first)
    And each CVE should include essential fields:
      | field         | type   | required |
      | id            | string | yes      |
      | title         | string | yes      |
      | severity      | string | yes      |
      | cvss_score    | number | yes      |
      | published_date| string | yes      |
      | description   | string | yes      |

  @api @cve-feed @filtering
  Scenario: Filter CVE feed by severity level
    Given I want to see only high-severity vulnerabilities
    When I request the CVE feed with severity filter "HIGH"
    Then the API should respond with status code 200
    And all returned CVEs should have severity "HIGH"
    And the total count should reflect only high-severity CVEs
    And the filter should be reflected in the response metadata
    When I request the CVE feed with severity filter "CRITICAL"
    Then all returned CVEs should have severity "CRITICAL"
    And the count should be different from the HIGH filter results

  @api @cve-feed @application-filtering
  Scenario: Filter CVE feed by application
    Given I have an application "WebApp-Production" with known components
    When I request the CVE feed filtered by application "WebApp-Production"
    Then the API should respond with status code 200
    And all returned CVEs should affect components in "WebApp-Production"
    And the response should include application context
    And CVEs not affecting this application should be excluded
    When I request the CVE feed for a different application
    Then I should get a different set of CVEs

  @api @cve-feed @date-filtering
  Scenario: Filter CVE feed by publication date range
    Given I want to see CVEs from a specific time period
    When I request the CVE feed with date range "last_30_days"
    Then the API should respond with status code 200
    And all returned CVEs should be published within the last 30 days
    And older CVEs should be excluded from results
    When I request the CVE feed with date range "last_7_days"
    Then all returned CVEs should be published within the last 7 days
    And the count should be less than or equal to the 30-day results

  @api @cve-feed @pagination
  Scenario: CVE feed pagination works correctly
    Given the CVE feed contains more than 50 CVEs
    When I request the first page with limit 20
    Then the API should respond with status code 200
    And I should receive exactly 20 CVEs
    And the response should indicate has_more is true
    And the offset should be 0
    When I request the second page with offset 20 and limit 20
    Then I should receive the next 20 CVEs
    And the CVEs should be different from the first page
    And the offset should be 20

  @api @cve-feed @sorting
  Scenario: CVE feed supports different sorting options
    When I request the CVE feed sorted by "published_date" descending
    Then the CVEs should be ordered by publication date (newest first)
    When I request the CVE feed sorted by "cvss_score" descending
    Then the CVEs should be ordered by CVSS score (highest first)
    When I request the CVE feed sorted by "severity" descending
    Then the CVEs should be ordered by severity (CRITICAL, HIGH, MEDIUM, LOW)

  @ux @cve-feed @interface
  Scenario: CVE feed interface displays correctly
    Given I am viewing the CVE feed page
    Then I should see a list of CVE cards
    And each CVE card should display:
      | element       | description                    |
      | cve_id        | CVE identifier prominently     |
      | title         | Vulnerability title            |
      | severity_badge| Color-coded severity indicator |
      | cvss_score    | Numerical score                |
      | published_date| Human-readable date            |
      | description   | Truncated description          |
    And I should see pagination controls at the bottom
    And I should see filter options in a sidebar

  @ux @cve-feed @filtering-interface
  Scenario: CVE feed filtering interface is intuitive
    Given I am viewing the CVE feed page
    When I open the filter sidebar
    Then I should see filter options for:
      | filter_type   | options                        |
      | severity      | CRITICAL, HIGH, MEDIUM, LOW    |
      | application   | List of my applications        |
      | date_range    | Predefined and custom ranges   |
      | component     | List of components             |
    When I select a severity filter
    Then the CVE list should update immediately
    And the filter should be visually indicated as active
    And I should see the count of filtered results

  @ux @cve-feed @responsive
  Scenario: CVE feed is responsive across devices
    Given I am viewing the CVE feed on different screen sizes
    When I access the feed on mobile (375px width)
    Then the CVE cards should stack vertically
    And the filter sidebar should be collapsible
    And all information should remain readable
    And touch targets should be appropriately sized
    When I access the feed on tablet (768px width)
    Then the layout should adapt to show 2 columns
    When I access the feed on desktop (1200px+ width)
    Then the layout should show the optimal number of columns
    And the filter sidebar should be permanently visible

  @ux @cve-feed @loading-states
  Scenario: CVE feed shows appropriate loading states
    Given I am loading the CVE feed
    When the data is being fetched
    Then I should see skeleton loading placeholders
    And the placeholders should match the CVE card layout
    And filter controls should be disabled during loading
    When the data loads successfully
    Then the loading placeholders should be replaced with actual CVE cards
    And all interactive elements should become enabled
    When a filter is applied
    Then I should see a loading indicator during the filter operation

  @ux @cve-feed @error-handling
  Scenario: CVE feed handles errors gracefully
    Given I am viewing the CVE feed
    When the API request fails due to network error
    Then I should see an error message explaining the issue
    And I should see a "Retry" button
    And the previous data should remain visible if available
    When I click the "Retry" button
    Then the system should attempt to reload the data
    When the API returns an error response
    Then I should see an appropriate error message
    And I should have options to modify my request

  @performance @cve-feed @response-time
  Scenario: CVE feed loads within performance requirements
    Given I am requesting the CVE feed
    When I load the default feed (50 CVEs)
    Then the response should complete within 2 seconds
    When I apply filters to the feed
    Then the filtered results should load within 1 second
    When I navigate between pages
    Then pagination should respond within 500ms
    And the user experience should feel responsive

  @security @cve-feed @access-control
  Scenario: CVE feed respects user permissions
    Given I am logged in with specific role permissions
    When I request the CVE feed as a "read_only" user
    Then I should only see CVEs I have permission to view
    And sensitive CVE details should be filtered appropriately
    When I request the CVE feed as a "security_analyst" user
    Then I should see comprehensive CVE information
    And I should have access to all relevant filtering options
    When I request the CVE feed as an "admin" user
    Then I should see all CVEs including administrative data

  @integration @cve-feed @real-time-updates
  Scenario: CVE feed supports real-time updates
    Given I am viewing the CVE feed with real-time updates enabled
    When new CVE data is ingested into the system
    Then I should see a notification of new CVEs available
    And I should have an option to refresh the feed
    When I choose to refresh
    Then the new CVEs should appear in the appropriate position
    And the total count should be updated
    When I disable real-time updates
    Then I should not receive notifications of new CVEs

  @api @cve-feed @export
  Scenario: CVE feed supports data export
    Given I have applied specific filters to the CVE feed
    When I request to export the filtered CVE data
    Then the API should respond with status code 200
    And I should receive the data in the requested format (CSV, JSON, PDF)
    And the exported data should match the current filter criteria
    And the export should include all relevant CVE fields
    When I request export without filters
    Then I should receive all CVEs I have permission to view

  @api @cve-feed @validation
  Scenario Outline: CVE feed API validates input parameters
    When I request the CVE feed with invalid parameter "<parameter>" set to "<value>"
    Then the API should respond with status code 400
    And I should receive a validation error message "<error_message>"

    Examples:
      | parameter     | value           | error_message                    |
      | severity      | INVALID         | Invalid severity level           |
      | limit         | -1              | Limit must be positive           |
      | limit         | 1001            | Limit exceeds maximum allowed    |
      | offset        | -1              | Offset must be non-negative      |
      | date_range    | invalid_range   | Invalid date range format       |
      | application_id| invalid-uuid    | Invalid application ID format    |

  @integration @cve-feed @component-correlation
  Scenario: CVE feed correctly correlates with application components
    Given I have an application with specific components:
      | component | version | cpe_string                           |
      | nginx     | 1.20.1  | cpe:2.3:a:nginx:nginx:1.20.1:*:*:*  |
      | openssl   | 1.1.1k  | cpe:2.3:a:openssl:openssl:1.1.1k:*  |
    When I request the CVE feed for this application
    Then I should only see CVEs that affect nginx 1.20.1 or openssl 1.1.1k
    And CVEs affecting other components should be excluded
    And the component correlation should be accurate
    When I update a component version
    Then the CVE feed should reflect the new component vulnerabilities
