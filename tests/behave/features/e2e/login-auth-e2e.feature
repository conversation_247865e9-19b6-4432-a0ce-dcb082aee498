Feature: End-to-End Login Authentication Flow
  As a security analyst
  I want to complete the entire login process through the web interface
  So that I can access the CVE Feed Service with full functionality

  Background:
    Given the CVE Feed Service web application is running
    And the backend API is available
    And test users exist in the database
    And I am using a web browser

  @e2e @playwright @authentication @critical
  Scenario: Complete successful login workflow
    Given I navigate to the login page
    And I can see the login form is loaded
    When I enter email "<EMAIL>" in the email field
    And I enter password "SecurePass123!" in the password field
    And I click the "Sign In" button
    Then I should see a loading spinner
    And the sign in button should be disabled
    When the authentication completes
    Then I should be redirected to "/dashboard"
    And I should see the dashboard page loaded
    And I should see my user name "Security Analyst" in the header
    And I should see the logout option in the user menu
    And the browser should store the authentication token securely

  @e2e @playwright @authentication @error-handling
  Scenario: Login with invalid credentials shows proper error
    Given I navigate to the login page
    When I enter email "<EMAIL>" in the email field
    And I enter password "wrongpassword" in the password field
    And I click the "Sign In" button
    Then I should see a loading spinner briefly
    When the authentication fails
    Then I should see an error message "Invalid email or password"
    And I should remain on the login page
    And the email field should retain the entered value
    And the password field should be cleared
    And the error message should be accessible to screen readers
    And I should be able to try logging in again

  @e2e @playwright @authentication @validation
  Scenario: Form validation prevents submission with empty fields
    Given I navigate to the login page
    When I click the "Sign In" button without entering any credentials
    Then I should see validation errors:
      | field    | error_message        |
      | email    | Email is required    |
      | password | Password is required |
    And the form should not be submitted
    And no API request should be made
    When I enter email "<EMAIL>"
    Then the email validation error should disappear
    When I enter password "password123"
    Then the password validation error should disappear
    And the "Sign In" button should become enabled

  @e2e @playwright @authentication @accessibility
  Scenario: Login form accessibility compliance
    Given I navigate to the login page
    When I inspect the login form for accessibility
    Then all form fields should have proper labels
    And the form should be navigable using only the keyboard
    And I should be able to tab through all interactive elements
    And the tab order should be logical (email → password → sign in → forgot password)
    And focus indicators should be clearly visible
    When I use a screen reader
    Then all form elements should be properly announced
    And error messages should be associated with their fields
    And the form should have appropriate ARIA attributes

  @e2e @playwright @authentication @responsive
  Scenario: Login form works across different screen sizes
    Given I navigate to the login page
    When I resize the browser to mobile dimensions (375x667)
    Then the login form should be fully visible and functional
    And all form elements should be easily tappable
    And the layout should adapt to the smaller screen
    When I resize to tablet dimensions (768x1024)
    Then the form should maintain proper proportions
    When I resize to desktop dimensions (1920x1080)
    Then the form should be centered and well-proportioned
    And all functionality should work consistently across sizes

  @e2e @playwright @authentication @remember-me
  Scenario: Remember me functionality persists session
    Given I navigate to the login page
    When I enter valid credentials
    And I check the "Remember me" checkbox
    And I click the "Sign In" button
    And I am successfully logged in
    When I close the browser completely
    And I reopen the browser and navigate to the application
    Then I should still be logged in
    And I should be on the dashboard page
    And my session should be active
    When I uncheck "Remember me" and log out and log back in
    And I close and reopen the browser
    Then I should need to log in again

  @e2e @playwright @authentication @forgot-password
  Scenario: Forgot password link navigation and functionality
    Given I navigate to the login page
    When I click the "Forgot Password?" link
    Then I should be navigated to the password reset page
    And I should see a password reset form
    When I enter email "<EMAIL>" in the reset form
    And I click the "Send Reset Link" button
    Then I should see a confirmation message
    And I should receive instructions to check my email
    When I navigate back to the login page
    Then I should see the normal login form again

  @e2e @playwright @authentication @session-management
  Scenario: Session timeout and renewal handling
    Given I am logged in to the application
    And I am on the dashboard page
    When my session token expires
    And I try to navigate to a protected page
    Then I should be automatically redirected to the login page
    And I should see a message "Your session has expired"
    When I log in again with valid credentials
    Then I should be redirected back to the page I was trying to access
    And my session should be renewed

  @e2e @playwright @authentication @security
  Scenario: Security measures are properly implemented
    Given I navigate to the login page
    When I inspect the network traffic during login
    Then all requests should be made over HTTPS
    And the password should not be visible in network logs
    And the authentication token should be stored securely
    When I check the browser developer tools
    Then sensitive information should not be exposed in console logs
    And the authentication token should not be accessible via JavaScript
    And proper CSRF protection should be in place

  @e2e @playwright @authentication @multiple-tabs
  Scenario: Authentication state synchronization across tabs
    Given I am logged in to the application in one browser tab
    When I open a new tab and navigate to the application
    Then I should already be logged in in the new tab
    And I should see the dashboard without needing to log in again
    When I log out from the first tab
    And I refresh the second tab
    Then I should be logged out in the second tab as well
    And I should be redirected to the login page

  @e2e @playwright @authentication @browser-compatibility
  Scenario: Login works across different browsers
    Given I test the login functionality in different browsers
    When I log in using Chrome
    Then the login process should work correctly
    When I log in using Firefox
    Then the login process should work correctly
    When I log in using Safari
    Then the login process should work correctly
    And the user experience should be consistent across browsers
    And all security features should function properly

  @e2e @playwright @authentication @performance
  Scenario: Login performance meets requirements
    Given I navigate to the login page
    When I measure the page load time
    Then the login page should load within 2 seconds
    When I submit valid credentials
    And I measure the authentication time
    Then the login process should complete within 3 seconds
    And the redirect to dashboard should happen within 1 second
    And the overall login flow should feel responsive

  @e2e @playwright @authentication @error-recovery
  Scenario: Recovery from network errors during login
    Given I navigate to the login page
    When I enter valid credentials
    And I simulate a network error during login
    Then I should see an appropriate error message
    And I should have an option to retry
    When the network connection is restored
    And I click the retry button
    Then the login should proceed normally
    And I should be successfully authenticated

  @e2e @playwright @authentication @integration
  Scenario: Login integrates properly with the rest of the application
    Given I successfully log in to the application
    When I navigate to different sections of the application
    Then my authentication should persist across all pages
    And I should have access to features based on my user role
    And protected API calls should include my authentication token
    When I perform actions that require authentication
    Then they should work without requiring re-authentication
    And my user context should be maintained throughout the session
