Feature: User Login Authentication
  As a security analyst
  I want to securely log into the CVE Feed Service
  So that I can access vulnerability management features

  Background:
    Given the CVE Feed Service is running
    And the database contains test users
    And I am on the login page

  @api @authentication @critical
  Scenario: Successful login with valid credentials
    Given I have valid user credentials
    When I submit the login form with email "<EMAIL>" and password "SecurePass123!"
    Then the API should respond with status code 200
    And I should receive a valid JWT token
    And I should receive user profile information
    And I should be redirected to the dashboard
    And the token should be stored securely

  @api @authentication @critical
  Scenario: Failed login with invalid credentials
    Given I have invalid user credentials
    When I submit the login form with email "<EMAIL>" and password "wrongpassword"
    Then the API should respond with status code 401
    And I should receive an error message "Invalid credentials"
    And no JWT token should be provided
    And I should remain on the login page
    And no user data should be stored

  @api @authentication @security
  Scenario: Account lockout after multiple failed attempts
    Given I have valid user credentials for "<EMAIL>"
    When I attempt to login with wrong password 5 times
    Then the account should be temporarily locked
    And subsequent login attempts should return status code 423
    And I should receive an error message "Account temporarily locked"
    And the lockout should expire after 15 minutes

  @api @authentication @validation
  Scenario Outline: Login form validation
    When I submit the login form with email "<email>" and password "<password>"
    Then the API should respond with status code 400
    And I should receive validation error "<error_message>"

    Examples:
      | email              | password    | error_message                |
      |                    | password123 | Email is required            |
      | invalid-email      | password123 | Invalid email format         |
      | <EMAIL>   |             | Password is required         |
      | <EMAIL>   | 123         | Password must be at least 8 characters |

  @api @authentication @token
  Scenario: Token refresh functionality
    Given I have a valid but expiring JWT token
    When I request a token refresh
    Then the API should respond with status code 200
    And I should receive a new valid JWT token
    And the old token should be invalidated
    And my session should continue seamlessly

  @api @authentication @token
  Scenario: Token refresh with invalid token
    Given I have an invalid or expired JWT token
    When I request a token refresh
    Then the API should respond with status code 401
    And I should receive an error message "Invalid or expired token"
    And I should be redirected to the login page

  @ux @authentication @usability
  Scenario: Login form user experience
    Given I am viewing the login form
    Then I should see email and password input fields
    And I should see a "Sign In" button
    And I should see a "Forgot Password?" link
    And I should see a "Remember Me" checkbox
    And the form should have proper accessibility labels
    And the password field should mask input

  @ux @authentication @responsive
  Scenario: Login form responsive design
    Given I am viewing the login form on different screen sizes
    When I resize the browser window
    Then the login form should adapt to mobile screens
    And all form elements should remain accessible
    And the layout should maintain proper spacing
    And touch targets should be appropriately sized

  @ux @authentication @loading
  Scenario: Login loading states
    Given I am on the login page
    When I submit valid credentials
    Then I should see a loading spinner
    And the submit button should be disabled
    And I should see "Signing in..." text
    When the authentication completes
    Then the loading state should disappear
    And I should be redirected to the dashboard

  @ux @authentication @error-handling
  Scenario: Login error display
    Given I am on the login page
    When I submit invalid credentials
    Then I should see an error message displayed prominently
    And the error message should be accessible to screen readers
    And the form fields should maintain their values
    And the error should disappear when I start typing again

  @security @authentication @csrf
  Scenario: CSRF protection on login
    Given I am on the login page
    When I inspect the login form
    Then the form should include CSRF protection
    And login requests without valid CSRF tokens should be rejected
    And the API should respond with status code 403 for invalid CSRF

  @security @authentication @rate-limiting
  Scenario: Rate limiting on login attempts
    Given I am making multiple login attempts
    When I exceed the rate limit of 10 attempts per minute
    Then subsequent requests should be rate limited
    And the API should respond with status code 429
    And I should receive a "Too many requests" error message
    And I should be able to retry after the rate limit window

  @integration @authentication @session
  Scenario: Session management integration
    Given I have successfully logged in
    When I navigate to different pages in the application
    Then my authentication state should persist
    And I should not need to re-authenticate
    When I close and reopen the browser
    And I have "Remember Me" enabled
    Then I should still be authenticated
    When I explicitly log out
    Then my session should be completely cleared
