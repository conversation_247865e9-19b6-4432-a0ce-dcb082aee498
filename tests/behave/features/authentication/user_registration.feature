Feature: User Registration
  As a new user
  I want to create an account in the CVE Feed Service
  So that I can access vulnerability management features

  Background:
    Given the CVE Feed Service is running
    And the registration feature is enabled
    And I am on the registration page

  @api @registration @critical
  Scenario: Successful user registration
    Given I have valid registration information
    When I submit the registration form with:
      | field           | value                    |
      | email           | <EMAIL>      |
      | password        | SecurePass123!           |
      | confirm_password| SecurePass123!           |
      | first_name      | John                     |
      | last_name       | Doe                      |
      | organization    | ACME Corp                |
    Then the API should respond with status code 201
    And a new user account should be created
    And a verification email should be sent
    And I should see a "Check your email" message
    And I should be redirected to the verification pending page

  @api @registration @validation
  Scenario: Registration with existing email
    Given a user already exists with email "<EMAIL>"
    When I submit the registration form with email "<EMAIL>"
    Then the API should respond with status code 409
    And I should receive an error message "Email already registered"
    And no new account should be created
    And I should see a "Sign in instead" link

  @api @registration @validation
  Scenario Outline: Registration form validation
    When I submit the registration form with invalid data:
      | field           | value        |
      | email           | <email>      |
      | password        | <password>   |
      | confirm_password| <confirm>    |
      | first_name      | <first_name> |
      | last_name       | <last_name>  |
    Then the API should respond with status code 400
    And I should receive validation error "<error_message>"

    Examples:
      | email              | password      | confirm       | first_name | last_name | error_message                    |
      |                    | SecurePass123!| SecurePass123!| John       | Doe       | Email is required                |
      | invalid-email      | SecurePass123!| SecurePass123!| John       | Doe       | Invalid email format             |
      | <EMAIL>   |               | SecurePass123!| John       | Doe       | Password is required             |
      | <EMAIL>   | weak          | weak          | John       | Doe       | Password too weak                |
      | <EMAIL>   | SecurePass123!| DifferentPass!| John       | Doe       | Passwords do not match           |
      | <EMAIL>   | SecurePass123!| SecurePass123!|            | Doe       | First name is required           |
      | <EMAIL>   | SecurePass123!| SecurePass123!| John       |           | Last name is required            |

  @api @registration @email-verification
  Scenario: Email verification process
    Given I have registered with email "<EMAIL>"
    And a verification email has been sent
    When I click the verification link in the email
    Then the API should respond with status code 200
    And my account should be marked as verified
    And I should be redirected to the login page
    And I should see a "Account verified successfully" message

  @api @registration @email-verification
  Scenario: Invalid verification token
    Given I have a registration verification link
    When I click an invalid or expired verification token
    Then the API should respond with status code 400
    And I should receive an error message "Invalid or expired verification token"
    And I should see an option to resend verification email

  @api @registration @email-verification
  Scenario: Resend verification email
    Given I have registered but not verified my email
    When I request to resend the verification email
    Then the API should respond with status code 200
    And a new verification email should be sent
    And I should see a "Verification email sent" message
    And the previous verification token should be invalidated

  @ux @registration @password-strength
  Scenario: Password strength indicator
    Given I am on the registration page
    When I start typing in the password field
    Then I should see a password strength indicator
    And the indicator should update in real-time
    When I enter "weak" password
    Then the indicator should show "Weak" in red
    When I enter "SecurePass123!" password
    Then the indicator should show "Strong" in green
    And I should see password requirements checklist

  @ux @registration @form-validation
  Scenario: Real-time form validation
    Given I am filling out the registration form
    When I enter an invalid email format
    Then I should see an error message below the email field
    When I correct the email format
    Then the error message should disappear
    When I enter mismatched passwords
    Then I should see "Passwords do not match" error
    When I match the passwords
    Then the error should disappear

  @ux @registration @accessibility
  Scenario: Registration form accessibility
    Given I am using a screen reader
    When I navigate the registration form
    Then all form fields should have proper labels
    And error messages should be announced
    And the form should be navigable with keyboard only
    And focus indicators should be visible
    And the form should have proper heading structure

  @ux @registration @responsive
  Scenario: Registration form responsive design
    Given I am viewing the registration form
    When I access it on different devices
    Then the form should be fully functional on mobile
    And all fields should be easily tappable
    And the layout should adapt to screen size
    And scrolling should work smoothly

  @security @registration @password-policy
  Scenario: Password policy enforcement
    Given I am creating a new password
    When I enter a password that doesn't meet requirements
    Then the system should reject weak passwords
    And I should see specific requirements:
      | requirement                    | status |
      | At least 8 characters          | ✗      |
      | Contains uppercase letter      | ✗      |
      | Contains lowercase letter      | ✓      |
      | Contains number                | ✗      |
      | Contains special character     | ✗      |
    When I meet all requirements
    Then all requirements should show as satisfied
    And the registration should be allowed to proceed

  @security @registration @data-protection
  Scenario: User data protection during registration
    Given I am registering a new account
    When I submit my personal information
    Then the data should be transmitted over HTTPS
    And passwords should be hashed before storage
    And sensitive data should not appear in logs
    And GDPR compliance notices should be displayed

  @integration @registration @workflow
  Scenario: Complete registration workflow
    Given I want to create a new account
    When I complete the registration process
    And I verify my email address
    And I log in for the first time
    Then I should be guided through initial setup
    And I should see a welcome tour
    And my account should have default permissions
    And I should be able to access basic features

  @api @registration @rate-limiting
  Scenario: Registration rate limiting
    Given I am attempting multiple registrations
    When I exceed the rate limit of 5 registrations per hour from the same IP
    Then subsequent registration attempts should be blocked
    And the API should respond with status code 429
    And I should receive a "Too many registration attempts" message

  @api @registration @username-availability
  Scenario: Username availability check
    Given I am filling out the registration form
    When I enter a username in the username field
    Then the system should check availability in real-time
    And I should see immediate feedback
    When I enter an available username
    Then I should see a green checkmark
    When I enter a taken username
    Then I should see "Username not available" message
    And I should see suggested alternatives
