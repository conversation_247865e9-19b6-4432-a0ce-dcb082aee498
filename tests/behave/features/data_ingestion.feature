Feature: CVE Data Ingestion from NVD
  As a system administrator
  I want to automatically ingest CVE data from the National Vulnerability Database
  So that the system has up-to-date vulnerability information

  Background:
    Given the CVE feed service is running
    And I have a clean database
    And I am an authenticated admin user

  <PERSON><PERSON><PERSON>: Manual CVE data ingestion
    Given the NVD API is available
    When I trigger a manual CVE data ingestion
    Then the ingestion process should start successfully
    And I should receive a confirmation message
    And the process should run in the background

  Scenario: Successful bulk CVE import
    Given the NVD API returns sample CVE data:
      | cve_id          | cvss_v3_score | severity | description                    |
      | CVE-2023-TEST1  | 9.8           | CRITICAL | Critical test vulnerability    |
      | CVE-2023-TEST2  | 7.5           | HIGH     | High severity test issue       |
      | CVE-2023-TEST3  | 4.3           | MEDIUM   | Medium severity test problem   |
    When I perform a bulk CVE import
    Then all 3 CVEs should be imported successfully
    And the CVEs should be stored in the database
    And each CVE should have complete metadata

  Scenario: Incremental CVE update
    Given existing CVEs in the database:
      | cve_id          | last_modified   |
      | CVE-2023-OLD    | 2023-01-01      |
      | CVE-2023-UPDATE | 2023-06-01      |
    And the NVD API returns updated CVE data:
      | cve_id          | last_modified   | cvss_v3_score |
      | CVE-2023-UPDATE | 2023-12-01      | 8.5           |
      | CVE-2023-NEW    | 2023-12-15      | 6.2           |
    When I perform an incremental CVE update
    Then the existing CVE "CVE-2023-UPDATE" should be updated
    And the new CVE "CVE-2023-NEW" should be added
    And the unchanged CVE "CVE-2023-OLD" should remain the same

  Scenario: Handle NVD API rate limiting
    Given the NVD API has rate limiting enabled
    And I have a large batch of CVEs to import
    When I start the bulk import process
    Then the system should respect the rate limits
    And the import should continue without errors
    And all CVEs should eventually be imported

  Scenario: Handle NVD API errors
    Given the NVD API is temporarily unavailable
    When I attempt to import CVE data
    Then the system should handle the error gracefully
    And the system should retry the request
    And I should receive an appropriate error message if retries fail

  Scenario: Parse CVSS v3 metrics
    Given the NVD API returns a CVE with CVSS v3 data:
      | cve_id         | CVE-2023-CVSS3                                    |
      | vector_string  | CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H     |
      | base_score     | 9.8                                               |
      | severity       | CRITICAL                                          |
    When I import this CVE
    Then the CVSS v3 score should be stored as 9.8
    And the CVSS v3 vector should be stored correctly
    And the severity should be "CRITICAL"

  Scenario: Parse CWE information
    Given the NVD API returns a CVE with CWE data:
      | cve_id    | CVE-2023-CWE     |
      | cwe_ids   | CWE-79, CWE-89   |
    When I import this CVE
    Then the CWE IDs should be stored as ["CWE-79", "CWE-89"]
    And the CWE information should be searchable

  Scenario: Parse CPE applicability data
    Given the NVD API returns a CVE with CPE data:
      | cve_id      | CVE-2023-CPE                              |
      | cpe_string  | cpe:2.3:a:apache:http_server:2.4.41:*    |
      | vulnerable  | true                                      |
    When I import this CVE
    Then the CPE applicability should be stored correctly
    And the CVE should be linked to the Apache HTTP Server component

  Scenario: Handle duplicate CVE imports
    Given a CVE "CVE-2023-DUPLICATE" already exists in the database
    When I import the same CVE again with updated information
    Then the existing CVE should be updated, not duplicated
    And the database should contain only one record for this CVE

  Scenario: Validate imported CVE data
    Given the NVD API returns CVE data with missing required fields
    When I attempt to import this invalid data
    Then the import should fail for the invalid CVE
    And valid CVEs in the same batch should still be imported
    And I should receive a report of validation errors

  Scenario: Monitor ingestion progress
    Given I start a large CVE import process
    When I check the ingestion status
    Then I should see the current progress
    And I should see the number of CVEs processed
    And I should see any errors that occurred

  Scenario: Schedule automatic ingestion
    Given I configure automatic CVE ingestion to run daily
    When the scheduled time arrives
    Then the system should automatically start ingestion
    And new CVEs should be imported without manual intervention
    And I should receive a summary report of the ingestion

  Scenario: Handle network connectivity issues
    Given the system loses network connectivity during ingestion
    When the network connection is restored
    Then the system should resume the ingestion process
    And no CVE data should be lost or duplicated

  Scenario: Import historical CVE data
    Given I want to import CVEs from the last 2 years
    When I start a historical data import
    Then the system should import CVEs in batches
    And the import should handle the large volume efficiently
    And all historical CVEs should be available for search

  Scenario: Verify data integrity after import
    Given I have imported a batch of CVEs
    When I verify the data integrity
    Then all imported CVEs should have valid IDs
    And all CVSS scores should be within valid ranges
    And all required fields should be populated
    And the database should be consistent
