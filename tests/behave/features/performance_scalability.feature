@performance @scalability @slow @regression
Feature: Performance and Scalability
  As a system architect
  I want to ensure the CVE feed service performs well under load
  So that it can handle enterprise-scale vulnerability management

  Background:
    Given the CVE feed service is running
    And I have a clean database
    And I am an authenticated user with role "IT_ADMIN"

  Scenario: High-volume CVE ingestion performance
    Given the NVD API returns 10,000 CVEs in batches
    And the system has performance monitoring enabled
    When I trigger bulk CVE ingestion
    Then the ingestion should complete within 30 minutes
    And memory usage should remain below 2GB
    And database connections should not exceed pool limits
    And no timeouts should occur during processing

  Scenario: Concurrent user load testing
    Given 100 concurrent users are accessing the system
    And each user performs typical operations:
      | operation           | frequency | expected_response_time |
      | List applications   | 40%       | < 200ms               |
      | View CVE details    | 30%       | < 500ms               |
      | Search CVEs         | 20%       | < 1000ms              |
      | Generate reports    | 10%       | < 5000ms              |
    When the load test runs for 10 minutes
    Then 95% of requests should meet response time targets
    And error rate should be less than 0.1%
    And system should remain stable throughout

  Scenario: Database query optimization
    Given a database with 1 million CVEs and 10,000 applications
    When I perform complex queries:
      | query_type                    | expected_time | optimization |
      | CVEs by severity and date     | < 100ms       | indexed      |
      | Application vulnerability feed| < 500ms       | materialized |
      | Cross-reference CPE matching  | < 1000ms      | cached       |
      | Historical trend analysis     | < 2000ms      | aggregated   |
    Then all queries should meet performance targets
    And query execution plans should be optimal
    And database resources should be efficiently utilized

  Scenario: API rate limiting and throttling
    Given API rate limits are configured:
      | user_type    | requests_per_minute | burst_limit | throttle_response |
      | Free tier    | 60                  | 10          | 429 status        |
      | Premium      | 600                 | 50          | queued            |
      | Enterprise   | 6000                | 200         | priority          |
    When users exceed their rate limits
    Then appropriate throttling should be applied
    And rate limit headers should be returned
    And system stability should be maintained

  Scenario: Memory usage optimization
    Given the system is processing large datasets:
      | operation              | dataset_size | memory_limit | optimization_technique |
      | CVE bulk import        | 100MB        | 512MB        | streaming              |
      | Report generation      | 50MB         | 256MB        | pagination             |
      | Search indexing        | 200MB        | 1GB          | incremental            |
      | Cache warming          | 150MB        | 512MB        | lazy loading           |
    When these operations run concurrently
    Then memory usage should stay within limits
    And garbage collection should be efficient
    And no out-of-memory errors should occur

  Scenario: Caching strategy effectiveness
    Given a multi-tier caching strategy:
      | cache_layer    | cache_type | ttl     | hit_ratio_target |
      | Application    | Redis      | 1 hour  | > 80%            |
      | Database       | Query      | 15 min  | > 70%            |
      | CDN            | Static     | 24 hour | > 95%            |
      | Browser        | Client     | 5 min   | > 60%            |
    When the system operates under normal load
    Then cache hit ratios should meet targets
    And cache invalidation should work correctly
    And overall response times should improve

  Scenario: Auto-scaling behavior
    Given auto-scaling is configured:
      | metric          | scale_up_threshold | scale_down_threshold | min_instances | max_instances |
      | CPU utilization | 70%                | 30%                  | 2             | 10            |
      | Memory usage    | 80%                | 40%                  | 2             | 10            |
      | Request queue   | 100 requests       | 10 requests          | 2             | 10            |
    When load increases gradually
    Then new instances should be provisioned automatically
    And load should be distributed evenly
    And scaling should not cause service disruption

  Scenario: Database connection pooling
    Given database connection pool configuration:
      | pool_setting     | value | monitoring_metric    |
      | min_connections  | 5     | active_connections   |
      | max_connections  | 50    | pool_utilization     |
      | connection_timeout| 30s   | wait_time           |
      | idle_timeout     | 300s  | idle_connections     |
    When the system experiences varying load
    Then connection pool should scale appropriately
    And connection leaks should not occur
    And database performance should remain optimal

  Scenario: Background job processing
    Given background job queues for different priorities:
      | job_type           | priority | queue_size_limit | processing_time_sla |
      | CVE ingestion      | high     | 1000             | < 5 minutes         |
      | Report generation  | medium   | 500              | < 15 minutes        |
      | Data cleanup       | low      | 100              | < 1 hour            |
      | Email notifications| urgent   | 10000            | < 30 seconds        |
    When jobs are queued at high volume
    Then jobs should be processed according to priority
    And SLAs should be met for each job type
    And queue overflow should be handled gracefully

  Scenario: Search performance optimization
    Given a search index with millions of CVE records
    And search queries with different complexity:
      | query_type        | complexity | expected_time | optimization |
      | Simple keyword    | low        | < 50ms        | full-text    |
      | Filtered search   | medium     | < 200ms       | composite    |
      | Faceted search    | high       | < 500ms       | aggregation  |
      | Fuzzy matching    | very high  | < 1000ms      | approximate  |
    When users perform searches
    Then search results should be returned within time limits
    And relevance scoring should be accurate
    And search index should be kept up-to-date

  Scenario: API response compression
    Given API responses of varying sizes:
      | endpoint_type     | avg_response_size | compression_ratio | bandwidth_savings |
      | CVE list          | 500KB             | 70%               | 350KB             |
      | Application feed  | 2MB               | 80%               | 1.6MB             |
      | Detailed report   | 10MB              | 85%               | 8.5MB             |
      | Search results    | 1MB               | 75%               | 750KB             |
    When compression is enabled
    Then response sizes should be reduced as expected
    And compression should not significantly impact CPU
    And client decompression should work correctly

  Scenario: Monitoring and alerting performance
    Given performance monitoring thresholds:
      | metric                | warning_threshold | critical_threshold | alert_action    |
      | Response time         | 1000ms           | 5000ms             | page_oncall     |
      | Error rate            | 1%               | 5%                 | escalate        |
      | Database connections  | 80%              | 95%                | auto_scale      |
      | Memory usage          | 70%              | 90%                | restart_service |
    When performance degrades
    Then alerts should be triggered at appropriate thresholds
    And automated remediation should be attempted
    And escalation procedures should be followed

  Scenario: Disaster recovery performance
    Given disaster recovery requirements:
      | scenario           | rto_target | rpo_target | recovery_method |
      | Database failure   | 15 minutes | 5 minutes  | hot_standby     |
      | Application crash  | 2 minutes  | 0 minutes  | auto_restart    |
      | Data center outage | 1 hour     | 15 minutes | cross_region    |
      | Network partition  | 5 minutes  | 1 minute   | failover        |
    When disaster recovery is tested
    Then RTO and RPO targets should be met
    And data consistency should be maintained
    And service availability should be restored quickly

  Scenario: Performance regression testing
    Given baseline performance metrics:
      | operation          | baseline_time | acceptable_variance | regression_threshold |
      | User authentication| 100ms         | ±10%                | +25%                 |
      | CVE search         | 500ms         | ±15%                | +30%                 |
      | Report generation  | 5000ms        | ±20%                | +40%                 |
      | Data ingestion     | 1000ms/record | ±10%                | +25%                 |
    When new code is deployed
    Then performance should not regress beyond thresholds
    And regression tests should run automatically
    And performance trends should be tracked over time

  Scenario: Resource utilization optimization
    Given system resource monitoring:
      | resource_type | current_usage | target_efficiency | optimization_opportunity |
      | CPU           | 45%           | 60-70%            | increase_concurrency     |
      | Memory        | 60%           | 70-80%            | optimize_caching         |
      | Disk I/O      | 30%           | 50-60%            | batch_operations         |
      | Network       | 25%           | 40-50%            | compress_responses       |
    When system optimization is performed
    Then resource utilization should improve
    And performance should not be negatively impacted
    And cost efficiency should increase

  Scenario: Capacity planning and forecasting
    Given historical usage patterns:
      | time_period | avg_users | peak_users | data_growth | performance_impact |
      | Last month  | 500       | 800        | 10%         | stable             |
      | Last quarter| 400       | 700        | 35%         | slight_degradation |
      | Last year   | 200       | 400        | 150%        | significant_growth |
    When capacity planning is performed
    Then future resource needs should be projected
    And scaling recommendations should be provided
    And budget implications should be calculated
