Feature: User Authentication and Authorization
  As a system administrator
  I want to control access to the CVE feed service
  So that only authorized users can access sensitive vulnerability information

  Background:
    Given the CVE feed service is running
    And I have a clean database

  Scenario: User registration
    Given I am not authenticated
    When I register a new user with the following details:
      | username  | testuser              |
      | email     | <EMAIL>      |
      | full_name | Test User             |
      | password  | SecurePassword123!    |
      | role      | SECURITY_ANALYST      |
    Then the user should be created successfully
    And the response should contain the user details
    And the password should not be visible in the response

  Scenario: Register user with duplicate username
    Given a user "existinguser" already exists
    When I try to register a new user with username "existinguser"
    Then the registration should fail with conflict error
    And the error message should indicate duplicate username

  <PERSON><PERSON><PERSON>: Register user with invalid email
    Given I am not authenticated
    When I try to register a user with email "invalid-email"
    Then the registration should fail with validation error
    And the error message should indicate invalid email format

  Scenario: Register user with weak password
    Given I am not authenticated
    When I try to register a user with password "123"
    Then the registration should fail with validation error
    And the error message should indicate password requirements

  Scenario: User login with valid credentials
    Given a user "validuser" exists with password "ValidPassword123!"
    When I login with username "validuser" and password "ValidPassword123!"
    Then the login should be successful
    And I should receive an access token
    And the token should be valid for API access

  Scenario: User login with invalid username
    Given I am not authenticated
    When I login with username "nonexistent" and password "anypassword"
    Then the login should fail with unauthorized error
    And I should not receive an access token

  Scenario: User login with invalid password
    Given a user "validuser" exists with password "ValidPassword123!"
    When I login with username "validuser" and password "wrongpassword"
    Then the login should fail with unauthorized error
    And I should not receive an access token

  Scenario: Access protected endpoint with valid token
    Given I am an authenticated user with role "SECURITY_ANALYST"
    When I access a protected endpoint with my token
    Then the request should be successful
    And I should receive the requested data

  Scenario: Access protected endpoint without token
    Given I am not authenticated
    When I try to access a protected endpoint without a token
    Then the request should fail with unauthorized error

  Scenario: Access protected endpoint with expired token
    Given I have an expired authentication token
    When I try to access a protected endpoint with the expired token
    Then the request should fail with unauthorized error

  Scenario: Access protected endpoint with invalid token
    Given I have an invalid authentication token
    When I try to access a protected endpoint with the invalid token
    Then the request should fail with unauthorized error

  Scenario: Role-based access control - Security Analyst
    Given I am an authenticated user with role "SECURITY_ANALYST"
    When I try to access CVE information
    Then the request should be successful
    When I try to create an application
    Then the request should be successful
    When I try to access admin functions
    Then the request should fail with forbidden error

  Scenario: Role-based access control - IT Admin
    Given I am an authenticated user with role "IT_ADMIN"
    When I try to access CVE information
    Then the request should be successful
    When I try to create an application
    Then the request should be successful
    When I try to access admin functions
    Then the request should be successful

  Scenario: Role-based access control - Read Only
    Given I am an authenticated user with role "READ_ONLY"
    When I try to access CVE information
    Then the request should be successful
    When I try to create an application
    Then the request should fail with forbidden error
    When I try to update an application
    Then the request should fail with forbidden error

  Scenario: Token refresh
    Given I am an authenticated user
    And my token is about to expire
    When I request a token refresh
    Then I should receive a new valid token
    And the old token should become invalid

  Scenario: User logout
    Given I am an authenticated user
    When I logout
    Then my token should be invalidated
    And subsequent requests with the token should fail

  Scenario: Get current user profile
    Given I am an authenticated user with the following details:
      | username  | profileuser           |
      | email     | <EMAIL>   |
      | full_name | Profile User          |
      | role      | SECURITY_ANALYST      |
    When I request my user profile
    Then I should receive my profile information
    And the profile should contain my username "profileuser"
    And the profile should contain my email "<EMAIL>"
    And the profile should not contain my password

  Scenario: Update user profile
    Given I am an authenticated user
    When I update my profile with:
      | full_name | Updated Name          |
      | email     | <EMAIL>   |
    Then my profile should be updated successfully
    And my full name should be "Updated Name"
    And my email should be "<EMAIL>"

  Scenario: Change password
    Given I am an authenticated user with password "OldPassword123!"
    When I change my password to "NewPassword456!"
    Then the password change should be successful
    When I login with my username and password "NewPassword456!"
    Then the login should be successful
    When I try to login with my username and password "OldPassword123!"
    Then the login should fail

  Scenario: API key generation
    Given I am an authenticated user
    When I request to generate an API key with name "Test API Key"
    Then an API key should be generated successfully
    And I should receive the API key value
    And the API key should be usable for authentication

  Scenario: API key authentication
    Given I have a valid API key "test-api-key-123"
    When I make a request using the API key for authentication
    Then the request should be successful
    And I should have the same access as with JWT token

  Scenario: Revoke API key
    Given I have an API key "revoke-test-key"
    When I revoke the API key
    Then the API key should be deactivated
    And subsequent requests with the API key should fail
