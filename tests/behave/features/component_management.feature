@components @security @regression
Feature: Component Management
  As a security engineer
  I want to manage software components and their vulnerabilities
  So that I can track security risks across my technology stack

  Background:
    Given the CVE feed service is running
    And I have a clean database
    And I am an authenticated user with role "SECURITY_ANALYST"

  Scenario: Add component to application
    Given an application "Web Portal" exists with ID stored as "app_id"
    When I add a component to the application with details:
      | name         | Apache HTTP Server |
      | version      | 2.4.41            |
      | cpe_string   | cpe:2.3:a:apache:http_server:2.4.41:*:*:*:*:*:*:* |
      | description  | Web server component |
    Then the component should be added successfully
    And the component should be linked to the application
    And the component should have a valid CPE string

  Scenario: Detect vulnerable components
    Given an application "Legacy System" exists with components:
      | name    | version | cpe_string                           |
      | OpenSSL | 1.0.1   | cpe:2.3:a:openssl:openssl:1.0.1:*   |
      | Apache  | 2.2.15  | cpe:2.3:a:apache:http_server:2.2.15 |
    And CVEs exist that affect these components:
      | cve_id          | affected_cpe                         | severity |
      | CVE-2014-0160   | cpe:2.3:a:openssl:openssl:1.0.1:*   | HIGH     |
      | CVE-2012-0883   | cpe:2.3:a:apache:http_server:2.2.15 | MEDIUM   |
    When I request vulnerable components for the application
    Then I should receive 2 vulnerable components
    And each component should show its associated CVEs
    And the components should be ranked by severity

  Scenario: Component version comparison
    Given a component "Node.js" with version "14.15.0" exists
    And CVEs exist for Node.js versions:
      | cve_id        | affected_versions | severity |
      | CVE-2021-001  | < 14.16.0        | HIGH     |
      | CVE-2020-002  | < 14.15.1        | MEDIUM   |
    When I check vulnerabilities for the component
    Then the component should be flagged as vulnerable
    And I should see 2 applicable CVEs
    And upgrade recommendations should be provided

  Scenario: Bulk component import
    Given I have a component inventory file with 50 components
    When I perform a bulk component import for application "Enterprise App"
    Then all 50 components should be imported successfully
    And each component should be validated for CPE format
    And duplicate components should be merged
    And import statistics should be provided

  Scenario: Component lifecycle management
    Given an application has a component "jQuery" version "1.8.0"
    When I update the component to version "3.6.0"
    Then the component version should be updated
    And historical version data should be preserved
    And new vulnerability assessments should be triggered
    And change notifications should be sent

  Scenario: CPE string validation and normalization
    Given I want to add components with various CPE formats:
      | input_cpe                                    | expected_normalized |
      | cpe:/a:apache:http_server:2.4.41            | cpe:2.3:a:apache:http_server:2.4.41:*:*:*:*:*:*:* |
      | apache:http_server:2.4.41                   | cpe:2.3:a:apache:http_server:2.4.41:*:*:*:*:*:*:* |
      | cpe:2.3:a:microsoft:windows:10:*:*:*:*:*:*:* | cpe:2.3:a:microsoft:windows:10:*:*:*:*:*:*:*      |
    When I add these components to an application
    Then all CPE strings should be normalized correctly
    And the components should be searchable by any CPE format

  Scenario: Component dependency mapping
    Given an application "Microservice A" has components:
      | name        | version | dependencies          |
      | Spring Boot | 2.5.0   | spring-core, tomcat   |
      | Spring Core | 5.3.0   | none                  |
      | Tomcat      | 9.0.45  | none                  |
    When I request the component dependency tree
    Then I should see the complete dependency hierarchy
    And transitive vulnerabilities should be identified
    And dependency update recommendations should be provided

  Scenario: Component risk scoring
    Given components with different risk profiles:
      | name     | version | exposure | criticality | vulnerabilities |
      | Database | 5.7.0   | external | high        | 3 critical      |
      | Logger   | 1.2.0   | internal | low         | 1 medium        |
      | Cache    | 6.0.0   | internal | medium      | 0               |
    When I request component risk assessment
    Then components should be ranked by risk score
    And risk factors should be clearly explained
    And mitigation recommendations should be provided

  Scenario: Component compliance checking
    Given compliance requirements for "SOC 2 Type II":
      | requirement                    | rule                           |
      | No critical vulnerabilities    | CVSS >= 9.0 not allowed       |
      | Supported versions only        | EOL versions not allowed       |
      | Regular security updates       | Updates within 30 days         |
    When I check application compliance
    Then compliance status should be calculated
    And non-compliant components should be identified
    And remediation steps should be provided

  Scenario: Component vulnerability timeline
    Given a component "OpenSSL 1.1.1" has been in use for 2 years
    And vulnerability history exists:
      | date       | cve_id        | severity | patched |
      | 2023-01-15 | CVE-2023-001  | HIGH     | yes     |
      | 2023-06-20 | CVE-2023-002  | CRITICAL | no      |
      | 2023-11-10 | CVE-2023-003  | MEDIUM   | yes     |
    When I request the vulnerability timeline
    Then I should see chronological vulnerability events
    And patching effectiveness should be measured
    And vulnerability trends should be analyzed

  Scenario: Component license management
    Given components with different licenses:
      | name      | version | license    | compatibility |
      | Library A | 1.0.0   | MIT        | compatible    |
      | Library B | 2.1.0   | GPL-3.0    | incompatible  |
      | Library C | 1.5.0   | Apache-2.0 | compatible    |
    When I check license compatibility
    Then license conflicts should be identified
    And alternative components should be suggested
    And legal risk assessment should be provided

  Scenario: Component performance impact
    Given components with performance characteristics:
      | name          | cpu_impact | memory_impact | known_issues |
      | Heavy Crypto  | high       | medium        | memory leaks |
      | Fast Parser   | low        | low           | none         |
      | Legacy Driver | medium     | high          | threading    |
    When I analyze performance impact
    Then performance bottlenecks should be identified
    And optimization recommendations should be provided
    And alternative components should be suggested

  Scenario: Component security configuration
    Given a component "Database Server" with security settings:
      | setting           | current_value | recommended_value | risk_level |
      | encryption        | disabled      | enabled           | high       |
      | authentication    | basic         | multi-factor      | medium     |
      | access_logging    | minimal       | comprehensive     | low        |
    When I audit component security configuration
    Then security gaps should be identified
    And configuration recommendations should be provided
    And implementation guides should be available

  Scenario: Component end-of-life management
    Given components with lifecycle status:
      | name       | version | eol_date   | support_status |
      | Framework  | 1.0.0   | 2023-12-31 | deprecated     |
      | Library    | 2.5.0   | 2025-06-30 | supported      |
      | Tool       | 3.1.0   | 2024-01-15 | end-of-life    |
    When I check component lifecycle status
    Then end-of-life components should be flagged
    And migration timelines should be provided
    And replacement recommendations should be suggested
