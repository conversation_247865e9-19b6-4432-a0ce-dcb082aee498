@api @core @smoke
Feature: Application Management
  As a security administrator
  I want to manage applications in the CVE feed service
  So that I can track vulnerabilities for my organization's applications

  Background:
    Given the CVE feed service is running
    And I have a clean database

  Scenario: Create a new application
    Given I am an authenticated user
    When I create an application with the following details:
      | name        | Test Application      |
      | environment | production           |
      | description | A test application   |
      | criticality | high                 |
      | owner       | Security Team        |
    Then the application should be created successfully
    And the response should contain the application details
    And the application should have a unique ID

  Scenario: Create application with minimal required fields
    Given I am an authenticated user
    When I create an application with only required fields:
      | name        | Minimal App |
      | environment | test        |
    Then the application should be created successfully
    And the application should have default values for optional fields

  Scenario: Fail to create application with missing required fields
    Given I am an authenticated user
    When I try to create an application without a name
    Then the request should fail with validation error
    And the error message should indicate missing required field

  Scenario: Create duplicate application in same environment
    Given I am an authenticated user
    And an application "Existing App" exists in "production" environment
    When I try to create another application "Existing App" in "production" environment
    Then the request should fail with conflict error
    And the error message should indicate duplicate application

  Scenario: Create application with same name in different environment
    Given I am an authenticated user
    And an application "Multi-Env App" exists in "production" environment
    When I create an application "Multi-Env App" in "development" environment
    Then the application should be created successfully
    And both applications should exist with different IDs

  Scenario: List all applications
    Given I am an authenticated user
    And the following applications exist:
      | name     | environment | criticality |
      | App One  | production  | high        |
      | App Two  | development | medium      |
      | App Three| staging     | low         |
    When I request the list of all applications
    Then I should receive all 3 applications
    And each application should contain complete details

  Scenario: Filter applications by environment
    Given I am an authenticated user
    And the following applications exist:
      | name        | environment |
      | Prod App 1  | production  |
      | Prod App 2  | production  |
      | Dev App     | development |
      | Test App    | test        |
    When I request applications filtered by "production" environment
    Then I should receive 2 applications
    And all returned applications should be in "production" environment

  Scenario: Paginate application list
    Given I am an authenticated user
    And 10 applications exist in the system
    When I request the first page with 3 applications per page
    Then I should receive 3 applications
    When I request the second page with 3 applications per page
    Then I should receive 3 different applications

  Scenario: Get application by ID
    Given I am an authenticated user
    And an application "Specific App" exists with ID stored as "app_id"
    When I request the application with ID "app_id"
    Then I should receive the application details
    And the application name should be "Specific App"

  Scenario: Get non-existent application
    Given I am an authenticated user
    When I request an application with a non-existent ID
    Then the request should fail with not found error

  Scenario: Update application details
    Given I am an authenticated user
    And an application "Original App" exists with ID stored as "app_id"
    When I update the application "app_id" with:
      | description | Updated description |
      | criticality | critical           |
    Then the application should be updated successfully
    And the application description should be "Updated description"
    And the application criticality should be "critical"
    And the application name should remain "Original App"

  Scenario: Update non-existent application
    Given I am an authenticated user
    When I try to update an application with a non-existent ID
    Then the request should fail with not found error

  Scenario: Delete application
    Given I am an authenticated user
    And an application "To Delete" exists with ID stored as "app_id"
    When I delete the application with ID "app_id"
    Then the application should be deleted successfully
    When I try to get the application with ID "app_id"
    Then the request should fail with not found error

  Scenario: Delete non-existent application
    Given I am an authenticated user
    When I try to delete an application with a non-existent ID
    Then the request should fail with not found error

  Scenario: Unauthorized access to applications
    Given I am not authenticated
    When I try to create an application
    Then the request should fail with unauthorized error
    When I try to list applications
    Then the request should fail with unauthorized error
