Feature: CVE Management
  As a security analyst
  I want to access and manage CVE information
  So that I can assess vulnerabilities affecting my applications

  Background:
    Given the CVE feed service is running
    And I have a clean database

  Scenario: List all CVEs
    Given I am an authenticated user
    And the following CVEs exist in the system:
      | cve_id          | cvss_v3_score | cvss_v3_severity |
      | CVE-2023-001    | 9.8           | CRITICAL         |
      | CVE-2023-002    | 7.5           | HIGH             |
      | CVE-2023-003    | 4.3           | MEDIUM           |
    When I request the list of all CVEs
    Then I should receive all 3 CVEs
    And each CVE should contain complete vulnerability details

  Scenario: Filter CVEs by severity
    Given I am an authenticated user
    And the following CVEs exist in the system:
      | cve_id          | cvss_v3_severity |
      | CVE-2023-HIGH-1 | HIGH             |
      | CVE-2023-HIGH-2 | HIGH             |
      | CVE-2023-MED-1  | MEDIUM           |
      | CVE-2023-CRIT-1 | CRITICAL         |
    When I request CVEs filtered by "HIGH" severity
    Then I should receive 2 CVEs
    And all returned CVEs should have "HIGH" severity

  Scenario: Paginate CVE list
    Given I am an authenticated user
    And 25 CVEs exist in the system
    When I request the first page with 10 CVEs per page
    Then I should receive 10 CVEs
    And the response should indicate there are more results
    When I request the third page with 10 CVEs per page
    Then I should receive 5 CVEs
    And the response should indicate this is the last page

  Scenario: Get specific CVE by ID
    Given I am an authenticated user
    And a CVE "CVE-2023-SPECIFIC" exists with the following details:
      | description     | A specific test vulnerability |
      | cvss_v3_score   | 8.1                          |
      | cvss_v3_vector  | CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N |
    When I request the CVE "CVE-2023-SPECIFIC"
    Then I should receive the CVE details
    And the CVE description should be "A specific test vulnerability"
    And the CVSS v3 score should be 8.1

  Scenario: Get non-existent CVE
    Given I am an authenticated user
    When I request a CVE with ID "CVE-9999-NONEXISTENT"
    Then the request should fail with not found error

  Scenario: Get tailored CVE feed
    Given I am an authenticated user
    When I request the tailored CVE feed
    Then I should receive a list of relevant CVEs
    And the response should include pagination information
    And the response should include total count

  Scenario: Get tailored CVE feed for specific application
    Given I am an authenticated user
    And an application "Web App" exists with ID stored as "app_id"
    And the application has the following components:
      | name    | version | cpe_string                           |
      | Apache  | 2.4.41  | cpe:2.3:a:apache:http_server:2.4.41 |
      | PHP     | 7.4.3   | cpe:2.3:a:php:php:7.4.3             |
    And CVEs exist that affect these components
    When I request the tailored CVE feed for application "app_id"
    Then I should receive CVEs relevant to the application
    And the CVEs should be related to Apache or PHP components

  Scenario: Filter CVEs by publication date
    Given I am an authenticated user
    And CVEs exist with different publication dates:
      | cve_id          | published_date |
      | CVE-2023-OLD    | 2023-01-01     |
      | CVE-2023-RECENT | 2023-12-01     |
      | CVE-2024-NEW    | 2024-01-01     |
    When I request CVEs published after "2023-06-01"
    Then I should receive 2 CVEs
    And the CVEs should be "CVE-2023-RECENT" and "CVE-2024-NEW"

  Scenario: Search CVEs by keyword
    Given I am an authenticated user
    And the following CVEs exist:
      | cve_id          | description                    |
      | CVE-2023-SQL    | SQL injection vulnerability   |
      | CVE-2023-XSS    | Cross-site scripting issue    |
      | CVE-2023-SQLI   | Another SQL injection flaw    |
    When I search for CVEs containing "SQL injection"
    Then I should receive 2 CVEs
    And the CVEs should be "CVE-2023-SQL" and "CVE-2023-SQLI"

  Scenario: Get CVE statistics
    Given I am an authenticated user
    And the following CVEs exist:
      | cve_id          | cvss_v3_severity |
      | CVE-2023-C1     | CRITICAL         |
      | CVE-2023-C2     | CRITICAL         |
      | CVE-2023-H1     | HIGH             |
      | CVE-2023-H2     | HIGH             |
      | CVE-2023-H3     | HIGH             |
      | CVE-2023-M1     | MEDIUM           |
    When I request CVE statistics
    Then I should receive a summary with:
      | severity | count |
      | CRITICAL | 2     |
      | HIGH     | 3     |
      | MEDIUM   | 1     |
      | LOW      | 0     |

  Scenario: Export CVE data
    Given I am an authenticated user
    And 5 CVEs exist in the system
    When I request to export CVE data in JSON format
    Then I should receive a downloadable file
    And the file should contain all 5 CVEs
    And the file format should be valid JSON

  Scenario: Unauthorized access to CVEs
    Given I am not authenticated
    When I try to list CVEs
    Then the request should fail with unauthorized error
    When I try to get a specific CVE
    Then the request should fail with unauthorized error
