Feature: Global Search Functionality
  As a security analyst
  I want to search across all vulnerability data from anywhere in the application
  So that I can quickly find relevant information

  Background:
    Given I am logged in as a security analyst
    And the system contains searchable data including CVEs, applications, and components
    And I can access the global search from any page

  @api @search @critical
  Scenario: Successful global search with results
    Given I want to search for vulnerability information
    When I search for "CVE-2023-1234" using the global search API
    Then the API should respond with status code 200
    And I should receive search results containing:
      | result_type   | count | fields                           |
      | cves          | 1     | id, title, severity, description |
      | applications  | 2     | name, environment, affected      |
      | components    | 3     | name, version, cpe_string        |
    And the results should be ranked by relevance
    And each result should include a snippet with highlighted matches

  @api @search @filtering
  Scenario: Search with type filters
    Given I want to search within specific data types
    When I search for "nginx" with type filter "components"
    Then the API should respond with status code 200
    And the results should only include component data
    And I should not see CVE or application results
    When I search for "nginx" with type filter "cves"
    Then the results should only include CVEs mentioning nginx
    When I search for "nginx" with no type filter
    Then the results should include all relevant data types

  @api @search @pagination
  Scenario: Search results pagination
    Given I have a search query that returns many results
    When I search for "vulnerability" with pagination parameters
    Then the API should respond with status code 200
    And the response should include:
      | field       | description                    |
      | results     | Array of search results        |
      | total_count | Total number of matching items |
      | page        | Current page number            |
      | per_page    | Results per page               |
      | has_more    | Boolean indicating more pages  |
    When I request the next page of results
    Then I should receive the next set of results
    And the pagination metadata should be updated

  @api @search @advanced
  Scenario: Advanced search with multiple criteria
    Given I want to perform an advanced search
    When I search with multiple criteria:
      | field      | value        | operator |
      | severity   | HIGH         | equals   |
      | published  | 2023-01-01   | after    |
      | component  | nginx        | contains |
    Then the API should respond with status code 200
    And all results should match the specified criteria
    And the search should use AND logic between criteria
    When I change the logic to OR
    Then the results should include items matching any criteria

  @ux @search @interface
  Scenario: Global search interface interaction
    Given I am on any page in the application
    Then I should see a global search bar in the header
    And the search bar should have a search icon
    And the placeholder text should say "Search CVEs, applications, components..."
    When I click in the search bar
    Then the search bar should expand if needed
    And I should see a cursor in the input field
    When I start typing
    Then I should see search suggestions appear

  @ux @search @autocomplete
  Scenario: Search autocomplete and suggestions
    Given I am using the global search
    When I type "CVE-202" in the search bar
    Then I should see autocomplete suggestions including:
      | suggestion_type | example           |
      | recent_cves     | CVE-2023-1234     |
      | popular_cves    | CVE-2023-5678     |
      | applications    | WebApp-Production |
    And suggestions should appear within 300ms
    When I select a suggestion
    Then the search should be executed immediately
    And I should be taken to the appropriate results page

  @ux @search @keyboard-navigation
  Scenario: Search keyboard navigation
    Given I am using the global search with keyboard
    When I press "Ctrl+K" (or "Cmd+K" on Mac)
    Then the search bar should be focused
    And any existing text should be selected
    When I type a search query and press "Enter"
    Then the search should be executed
    And I should be taken to the search results page
    When I use arrow keys in the autocomplete dropdown
    Then I should be able to navigate through suggestions
    And the selected suggestion should be highlighted

  @ux @search @results-display
  Scenario: Search results page layout and interaction
    Given I have performed a search with mixed results
    When I view the search results page
    Then I should see results grouped by type:
      | section      | description                    |
      | CVEs         | Vulnerability entries          |
      | Applications | Application matches            |
      | Components   | Component matches              |
    And each result should show relevant preview information
    And I should see the total count for each category
    When I click on a result
    Then I should be taken to the detailed view for that item

  @ux @search @responsive
  Scenario: Search responsive behavior
    Given I am using the search on different devices
    When I access the search on mobile
    Then the search bar should be appropriately sized
    And autocomplete suggestions should be touch-friendly
    And the results page should be optimized for mobile viewing
    When I access the search on tablet
    Then the layout should adapt to the available space
    And all functionality should remain accessible

  @ux @search @loading-states
  Scenario: Search loading and error states
    Given I am performing a search
    When I submit a search query
    Then I should see a loading indicator
    And the search button should be disabled during the search
    When the search completes successfully
    Then the loading indicator should disappear
    And the results should be displayed
    When the search fails due to network error
    Then I should see an error message
    And I should have an option to retry the search

  @performance @search @response-time
  Scenario: Search performance requirements
    Given I am performing various types of searches
    When I search for a simple term
    Then the autocomplete should respond within 300ms
    And the full search should complete within 2 seconds
    When I search for a complex query with filters
    Then the search should complete within 5 seconds
    And the results should be paginated for large result sets

  @security @search @access-control
  Scenario: Search access control and data filtering
    Given I am logged in with specific permissions
    When I search for sensitive information
    Then I should only see results I have permission to view
    And restricted data should be filtered from results
    When I search as a "read_only" user
    Then I should not see administrative or sensitive data
    When I search as an "admin" user
    Then I should have access to all searchable data

  @api @search @error-handling
  Scenario Outline: Search API error scenarios
    Given I am using the search API
    When I submit a search request that results in <error_condition>
    Then the API should respond with status code <status_code>
    And I should receive error message "<error_message>"
    And the UI should handle the error gracefully

    Examples:
      | error_condition    | status_code | error_message                |
      | invalid_query      | 400         | Invalid search query         |
      | unauthorized       | 401         | Authentication required      |
      | forbidden_access   | 403         | Insufficient permissions     |
      | server_error       | 500         | Search service unavailable   |
      | timeout            | 504         | Search request timed out     |

  @integration @search @cross-navigation
  Scenario: Search integration with application navigation
    Given I am on the CVE details page
    When I perform a global search for "nginx"
    Then I should be taken to the search results page
    And my previous page should be available in browser history
    When I click on an application result
    Then I should be taken to the application details page
    And the search context should be preserved
    When I use the browser back button
    Then I should return to the search results
    And my search query should still be visible

  @api @search @indexing
  Scenario: Search index freshness and accuracy
    Given new data has been added to the system
    When I search for the newly added data
    Then the search results should include the new information
    And the search index should be updated within 5 minutes
    When data is modified in the system
    Then the search results should reflect the changes
    When data is deleted from the system
    Then it should no longer appear in search results
