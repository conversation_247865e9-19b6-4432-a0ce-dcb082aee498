Feature: Dashboard Metrics Display
  As a security analyst
  I want to view comprehensive vulnerability metrics on the dashboard
  So that I can quickly assess the security posture of my applications

  Background:
    Given I am logged in as a security analyst
    And I have applications with various vulnerability statuses
    And I am on the dashboard page

  @api @dashboard @metrics @critical
  Scenario: Load dashboard metrics successfully
    When I request dashboard metrics from the API
    Then the API should respond with status code 200
    And I should receive metrics data including:
      | metric_type        | field_name           |
      | total_cves         | total_count          |
      | critical_cves      | critical_count       |
      | high_cves          | high_count           |
      | medium_cves        | medium_count         |
      | low_cves           | low_count            |
      | applications_count | total_applications   |
      | components_count   | total_components     |
      | last_updated       | timestamp            |

  @api @dashboard @metrics @filtering
  Scenario: Filter metrics by time period
    Given I want to view metrics for a specific time period
    When I request dashboard metrics with date range "last_30_days"
    Then the API should respond with status code 200
    And the metrics should only include CVEs from the last 30 days
    And the response should include the applied date filter
    When I request metrics with date range "last_7_days"
    Then the metrics should be filtered to the last 7 days
    And the counts should be different from the 30-day period

  @api @dashboard @metrics @application-specific
  Scenario: Application-specific metrics
    Given I have multiple applications with different risk profiles
    When I request dashboard metrics filtered by application "WebApp-Production"
    Then the API should respond with status code 200
    And the metrics should only include CVEs affecting "WebApp-Production"
    And I should see application-specific component counts
    And the risk score should be calculated for that application only

  @ux @dashboard @metrics @visualization
  Scenario: Metrics cards display and interaction
    Given I am viewing the dashboard
    Then I should see metrics cards for each severity level
    And each card should display:
      | element           | description                    |
      | severity_badge    | Color-coded severity indicator |
      | count_number      | Large, prominent number        |
      | trend_indicator   | Up/down arrow with percentage  |
      | time_period       | "Last 30 days" or similar      |
    When I hover over a metrics card
    Then I should see additional details in a tooltip
    And the card should have a subtle hover effect

  @ux @dashboard @metrics @responsive
  Scenario: Metrics responsive layout
    Given I am viewing the dashboard on different screen sizes
    When I resize the browser to mobile width
    Then the metrics cards should stack vertically
    And all information should remain readable
    And touch targets should be appropriately sized
    When I resize to tablet width
    Then the cards should arrange in a 2x2 grid
    When I resize to desktop width
    Then the cards should display in a single row

  @ux @dashboard @metrics @loading
  Scenario: Metrics loading states
    Given I am loading the dashboard
    When the metrics are being fetched
    Then I should see skeleton loading placeholders
    And the placeholders should match the final card layout
    When the API request completes successfully
    Then the loading placeholders should be replaced with actual data
    And numbers should animate from 0 to their final values

  @ux @dashboard @metrics @error-handling
  Scenario: Metrics loading error handling
    Given I am on the dashboard
    When the metrics API request fails
    Then I should see an error state in the metrics section
    And I should see a "Retry" button
    And the error message should be user-friendly
    When I click the "Retry" button
    Then the system should attempt to reload the metrics
    And I should see the loading state again

  @ux @dashboard @metrics @real-time-updates
  Scenario: Real-time metrics updates
    Given I am viewing the dashboard
    And I have real-time updates enabled
    When new CVE data is ingested into the system
    Then the metrics should update automatically
    And I should see a subtle notification of the update
    And the trend indicators should reflect the new data
    When I disable real-time updates
    Then the metrics should remain static until manual refresh

  @ux @dashboard @metrics @accessibility
  Scenario: Metrics accessibility features
    Given I am using assistive technology
    When I navigate the metrics section
    Then each metrics card should have proper ARIA labels
    And the trend indicators should be announced correctly
    And color information should not be the only way to convey severity
    And keyboard navigation should work smoothly
    When metrics update in real-time
    Then screen readers should be notified of important changes

  @integration @dashboard @metrics @drill-down
  Scenario: Metrics drill-down navigation
    Given I am viewing the dashboard metrics
    When I click on the "Critical CVEs" metrics card
    Then I should be navigated to the CVE feed page
    And the CVE feed should be pre-filtered to show only critical CVEs
    And the filter should be visible in the URL and UI
    When I click on the "Applications" metrics card
    Then I should be navigated to the applications page
    And I should see all applications with their current status

  @performance @dashboard @metrics @caching
  Scenario: Metrics caching and performance
    Given I am loading the dashboard for the first time
    When I request the metrics
    Then the response time should be under 2 seconds
    And the metrics should be cached for 5 minutes
    When I refresh the page within the cache period
    Then the metrics should load from cache
    And the response should be nearly instantaneous
    When the cache expires
    Then fresh data should be fetched from the API

  @api @dashboard @metrics @error-scenarios
  Scenario Outline: Metrics API error handling
    Given I am requesting dashboard metrics
    When the API returns status code <status_code>
    Then I should receive appropriate error response
    And the error message should be "<error_message>"
    And the UI should handle the error gracefully

    Examples:
      | status_code | error_message                    |
      | 401         | Authentication required          |
      | 403         | Insufficient permissions         |
      | 500         | Internal server error            |
      | 503         | Service temporarily unavailable  |

  @security @dashboard @metrics @authorization
  Scenario: Metrics access control
    Given I am logged in with different user roles
    When I request dashboard metrics as a "read_only" user
    Then I should see basic metrics without sensitive details
    When I request dashboard metrics as a "security_analyst" user
    Then I should see comprehensive metrics including risk scores
    When I request dashboard metrics as an "admin" user
    Then I should see all metrics including system health indicators
    And I should have access to administrative metrics

  @api @dashboard @metrics @data-accuracy
  Scenario: Metrics data accuracy validation
    Given I have known test data in the system
    When I request dashboard metrics
    Then the total CVE count should match the database count
    And the severity breakdown should sum to the total count
    And the application count should match active applications
    And the component count should match registered components
    And the last updated timestamp should be recent and accurate
