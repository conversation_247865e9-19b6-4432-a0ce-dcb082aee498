"""Behave environment configuration for CVE Feed Service BDD tests."""

import asyncio
import os
import sys
from pathlib import Path

import httpx
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.pool import StaticPool

# Add the src directory to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

from src.cve_feed_service.main import app
from src.cve_feed_service.db.base import Base
from src.cve_feed_service.db.database import get_db


class BehaveContext:
    """Enhanced context for Behave tests."""
    
    def __init__(self):
        self.app = None
        self.client = None
        self.db_engine = None
        self.db_session = None
        self.response = None
        self.created_entities = {}
        self.test_data = {}
        self.auth_token = None
        self.current_user = None


def before_all(context):
    """Set up test environment before all scenarios."""
    context.behave_context = BehaveContext()
    
    # Set test environment variables
    os.environ["ENVIRONMENT"] = "test"
    os.environ["DATABASE_URL"] = "sqlite+aiosqlite:///:memory:"
    os.environ["SECRET_KEY"] = "test-secret-key-for-behave-tests"
    
    # Create event loop for async operations
    context.loop = asyncio.new_event_loop()
    asyncio.set_event_loop(context.loop)


def before_scenario(context, scenario):
    """Set up before each scenario."""
    # Create fresh database for each scenario
    context.behave_context.db_engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
    )
    
    # Create all tables
    async def create_tables():
        async with context.behave_context.db_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
    
    context.loop.run_until_complete(create_tables())
    
    # Create database session
    context.behave_context.db_session = AsyncSession(context.behave_context.db_engine)
    
    # Override database dependency
    async def get_test_db():
        async with AsyncSession(context.behave_context.db_engine) as session:
            yield session
    
    app.dependency_overrides[get_db] = get_test_db
    
    # Create HTTP client
    context.behave_context.client = httpx.AsyncClient(app=app, base_url="http://test")
    
    # Reset context data
    context.behave_context.response = None
    context.behave_context.created_entities = {}
    context.behave_context.test_data = {}
    context.behave_context.auth_token = None
    context.behave_context.current_user = None


def after_scenario(context, scenario):
    """Clean up after each scenario."""
    # Close HTTP client
    if context.behave_context.client:
        context.loop.run_until_complete(context.behave_context.client.aclose())
    
    # Close database session
    if context.behave_context.db_session:
        context.loop.run_until_complete(context.behave_context.db_session.close())
    
    # Dispose database engine
    if context.behave_context.db_engine:
        context.loop.run_until_complete(context.behave_context.db_engine.dispose())
    
    # Clear dependency overrides
    app.dependency_overrides.clear()


def after_all(context):
    """Clean up after all scenarios."""
    if hasattr(context, 'loop'):
        context.loop.close()


# Helper functions for async operations in steps
def run_async(context, coro):
    """Run an async coroutine in the test event loop."""
    return context.loop.run_until_complete(coro)


def get_context(context):
    """Get the enhanced behave context."""
    return context.behave_context
