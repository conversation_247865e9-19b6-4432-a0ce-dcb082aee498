"""Tests for dependency injection utilities."""

import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock, patch
from fastapi import HTT<PERSON>Exception
from sqlalchemy.ext.asyncio import AsyncSession

from src.cve_feed_service.core.dependencies import (
    get_current_user_from_token,
    get_current_user_from_api_key,
    get_current_user,
    require_authentication,
    require_admin,
    require_analyst_or_admin,
    check_permission
)
from src.cve_feed_service.db.database import get_db
from src.cve_feed_service.core.config import get_settings
from src.cve_feed_service.core.config import Settings


class TestDatabaseDependency:
    """Test database dependency injection."""

    @pytest.mark.asyncio
    async def test_get_db_basic(self):
        """Test basic database dependency."""
        # Test that get_db is callable and returns a generator
        db_generator = get_db()
        assert db_generator is not None

        # Test that it's an async generator
        assert hasattr(db_generator, '__anext__')

    @pytest.mark.asyncio
    async def test_get_db_import(self):
        """Test that get_db can be imported."""
        from src.cve_feed_service.db.database import get_db as imported_get_db
        assert imported_get_db is not None
        assert callable(imported_get_db)


class TestUserDependencies:
    """Test user authentication dependencies."""

    @pytest.mark.asyncio
    async def test_get_current_user_from_token_no_credentials(self):
        """Test get_current_user_from_token with no credentials."""
        mock_db = MagicMock(spec=AsyncSession)

        # Test with None credentials
        result = await get_current_user_from_token(None, mock_db)
        assert result is None

    @pytest.mark.asyncio
    async def test_get_current_user_from_api_key_no_key(self):
        """Test get_current_user_from_api_key with no key."""
        mock_db = MagicMock(spec=AsyncSession)

        # Test with None API key
        result = await get_current_user_from_api_key(None, mock_db)
        assert result is None

    @pytest.mark.asyncio
    async def test_get_current_user_combines_sources(self):
        """Test get_current_user combines token and API key sources."""
        # Test that the function exists and is callable
        assert callable(get_current_user)

    @pytest.mark.asyncio
    async def test_require_authentication_no_user(self):
        """Test require_authentication with no user."""
        with pytest.raises(HTTPException) as exc_info:
            await require_authentication(None)

        assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_require_authentication_with_user(self):
        """Test require_authentication with valid user."""
        mock_user = MagicMock()

        result = await require_authentication(mock_user)
        assert result == mock_user

    @pytest.mark.asyncio
    async def test_require_admin_non_admin(self):
        """Test require_admin with non-admin user."""
        mock_user = MagicMock()
        mock_user.is_admin = False

        with pytest.raises(HTTPException) as exc_info:
            await require_admin(mock_user)

        assert exc_info.value.status_code == 403

    @pytest.mark.asyncio
    async def test_require_admin_with_admin(self):
        """Test require_admin with admin user."""
        mock_user = MagicMock()
        mock_user.is_admin = True

        result = await require_admin(mock_user)
        assert result == mock_user

    @pytest.mark.asyncio
    async def test_require_analyst_or_admin_neither(self):
        """Test require_analyst_or_admin with user who is neither."""
        mock_user = MagicMock()
        mock_user.is_analyst = False
        mock_user.is_admin = False

        with pytest.raises(HTTPException) as exc_info:
            await require_analyst_or_admin(mock_user)

        assert exc_info.value.status_code == 403

    @pytest.mark.asyncio
    async def test_require_analyst_or_admin_analyst(self):
        """Test require_analyst_or_admin with analyst user."""
        mock_user = MagicMock()
        mock_user.is_analyst = True
        mock_user.is_admin = False

        result = await require_analyst_or_admin(mock_user)
        assert result == mock_user

    @pytest.mark.asyncio
    async def test_require_analyst_or_admin_admin(self):
        """Test require_analyst_or_admin with admin user."""
        mock_user = MagicMock()
        mock_user.is_analyst = False
        mock_user.is_admin = True

        result = await require_analyst_or_admin(mock_user)
        assert result == mock_user


class TestSettingsDependency:
    """Test settings dependency injection."""

    def test_get_settings_basic(self):
        """Test basic settings dependency."""
        from src.cve_feed_service.core.config import Settings

        settings = get_settings()

        assert isinstance(settings, Settings)
        assert hasattr(settings, 'app_name')
        assert hasattr(settings, 'debug')

    def test_get_settings_caching(self):
        """Test settings dependency caching."""
        settings1 = get_settings()
        settings2 = get_settings()

        # Should return equivalent settings (may or may not be same instance)
        assert settings1.app_name == settings2.app_name
        assert settings1.debug == settings2.debug

    def test_get_settings_properties(self):
        """Test settings dependency properties."""
        settings = get_settings()

        # Check that essential properties exist
        essential_properties = [
            'app_name',
            'debug',
            'secret_key',
            'algorithm',
            'access_token_expire_minutes'
        ]

        for prop in essential_properties:
            assert hasattr(settings, prop)


class TestPermissionChecking:
    """Test permission checking functionality."""

    def test_check_permission_basic(self):
        """Test basic permission checking."""
        from src.cve_feed_service.models.user import UserRole

        mock_user = MagicMock()
        mock_user.role = UserRole.SECURITY_ANALYST

        # Test that function exists and is callable
        result = check_permission(mock_user, UserRole.SECURITY_ANALYST)
        assert isinstance(result, bool)

    def test_check_permission_hierarchy(self):
        """Test permission hierarchy."""
        from src.cve_feed_service.models.user import UserRole

        # Test with IT_ADMIN role
        mock_admin = MagicMock()
        mock_admin.role = UserRole.IT_ADMIN

        # Admin should have analyst permissions
        result = check_permission(mock_admin, UserRole.SECURITY_ANALYST)
        assert isinstance(result, bool)

    def test_check_permission_invalid_role(self):
        """Test permission checking with invalid role."""
        mock_user = MagicMock()
        mock_user.role = "invalid_role"

        # Should handle invalid roles gracefully
        try:
            result = check_permission(mock_user, "invalid_required_role")
            assert isinstance(result, bool)
        except Exception:
            # It's acceptable to raise an exception for invalid roles
            pass



