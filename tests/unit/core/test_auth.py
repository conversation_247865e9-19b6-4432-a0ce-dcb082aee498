"""Tests for core authentication utilities."""

import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from src.cve_feed_service.core.auth import (
    verify_password,
    get_password_hash,
    create_access_token,
    verify_token,
    generate_api_key,
    hash_api_key,
    verify_api_key
)


class TestPasswordHashing:
    """Test password hashing and verification."""

    def test_password_hashing_basic(self):
        """Test basic password hashing."""
        password = "testpassword123"
        
        # Hash the password
        hashed = get_password_hash(password)
        
        # Should return a string
        assert isinstance(hashed, str)
        assert len(hashed) > 0
        assert hashed != password  # Should be different from original

    def test_password_verification_correct(self):
        """Test password verification with correct password."""
        password = "testpassword123"
        hashed = get_password_hash(password)
        
        # Verify with correct password
        assert verify_password(password, hashed) is True

    def test_password_verification_incorrect(self):
        """Test password verification with incorrect password."""
        password = "testpassword123"
        wrong_password = "wrongpassword"
        hashed = get_password_hash(password)
        
        # Verify with wrong password
        assert verify_password(wrong_password, hashed) is False

    def test_password_hashing_different_passwords(self):
        """Test that different passwords produce different hashes."""
        password1 = "password123"
        password2 = "password456"
        
        hash1 = get_password_hash(password1)
        hash2 = get_password_hash(password2)
        
        assert hash1 != hash2

    def test_password_hashing_same_password_different_hashes(self):
        """Test that same password produces different hashes (salt)."""
        password = "testpassword123"
        
        hash1 = get_password_hash(password)
        hash2 = get_password_hash(password)
        
        # Should be different due to salt
        assert hash1 != hash2
        
        # But both should verify correctly
        assert verify_password(password, hash1) is True
        assert verify_password(password, hash2) is True

    def test_password_hashing_empty_password(self):
        """Test password hashing with empty password."""
        password = ""
        
        # Should handle empty password
        hashed = get_password_hash(password)
        assert isinstance(hashed, str)
        assert verify_password(password, hashed) is True

    def test_password_hashing_special_characters(self):
        """Test password hashing with special characters."""
        passwords = [
            "password!@#$%^&*()",
            "pássword_with_áccentš",
            "密码123",  # Chinese characters
            "🔒secure🔑password🛡️",  # Emojis
            "password\nwith\nnewlines",
            "password\twith\ttabs"
        ]
        
        for password in passwords:
            hashed = get_password_hash(password)
            assert verify_password(password, hashed) is True

    def test_password_hashing_long_password(self):
        """Test password hashing with very long password."""
        password = "x" * 100  # 100 character password (more reasonable)

        try:
            hashed = get_password_hash(password)
            assert verify_password(password, hashed) is True
        except Exception:
            # Some password hashers may have length limits
            pass


class TestTokenManagement:
    """Test JWT token creation and verification."""

    def test_create_access_token_basic(self):
        """Test basic access token creation."""
        data = {"sub": "testuser"}
        
        token = create_access_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 0
        assert "." in token  # JWT format

    def test_create_access_token_with_expiration(self):
        """Test access token creation with custom expiration."""
        data = {"sub": "testuser"}
        expires_delta = timedelta(minutes=30)
        
        token = create_access_token(data, expires_delta)
        
        assert isinstance(token, str)
        assert len(token) > 0

    def test_create_access_token_different_data(self):
        """Test that different data produces different tokens."""
        data1 = {"sub": "user1"}
        data2 = {"sub": "user2"}
        
        token1 = create_access_token(data1)
        token2 = create_access_token(data2)
        
        assert token1 != token2

    def test_verify_token_valid(self):
        """Test token verification with valid token."""
        data = {"sub": "testuser"}
        token = create_access_token(data)
        
        # Verify the token
        payload = verify_token(token)
        
        assert payload is not None
        assert payload["sub"] == "testuser"

    def test_verify_token_invalid(self):
        """Test token verification with invalid token."""
        invalid_token = "invalid.token.here"
        
        payload = verify_token(invalid_token)
        
        assert payload is None

    def test_verify_token_expired(self):
        """Test token verification with expired token."""
        data = {"sub": "testuser"}
        # Create token that expires immediately
        expires_delta = timedelta(seconds=-1)
        token = create_access_token(data, expires_delta)
        
        # Wait a moment to ensure expiration
        import time
        time.sleep(0.1)
        
        payload = verify_token(token)
        
        # Should be None due to expiration
        assert payload is None

    def test_verify_token_malformed(self):
        """Test token verification with malformed tokens."""
        malformed_tokens = [
            "",
            "not.a.token",
            "too.few.parts",
            "too.many.parts.here.extra",
            "invalid_base64.invalid_base64.invalid_base64"
        ]
        
        for token in malformed_tokens:
            payload = verify_token(token)
            assert payload is None

    def test_token_contains_expiration(self):
        """Test that created tokens contain expiration."""
        data = {"sub": "testuser"}
        token = create_access_token(data)
        
        payload = verify_token(token)
        
        assert payload is not None
        assert "exp" in payload
        assert "sub" in payload

    def test_token_custom_claims(self):
        """Test token creation with custom claims."""
        data = {
            "sub": "testuser",
            "role": "admin",
            "permissions": ["read", "write"],
            "custom_field": "custom_value"
        }
        
        token = create_access_token(data)
        payload = verify_token(token)
        
        assert payload is not None
        assert payload["sub"] == "testuser"
        assert payload["role"] == "admin"
        assert payload["permissions"] == ["read", "write"]
        assert payload["custom_field"] == "custom_value"


class TestAPIKeyManagement:
    """Test API key management functions."""

    def test_generate_api_key(self):
        """Test API key generation."""
        api_key = generate_api_key()

        assert isinstance(api_key, str)
        assert len(api_key) > 20  # Should be a substantial key

        # Generate another key to ensure they're different
        api_key2 = generate_api_key()
        assert api_key != api_key2

    def test_hash_api_key(self):
        """Test API key hashing."""
        api_key = "test_api_key_123"

        hashed = hash_api_key(api_key)

        assert isinstance(hashed, str)
        assert hashed != api_key  # Should be different from original
        assert len(hashed) > 0

    def test_verify_api_key_correct(self):
        """Test API key verification with correct key."""
        api_key = "test_api_key_123"
        hashed = hash_api_key(api_key)

        assert verify_api_key(api_key, hashed) is True

    def test_verify_api_key_incorrect(self):
        """Test API key verification with incorrect key."""
        api_key = "test_api_key_123"
        wrong_key = "wrong_api_key"
        hashed = hash_api_key(api_key)

        assert verify_api_key(wrong_key, hashed) is False

    def test_api_key_hashing_different_keys(self):
        """Test that different API keys produce different hashes."""
        key1 = "api_key_1"
        key2 = "api_key_2"

        hash1 = hash_api_key(key1)
        hash2 = hash_api_key(key2)

        assert hash1 != hash2

    def test_api_key_hashing_same_key_different_hashes(self):
        """Test that same API key produces different hashes (salt)."""
        api_key = "test_api_key"

        hash1 = hash_api_key(api_key)
        hash2 = hash_api_key(api_key)

        # Should be different due to salt
        assert hash1 != hash2

        # But both should verify correctly
        assert verify_api_key(api_key, hash1) is True
        assert verify_api_key(api_key, hash2) is True


class TestAuthenticationEdgeCases:
    """Test edge cases in authentication."""

    def test_password_verification_none_values(self):
        """Test password verification with None values."""
        password = "testpassword"
        hashed = get_password_hash(password)

        # Test with None password - should raise exception or return False
        try:
            result = verify_password(None, hashed)
            assert result is False
        except (TypeError, ValueError):
            # It's acceptable to raise an exception for None values
            pass

        # Test with None hash - should raise exception or return False
        try:
            result = verify_password(password, None)
            assert result is False
        except (TypeError, ValueError):
            # It's acceptable to raise an exception for None values
            pass

        # Test with both None - should raise exception or return False
        try:
            result = verify_password(None, None)
            assert result is False
        except (TypeError, ValueError):
            # It's acceptable to raise an exception for None values
            pass

    def test_password_hashing_none_password(self):
        """Test password hashing with None password."""
        # Should handle None gracefully
        try:
            result = get_password_hash(None)
            # If it doesn't raise an exception, it should return something
            assert result is not None
        except (TypeError, AttributeError, ValueError):
            # It's also acceptable to raise an exception for None
            pass

    def test_token_creation_empty_data(self):
        """Test token creation with empty data."""
        empty_data = {}
        
        token = create_access_token(empty_data)
        
        assert isinstance(token, str)
        assert len(token) > 0

    def test_token_creation_none_data(self):
        """Test token creation with None data."""
        try:
            token = create_access_token(None)
            assert token is not None
        except (TypeError, AttributeError, ValueError):
            # It's acceptable to raise an exception for None data
            pass

    def test_verify_token_none(self):
        """Test token verification with None token."""
        try:
            payload = verify_token(None)
            assert payload is None
        except (AttributeError, TypeError):
            # It's acceptable to raise an exception for None token
            pass

    def test_verify_token_empty_string(self):
        """Test token verification with empty string."""
        payload = verify_token("")
        assert payload is None
