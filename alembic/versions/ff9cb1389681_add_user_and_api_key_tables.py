"""Add user and API key tables

Revision ID: ff9cb1389681
Revises: 3a06de448b9e
Create Date: 2025-06-18 15:24:29.365097

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = 'ff9cb1389681'
down_revision: Union[str, None] = '3a06de448b9e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade database schema."""
    # Create users table
    op.create_table(
        'users',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('username', sa.String(length=100), nullable=False),
        sa.Column('email', sa.String(length=255), nullable=False),
        sa.Column('full_name', sa.String(length=255), nullable=True),
        sa.Column('hashed_password', sa.String(length=255), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('role', sa.Enum('it_admin', 'security_analyst', name='userrole'), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('username', name='uq_user_username_active'),
        sa.UniqueConstraint('email', name='uq_user_email_active')
    )
    op.create_index('ix_user_role_active', 'users', ['role'])
    op.create_index(op.f('ix_users_created_at'), 'users', ['created_at'])
    op.create_index(op.f('ix_users_deleted_at'), 'users', ['deleted_at'])
    op.create_index(op.f('ix_users_email'), 'users', ['email'])
    op.create_index(op.f('ix_users_is_active'), 'users', ['is_active'])
    op.create_index(op.f('ix_users_updated_at'), 'users', ['updated_at'])
    op.create_index(op.f('ix_users_username'), 'users', ['username'])

    # Create api_keys table
    op.create_table(
        'api_keys',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.String(length=36), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('key_hash', sa.String(length=255), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('last_used_at', sa.String(length=50), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('key_hash', name='uq_api_key_hash_active')
    )
    op.create_index('ix_api_key_user_active', 'api_keys', ['user_id'])
    op.create_index(op.f('ix_api_keys_created_at'), 'api_keys', ['created_at'])
    op.create_index(op.f('ix_api_keys_deleted_at'), 'api_keys', ['deleted_at'])
    op.create_index(op.f('ix_api_keys_is_active'), 'api_keys', ['is_active'])
    op.create_index(op.f('ix_api_keys_key_hash'), 'api_keys', ['key_hash'])
    op.create_index(op.f('ix_api_keys_updated_at'), 'api_keys', ['updated_at'])


def downgrade() -> None:
    """Downgrade database schema."""
    # Drop tables in reverse order
    op.drop_table('api_keys')
    op.drop_table('users')

    # Drop enum type
    op.execute("DROP TYPE IF EXISTS userrole")
