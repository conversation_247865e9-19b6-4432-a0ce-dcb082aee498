{"summary": {"total_tests": 13, "passed": 9, "failed": 4, "success_rate": 69.23076923076923}, "categories": {"health_checks": {"passed": 1, "failed": 0}, "api_tests": {"passed": 3, "failed": 1}, "frontend_tests": {"passed": 0, "failed": 3}, "integration_tests": {"passed": 2, "failed": 0}, "performance_tests": {"passed": 3, "failed": 0}}, "detailed_results": {"health_checks": [{"service": "api", "status": "healthy", "url": "http://localhost:8001/health", "response_time": 0.002172708511352539}], "api_tests": [{"endpoint": "cves", "method": "GET", "status": "passed", "response_time": 0.014391660690307617, "response_size": 4025}, {"endpoint": "applications", "method": "GET", "status": "passed", "response_time": 0.011797428131103516, "response_size": 4061}, {"endpoint": "auth/me", "method": "GET", "status": "passed", "response_time": 0.012995481491088867, "response_size": 283}], "frontend_tests": [], "integration_tests": [], "performance_tests": [{"endpoint": "cves", "avg_response_time": 0.00919647216796875, "max_response_time": 0.00965428352355957, "status": "passed"}, {"endpoint": "applications", "avg_response_time": 0.009360694885253906, "max_response_time": 0.010181188583374023, "status": "passed"}, {"endpoint": "dashboard/stats", "avg_response_time": 0.0003573894500732422, "max_response_time": 0.0003573894500732422, "status": "passed"}]}, "timestamp": **********.0758293}