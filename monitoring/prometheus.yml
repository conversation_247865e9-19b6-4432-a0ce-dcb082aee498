# Prometheus configuration for CVE Feed Service monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'cve-feed-monitor'

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics
    scrape_interval: 15s

  # CVE Feed API monitoring
  - job_name: 'cve-feed-api'
    static_configs:
      - targets: ['api:8000']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # PostgreSQL monitoring
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    metrics_path: /metrics
    scrape_interval: 30s

  # Redis monitoring
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: /metrics
    scrape_interval: 30s

  # Frontend monitoring
  - job_name: 'frontend'
    static_configs:
      - targets: ['frontend:3000']
    metrics_path: /api/metrics
    scrape_interval: 60s

  # Admin dashboard monitoring
  - job_name: 'admin'
    static_configs:
      - targets: ['admin:3000']
    metrics_path: /api/metrics
    scrape_interval: 60s

  # Worker monitoring
  - job_name: 'worker'
    static_configs:
      - targets: ['worker:8001']
    metrics_path: /metrics
    scrape_interval: 30s

  # Node exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Custom CVE metrics
  - job_name: 'cve-metrics'
    static_configs:
      - targets: ['api:8000']
    metrics_path: /api/v1/metrics/cves
    scrape_interval: 60s
    scrape_timeout: 30s

  # Application metrics
  - job_name: 'application-metrics'
    static_configs:
      - targets: ['api:8000']
    metrics_path: /api/v1/metrics/applications
    scrape_interval: 60s

  # Security metrics
  - job_name: 'security-metrics'
    static_configs:
      - targets: ['api:8000']
    metrics_path: /api/v1/metrics/security
    scrape_interval: 300s  # Every 5 minutes

  # Performance metrics
  - job_name: 'performance-metrics'
    static_configs:
      - targets: ['api:8000']
    metrics_path: /api/v1/metrics/performance
    scrape_interval: 30s

# Remote write configuration (optional - for external monitoring)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint.example.com/api/v1/write"
#     basic_auth:
#       username: "username"
#       password: "password"

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 10GB
