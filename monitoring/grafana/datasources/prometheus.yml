# Grafana datasource configuration for Prometheus

apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      manageAlerts: true
      prometheusType: Prometheus
      prometheusVersion: 2.40.0
      cacheLevel: 'High'
      disableRecordingRules: false
      incrementalQueryOverlapWindow: 10m
      queryTimeout: 60s
      timeInterval: 30s
    secureJsonData: {}

  - name: PostgreSQL
    type: postgres
    access: proxy
    url: postgres:5432
    database: cve_feed_db
    user: cve_user
    secureJsonData:
      password: cve_password_secure_2024
    jsonData:
      sslmode: disable
      maxOpenConns: 10
      maxIdleConns: 2
      connMaxLifetime: 14400
      postgresVersion: 1500
      timescaledb: false

  - name: Redis
    type: redis-datasource
    access: proxy
    url: redis:6379
    jsonData:
      client: standalone
      poolSize: 5
      timeout: 10
      pingInterval: 0
      pipelineWindow: 0
    secureJsonData:
      password: redis_password_secure_2024
