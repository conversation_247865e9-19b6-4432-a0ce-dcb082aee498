#!/usr/bin/env python3
"""
Development dashboard - Real-time system status and quick actions.
"""

import asyncio
import httpx
import sys
from datetime import datetime
import subprocess

BASE_URL = "http://localhost:8001"
FRONTEND_URL = "http://localhost:3000"

def print_header():
    """Print dashboard header."""
    print("\n" + "="*80)
    print("🚀 CVE FEED SERVICE - DEVELOPMENT DASHBOARD")
    print("="*80)
    print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def print_section(title, emoji="📊"):
    """Print section header."""
    print(f"\n{emoji} {title}")
    print("-" * 50)

async def check_service_status():
    """Check if services are running."""
    print_section("SERVICE STATUS", "🔍")
    
    # Check backend
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                health = response.json()
                print(f"✅ Backend API: {BASE_URL} (v{health.get('version', 'unknown')})")
            else:
                print(f"⚠️  Backend API: {BASE_URL} (HTTP {response.status_code})")
    except Exception as e:
        print(f"❌ Backend API: {BASE_URL} (Not responding)")
    
    # Check frontend (simple HTTP check)
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get(FRONTEND_URL)
            if response.status_code == 200:
                print(f"✅ Frontend UI: {FRONTEND_URL}")
            else:
                print(f"⚠️  Frontend UI: {FRONTEND_URL} (HTTP {response.status_code})")
    except Exception as e:
        print(f"❌ Frontend UI: {FRONTEND_URL} (Not responding)")

async def get_system_stats():
    """Get system statistics."""
    print_section("SYSTEM STATISTICS", "📊")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Login
            login_data = {"username": "<EMAIL>", "password": "password123"}
            auth_response = await client.post(f"{BASE_URL}/api/v1/auth/login", json=login_data)
            
            if auth_response.status_code != 200:
                print("❌ Authentication failed")
                return
            
            token = auth_response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            
            # Get CVE stats
            cve_response = await client.get(f"{BASE_URL}/api/v1/cves/", headers=headers)
            if cve_response.status_code == 200:
                cve_data = cve_response.json()
                print(f"📋 Total CVEs: {cve_data['total']}")
                
                # Severity breakdown
                severity_count = {}
                for cve in cve_data['cves']:
                    severity = cve.get('cvss_v3_severity', 'UNKNOWN')
                    severity_count[severity] = severity_count.get(severity, 0) + 1
                
                for severity in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']:
                    count = severity_count.get(severity, 0)
                    if count > 0:
                        emoji = {"CRITICAL": "🔴", "HIGH": "🟠", "MEDIUM": "🟡", "LOW": "🟢"}.get(severity, "⚪")
                        print(f"   {emoji} {severity}: {count}")
            
            # Get application stats
            app_response = await client.get(f"{BASE_URL}/api/v1/applications/", headers=headers)
            if app_response.status_code == 200:
                apps = app_response.json()
                print(f"📱 Applications: {len(apps)}")
                
                total_components = 0
                for app in apps:
                    app_detail_response = await client.get(f"{BASE_URL}/api/v1/applications/{app['id']}", headers=headers)
                    if app_detail_response.status_code == 200:
                        app_detail = app_detail_response.json()
                        components = len(app_detail.get('components', []))
                        total_components += components
                        print(f"   📦 {app['name']}: {components} components")
                
                print(f"🔧 Total Components: {total_components}")
            
    except Exception as e:
        print(f"❌ Error getting stats: {e}")

def print_quick_actions():
    """Print available quick actions."""
    print_section("QUICK ACTIONS", "⚡")
    print("1. 🌐 Open Frontend (http://localhost:3000)")
    print("2. 📚 Open API Docs (http://localhost:8001/api/v1/docs)")
    print("3. 🧪 Run System Test")
    print("4. 🌱 Seed Sample Data")
    print("5. 🗑️  Clear CVE Data")
    print("6. 🔄 Refresh Dashboard")
    print("7. ❌ Exit")

def print_useful_commands():
    """Print useful development commands."""
    print_section("USEFUL COMMANDS", "💻")
    print("Backend:")
    print("  nix-shell --run 'uvicorn src.cve_feed_service.main:app --reload --host 0.0.0.0 --port 8001'")
    print("\nFrontend:")
    print("  cd frontend && nix-shell -p nodejs_20 --run 'npm run dev'")
    print("\nDatabase:")
    print("  sqlite3 dev_database.db '.tables'")
    print("  sqlite3 dev_database.db 'SELECT COUNT(*) FROM cves;'")
    print("\nTesting:")
    print("  python scripts/test_full_system.py")
    print("  python scripts/seed_sample_data.py")

async def main():
    """Main dashboard function."""
    while True:
        try:
            print_header()
            await check_service_status()
            await get_system_stats()
            print_quick_actions()
            print_useful_commands()
            
            print(f"\n{'='*80}")
            choice = input("Enter your choice (1-7): ").strip()
            
            if choice == "1":
                subprocess.run(["xdg-open", FRONTEND_URL], check=False)
                print("🌐 Opening frontend...")
            elif choice == "2":
                subprocess.run(["xdg-open", f"{BASE_URL}/api/v1/docs"], check=False)
                print("📚 Opening API docs...")
            elif choice == "3":
                print("🧪 Running system test...")
                subprocess.run(["python", "scripts/test_full_system.py"], check=False)
                input("\nPress Enter to continue...")
            elif choice == "4":
                print("🌱 Seeding sample data...")
                subprocess.run(["python", "scripts/seed_sample_data.py"], check=False)
                input("\nPress Enter to continue...")
            elif choice == "5":
                confirm = input("⚠️  Are you sure you want to clear all CVE data? (y/N): ")
                if confirm.lower() == 'y':
                    print("🗑️  Clearing CVE data...")
                    subprocess.run(["python", "scripts/clear_cve_data.py"], check=False)
                    input("\nPress Enter to continue...")
            elif choice == "6":
                print("🔄 Refreshing dashboard...")
                continue
            elif choice == "7":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please try again.")
                input("Press Enter to continue...")
                
        except KeyboardInterrupt:
            print("\n\n👋 Dashboard interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Dashboard error: {e}")
            input("Press Enter to continue...")

if __name__ == "__main__":
    asyncio.run(main())
