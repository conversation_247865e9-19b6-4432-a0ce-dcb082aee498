#!/bin/bash

# CVE Feed Service Worker Entrypoint
# This script manages background tasks for CVE processing

set -e

echo "🚀 Starting CVE Feed Service Worker..."

# Wait for database to be ready
echo "⏳ Waiting for database connection..."
python -c "
import asyncio
import asyncpg
import os
import time

async def wait_for_db():
    db_url = os.getenv('DATABASE_URL', '').replace('postgresql+asyncpg://', 'postgresql://')
    max_retries = 30
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            conn = await asyncpg.connect(db_url)
            await conn.close()
            print('✅ Database connection successful!')
            return
        except Exception as e:
            retry_count += 1
            print(f'❌ Database connection failed (attempt {retry_count}/{max_retries}): {e}')
            await asyncio.sleep(2)
    
    raise Exception('Failed to connect to database after maximum retries')

asyncio.run(wait_for_db())
"

# Wait for Redis to be ready
echo "⏳ Waiting for Redis connection..."
python -c "
import redis
import os
import time

redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
max_retries = 30
retry_count = 0

while retry_count < max_retries:
    try:
        r = redis.from_url(redis_url)
        r.ping()
        print('✅ Redis connection successful!')
        break
    except Exception as e:
        retry_count += 1
        print(f'❌ Redis connection failed (attempt {retry_count}/{max_retries}): {e}')
        time.sleep(2)
else:
    raise Exception('Failed to connect to Redis after maximum retries')
"

# Run database migrations
echo "🔄 Running database migrations..."
python -m alembic upgrade head

# Start background tasks
echo "🔧 Starting background worker processes..."

# Function to run CVE sync
run_cve_sync() {
    echo "📡 Starting CVE synchronization..."
    python -c "
import asyncio
from src.cve_feed_service.services.nvd_client import NVDClient
from src.cve_feed_service.core.database import get_session
from src.cve_feed_service.services.cve_service import CVEService
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def sync_cves():
    try:
        nvd_client = NVDClient()
        async with get_session() as session:
            cve_service = CVEService(session)
            
            # Sync recent CVEs (last 7 days)
            logger.info('Starting CVE synchronization...')
            await nvd_client.sync_recent_cves(cve_service, days=7)
            logger.info('CVE synchronization completed successfully')
            
    except Exception as e:
        logger.error(f'CVE synchronization failed: {e}')
        raise

asyncio.run(sync_cves())
"
}

# Function to run metrics collection
run_metrics_collection() {
    echo "📊 Collecting system metrics..."
    python -c "
import asyncio
from src.cve_feed_service.core.database import get_session
from src.cve_feed_service.services.cve_service import CVEService
from src.cve_feed_service.services.application_service import ApplicationService
import logging
import json
import redis
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def collect_metrics():
    try:
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        r = redis.from_url(redis_url)
        
        async with get_session() as session:
            cve_service = CVEService(session)
            app_service = ApplicationService(session)
            
            # Collect CVE metrics
            total_cves = await cve_service.get_total_count()
            critical_cves = await cve_service.get_count_by_severity('CRITICAL')
            high_cves = await cve_service.get_count_by_severity('HIGH')
            
            # Collect application metrics
            total_apps = await app_service.get_total_count()
            
            # Store metrics in Redis
            metrics = {
                'total_cves': total_cves,
                'critical_cves': critical_cves,
                'high_cves': high_cves,
                'total_applications': total_apps,
                'last_updated': int(time.time())
            }
            
            r.setex('system_metrics', 300, json.dumps(metrics))  # Cache for 5 minutes
            logger.info(f'Metrics collected: {metrics}')
            
    except Exception as e:
        logger.error(f'Metrics collection failed: {e}')

import time
asyncio.run(collect_metrics())
"
}

# Function to cleanup old data
run_cleanup() {
    echo "🧹 Running data cleanup..."
    python -c "
import asyncio
from src.cve_feed_service.core.database import get_session
import logging
from datetime import datetime, timedelta

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def cleanup_old_data():
    try:
        async with get_session() as session:
            # Cleanup old logs (older than 30 days)
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            
            # Add cleanup logic here
            logger.info(f'Cleanup completed for data older than {cutoff_date}')
            
    except Exception as e:
        logger.error(f'Data cleanup failed: {e}')

asyncio.run(cleanup_old_data())
"
}

# Main worker loop
echo "🔄 Starting worker main loop..."

# Get configuration
CVE_SYNC_INTERVAL=${CVE_SYNC_INTERVAL:-3600}  # Default 1 hour
METRICS_INTERVAL=${METRICS_INTERVAL:-300}     # Default 5 minutes
CLEANUP_INTERVAL=${CLEANUP_INTERVAL:-86400}   # Default 24 hours

# Initialize counters
cve_sync_counter=0
metrics_counter=0
cleanup_counter=0

# Run initial sync
run_cve_sync
run_metrics_collection

echo "✅ Worker started successfully. Running background tasks..."

# Main loop
while true; do
    sleep 60  # Check every minute
    
    cve_sync_counter=$((cve_sync_counter + 60))
    metrics_counter=$((metrics_counter + 60))
    cleanup_counter=$((cleanup_counter + 60))
    
    # Run CVE sync
    if [ $cve_sync_counter -ge $CVE_SYNC_INTERVAL ]; then
        run_cve_sync &
        cve_sync_counter=0
    fi
    
    # Run metrics collection
    if [ $metrics_counter -ge $METRICS_INTERVAL ]; then
        run_metrics_collection &
        metrics_counter=0
    fi
    
    # Run cleanup
    if [ $cleanup_counter -ge $CLEANUP_INTERVAL ]; then
        run_cleanup &
        cleanup_counter=0
    fi
    
    # Health check
    echo "💓 Worker heartbeat - $(date)"
done
