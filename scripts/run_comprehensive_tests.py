#!/usr/bin/env python3
"""Comprehensive test runner for CVE Feed Service."""

import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, List, Tuple


class TestRunner:
    """Comprehensive test runner with detailed reporting."""

    def __init__(self):
        self.results = {}
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.start_time = time.time()

    def run_test_suite(self, name: str, command: str, timeout: int = 120) -> Tuple[bool, str, Dict]:
        """Run a test suite and capture results."""
        print(f"\n🧪 Running {name}...")
        print("=" * 80)
        
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=Path.cwd()
            )
            
            success = result.returncode == 0
            output = result.stdout
            error_output = result.stderr
            
            # Parse test results
            stats = self._parse_test_output(output)
            
            if success:
                print(f"✅ {name}: {stats.get('summary', 'All tests passed')}")
            else:
                print(f"❌ {name}: {stats.get('summary', 'Tests failed')}")
                if error_output:
                    print(f"   Error: {error_output[:200]}...")
            
            return success, output, stats
            
        except subprocess.TimeoutExpired:
            print(f"⏰ {name}: Tests timed out after {timeout} seconds")
            return False, "", {"summary": "Timeout", "passed": 0, "failed": 0}
        except Exception as e:
            print(f"💥 {name}: Error running tests - {e}")
            return False, "", {"summary": f"Error: {e}", "passed": 0, "failed": 0}

    def _parse_test_output(self, output: str) -> Dict:
        """Parse pytest output to extract test statistics."""
        lines = output.split('\n')
        stats = {"passed": 0, "failed": 0, "errors": 0, "skipped": 0}
        
        for line in lines:
            line = line.strip()
            
            # Look for pytest summary line
            if "passed" in line and ("failed" in line or "error" in line or "skipped" in line):
                stats["summary"] = line
                # Extract numbers
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == "passed":
                        try:
                            stats["passed"] = int(parts[i-1])
                        except (ValueError, IndexError):
                            pass
                    elif part == "failed":
                        try:
                            stats["failed"] = int(parts[i-1])
                        except (ValueError, IndexError):
                            pass
                    elif part == "error" or part == "errors":
                        try:
                            stats["errors"] = int(parts[i-1])
                        except (ValueError, IndexError):
                            pass
                break
            elif line.endswith("passed"):
                # Simple case: "X passed"
                try:
                    parts = line.split()
                    if len(parts) >= 2:
                        stats["passed"] = int(parts[0])
                        stats["summary"] = line
                except ValueError:
                    pass
        
        if "summary" not in stats:
            stats["summary"] = f"{stats['passed']} passed, {stats['failed']} failed"
        
        return stats

    def run_all_tests(self):
        """Run all test suites and generate comprehensive report."""
        print("🚀 CVE Feed Service - Comprehensive Test Suite")
        print("=" * 80)
        print(f"⏰ Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Define test suites
        test_suites = [
            ("Basic Functionality Tests", "python -m pytest tests/unit/test_basic_functionality.py -v"),
            ("Main API Tests", "python -m pytest tests/test_main.py -v"),
            ("BDD Scenario Tests", "python -m pytest tests/behave/test_bdd_runner.py -v"),
            ("Application Service Tests", "python -m pytest tests/unit/services/test_application_service.py -v"),
            ("CVE Service Tests", "python -m pytest tests/unit/services/test_cve_service.py -v --tb=short"),
            ("Component Service Tests", "python -m pytest tests/unit/services/test_component_service.py -v --tb=short"),
            ("Auth Service Tests", "python -m pytest tests/unit/services/test_auth_service.py -v --tb=short"),
            ("Integration Workflow Tests", "python -m pytest tests/integration/test_full_workflow.py -v --tb=short"),
            ("API Integration Tests", "python -m pytest tests/api/ -v --tb=short"),
        ]
        
        # Run each test suite
        for name, command in test_suites:
            success, output, stats = self.run_test_suite(name, f"nix-shell --run '{command}'")
            
            self.results[name] = {
                "success": success,
                "output": output,
                "stats": stats
            }
            
            self.total_tests += stats.get("passed", 0) + stats.get("failed", 0)
            self.passed_tests += stats.get("passed", 0)
            self.failed_tests += stats.get("failed", 0)
        
        # Generate final report
        self._generate_report()

    def _generate_report(self):
        """Generate comprehensive test report."""
        end_time = time.time()
        duration = end_time - self.start_time
        
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE TEST REPORT")
        print("=" * 80)
        
        # Summary statistics
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        print(f"⏱️  Total Duration: {duration:.2f} seconds")
        print(f"🧪 Total Tests: {self.total_tests}")
        print(f"✅ Passed: {self.passed_tests}")
        print(f"❌ Failed: {self.failed_tests}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        print("\n📋 Test Suite Results:")
        print("-" * 80)
        
        # Detailed results
        for name, result in self.results.items():
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            stats = result["stats"]
            summary = stats.get("summary", "No summary available")
            
            print(f"{status} {name}")
            print(f"     {summary}")
        
        # Coverage and quality metrics
        print("\n📊 Quality Metrics:")
        print("-" * 80)
        
        # Calculate test categories
        unit_tests = sum(
            self.results.get(name, {}).get("stats", {}).get("passed", 0)
            for name in self.results
            if "Service Tests" in name or "Basic Functionality" in name
        )
        
        integration_tests = sum(
            self.results.get(name, {}).get("stats", {}).get("passed", 0)
            for name in self.results
            if "Integration" in name or "API" in name
        )
        
        bdd_tests = self.results.get("BDD Scenario Tests", {}).get("stats", {}).get("passed", 0)
        
        print(f"🔧 Unit Tests: {unit_tests}")
        print(f"🔗 Integration Tests: {integration_tests}")
        print(f"📋 BDD Tests: {bdd_tests}")
        print(f"🎯 API Tests: {self.results.get('Main API Tests', {}).get('stats', {}).get('passed', 0)}")
        
        # Test coverage assessment
        print("\n🎯 Test Coverage Assessment:")
        print("-" * 80)
        
        coverage_areas = {
            "Core Functionality": unit_tests > 0,
            "API Endpoints": self.results.get("Main API Tests", {}).get("success", False),
            "Business Logic": bdd_tests > 0,
            "Service Layer": any("Service Tests" in name and self.results[name]["success"] for name in self.results),
            "Integration Workflows": integration_tests > 0,
        }
        
        for area, covered in coverage_areas.items():
            status = "✅" if covered else "❌"
            print(f"{status} {area}")
        
        # Recommendations
        print("\n💡 Recommendations:")
        print("-" * 80)
        
        if success_rate >= 90:
            print("🎉 Excellent test coverage! System is well-tested.")
        elif success_rate >= 75:
            print("👍 Good test coverage. Consider addressing failing tests.")
        elif success_rate >= 50:
            print("⚠️  Moderate test coverage. Focus on fixing critical failures.")
        else:
            print("🚨 Low test coverage. Significant testing improvements needed.")
        
        # Specific recommendations
        failed_suites = [name for name, result in self.results.items() if not result["success"]]
        
        if failed_suites:
            print(f"\n🔧 Priority: Fix failing test suites:")
            for suite in failed_suites:
                print(f"   - {suite}")
        
        if self.total_tests < 50:
            print("📈 Consider adding more comprehensive tests")
        
        if integration_tests == 0:
            print("🔗 Add integration tests for end-to-end workflows")
        
        # Final status
        print("\n" + "=" * 80)
        if success_rate >= 80:
            print("🎯 OVERALL STATUS: GOOD ✅")
            print("   System is ready for development and demonstration")
        elif success_rate >= 60:
            print("🎯 OVERALL STATUS: ACCEPTABLE ⚠️")
            print("   Core functionality working, some issues to address")
        else:
            print("🎯 OVERALL STATUS: NEEDS IMPROVEMENT ❌")
            print("   Significant testing issues need resolution")
        
        print("=" * 80)


def main():
    """Main entry point."""
    runner = TestRunner()
    
    try:
        runner.run_all_tests()
    except KeyboardInterrupt:
        print("\n\n⚠️  Test run interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
