#!/usr/bin/env python3
"""
Comprehensive system test to verify all functionality is working.
"""

import asyncio
import httpx
import json
from datetime import datetime

BASE_URL = "http://localhost:8001"

async def test_full_system():
    """Test the complete CVE Feed Service functionality."""
    
    print("🔍 CVE Feed Service - Comprehensive System Test")
    print("=" * 60)
    
    async with httpx.AsyncClient() as client:
        # 1. Authentication Test
        print("\n1️⃣  AUTHENTICATION TEST")
        print("-" * 30)
        
        login_data = {
            "username": "<EMAIL>",
            "password": "password123"
        }
        response = await client.post(f"{BASE_URL}/api/v1/auth/login", json=login_data)
        assert response.status_code == 200
        auth_data = response.json()
        token = auth_data["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        print(f"✅ Login successful - Token received")
        
        # Get user profile
        response = await client.get(f"{BASE_URL}/api/v1/auth/me", headers=headers)
        assert response.status_code == 200
        user_data = response.json()
        print(f"✅ User profile: {user_data['username']} ({user_data['role']})")
        
        # 2. CVE Data Test
        print("\n2️⃣  CVE DATA TEST")
        print("-" * 30)
        
        response = await client.get(f"{BASE_URL}/api/v1/cves/", headers=headers)
        assert response.status_code == 200
        cve_data = response.json()
        print(f"✅ CVE list: {cve_data['total']} CVEs available")
        
        # Show CVE breakdown by severity
        severity_count = {}
        for cve in cve_data['cves']:
            severity = cve.get('cvss_v3_severity', 'UNKNOWN')
            severity_count[severity] = severity_count.get(severity, 0) + 1
        
        for severity, count in severity_count.items():
            print(f"   📊 {severity}: {count} CVEs")
        
        # Test specific CVE
        if cve_data['cves']:
            test_cve = cve_data['cves'][0]
            response = await client.get(f"{BASE_URL}/api/v1/cves/{test_cve['cve_id']}", headers=headers)
            if response.status_code == 200:
                print(f"✅ CVE detail retrieval: {test_cve['cve_id']}")
            else:
                print(f"⚠️  CVE detail retrieval failed: {response.status_code}")
        
        # 3. Application Management Test
        print("\n3️⃣  APPLICATION MANAGEMENT TEST")
        print("-" * 30)
        
        response = await client.get(f"{BASE_URL}/api/v1/applications/", headers=headers)
        assert response.status_code == 200
        apps_data = response.json()
        print(f"✅ Applications list: {len(apps_data)} applications")
        
        if apps_data:
            app = apps_data[0]
            app_id = app['id']
            print(f"   📱 Test app: {app['name']} v{app['version']} ({app['environment']})")
            
            # Get application details
            response = await client.get(f"{BASE_URL}/api/v1/applications/{app_id}", headers=headers)
            assert response.status_code == 200
            app_detail = response.json()
            print(f"✅ Application details: {len(app_detail.get('components', []))} components")
            
            # 4. Component Management Test
            print("\n4️⃣  COMPONENT MANAGEMENT TEST")
            print("-" * 30)
            
            response = await client.get(f"{BASE_URL}/api/v1/components/{app_id}/components", headers=headers)
            if response.status_code == 200:
                components = response.json()
                print(f"✅ Components list: {len(components)} components")
                
                for comp in components:
                    cpe_count = len(comp.get('cpe_mappings', []))
                    print(f"   🔧 {comp['name']} v{comp['version']} - {cpe_count} CPE mappings")
            else:
                print(f"⚠️  Components list failed: {response.status_code}")
            
            # 5. CVE Feed Test
            print("\n5️⃣  CVE FEED TEST")
            print("-" * 30)
            
            # General feed
            response = await client.get(f"{BASE_URL}/api/v1/cves/feed", headers=headers)
            assert response.status_code == 200
            feed_data = response.json()
            print(f"✅ General CVE feed: {feed_data['total']} CVEs")
            
            # Application-specific feed
            response = await client.get(f"{BASE_URL}/api/v1/cves/feed?application_id={app_id}", headers=headers)
            assert response.status_code == 200
            app_feed = response.json()
            print(f"✅ Application CVE feed: {app_feed['total']} relevant CVEs")
            
            if app_feed['total'] > 0:
                print("   🚨 Relevant vulnerabilities found:")
                for cve in app_feed['cves'][:3]:
                    print(f"      - {cve['cve_id']}: {cve['cvss_v3_severity']} ({cve['cvss_v3_score']})")
            else:
                print("   ℹ️  No vulnerabilities match current component versions")
        
        # 6. API Documentation Test
        print("\n6️⃣  API DOCUMENTATION TEST")
        print("-" * 30)
        
        response = await client.get(f"{BASE_URL}/api/v1/docs")
        assert response.status_code == 200
        print("✅ API documentation accessible")
        
        response = await client.get(f"{BASE_URL}/api/v1/openapi.json")
        assert response.status_code == 200
        openapi_data = response.json()
        endpoint_count = len(openapi_data.get('paths', {}))
        print(f"✅ OpenAPI spec: {endpoint_count} endpoints documented")
        
        # 7. Health Check Test
        print("\n7️⃣  HEALTH CHECK TEST")
        print("-" * 30)
        
        response = await client.get(f"{BASE_URL}/health")
        assert response.status_code == 200
        health_data = response.json()
        print(f"✅ Health check: {health_data['status']} (v{health_data['version']})")
        
        # 8. Summary
        print("\n🎉 SYSTEM TEST SUMMARY")
        print("=" * 60)
        print("✅ Authentication & Authorization")
        print("✅ CVE Data Management")
        print("✅ Application Management")
        print("✅ Component Management")
        print("✅ CVE Feed Generation")
        print("✅ API Documentation")
        print("✅ Health Monitoring")
        print("\n🚀 CVE Feed Service is fully operational!")
        
        print(f"\n📊 CURRENT SYSTEM STATE:")
        print(f"   - Backend: {BASE_URL}")
        print(f"   - Frontend: http://localhost:3000")
        print(f"   - API Docs: {BASE_URL}/api/v1/docs")
        print(f"   - Database: SQLite (dev_database.db)")
        print(f"   - CVEs: {cve_data['total']} total")
        print(f"   - Applications: {len(apps_data)}")
        if apps_data:
            total_components = sum(len(app.get('components', [])) for app in [app_detail])
            print(f"   - Components: {total_components}")

if __name__ == "__main__":
    asyncio.run(test_full_system())
