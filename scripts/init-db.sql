-- CVE Feed Service Database Initialization
-- This script sets up the initial database structure and data

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- <PERSON><PERSON> custom types
DO $$ BEGIN
    CREATE TYPE severity_level AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('admin', 'security_analyst', 'developer', 'viewer');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE application_environment AS ENUM ('development', 'staging', 'production');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_cves_severity ON cves(severity);
CREATE INDEX IF NOT EXISTS idx_cves_published_date ON cves(published_date);
CREATE INDEX IF NOT EXISTS idx_cves_cvss_score ON cves(cvss_score);
CREATE INDEX IF NOT EXISTS idx_cves_search ON cves USING gin(to_tsvector('english', description));

CREATE INDEX IF NOT EXISTS idx_applications_name ON applications(name);
CREATE INDEX IF NOT EXISTS idx_applications_environment ON applications(environment);
CREATE INDEX IF NOT EXISTS idx_applications_created_at ON applications(created_at);

CREATE INDEX IF NOT EXISTS idx_components_name ON components(name);
CREATE INDEX IF NOT EXISTS idx_components_version ON components(version);

CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);

-- Create full-text search indexes
CREATE INDEX IF NOT EXISTS idx_cves_fulltext ON cves USING gin(
    to_tsvector('english', coalesce(cve_id, '') || ' ' || coalesce(description, ''))
);

CREATE INDEX IF NOT EXISTS idx_applications_fulltext ON applications USING gin(
    to_tsvector('english', coalesce(name, '') || ' ' || coalesce(description, ''))
);

-- Insert default admin user (password: admin123 - change in production!)
INSERT INTO users (id, username, email, hashed_password, role, is_active, created_at, updated_at)
VALUES (
    uuid_generate_v4(),
    'admin',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L3jzjvG4e', -- admin123
    'admin',
    true,
    NOW(),
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- Insert sample security analyst user (password: analyst123)
INSERT INTO users (id, username, email, hashed_password, role, is_active, created_at, updated_at)
VALUES (
    uuid_generate_v4(),
    'analyst',
    '<EMAIL>',
    '$2b$12$8Ry3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L3jzjvG4f', -- analyst123
    'security_analyst',
    true,
    NOW(),
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- Insert sample developer user (password: dev123)
INSERT INTO users (id, username, email, hashed_password, role, is_active, created_at, updated_at)
VALUES (
    uuid_generate_v4(),
    'developer',
    '<EMAIL>',
    '$2b$12$9Sz3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L3jzjvG4g', -- dev123
    'developer',
    true,
    NOW(),
    NOW()
) ON CONFLICT (email) DO NOTHING;

-- Create sample applications
INSERT INTO applications (id, name, description, version, environment, repository_url, created_at, updated_at)
VALUES 
(
    uuid_generate_v4(),
    'Web Application Frontend',
    'Main customer-facing web application built with React',
    '2.1.0',
    'production',
    'https://github.com/company/web-frontend',
    NOW(),
    NOW()
),
(
    uuid_generate_v4(),
    'API Gateway',
    'Central API gateway handling all external requests',
    '1.5.2',
    'production',
    'https://github.com/company/api-gateway',
    NOW(),
    NOW()
),
(
    uuid_generate_v4(),
    'User Service',
    'Microservice handling user authentication and management',
    '3.0.1',
    'production',
    'https://github.com/company/user-service',
    NOW(),
    NOW()
),
(
    uuid_generate_v4(),
    'Payment Service',
    'Microservice handling payment processing',
    '2.3.0',
    'production',
    'https://github.com/company/payment-service',
    NOW(),
    NOW()
),
(
    uuid_generate_v4(),
    'Development Environment',
    'Development instance of the main application',
    '2.2.0-dev',
    'development',
    'https://github.com/company/web-frontend',
    NOW(),
    NOW()
) ON CONFLICT DO NOTHING;

-- Create sample components
INSERT INTO components (id, name, version, description, license, repository_url, created_at, updated_at)
VALUES 
(
    uuid_generate_v4(),
    'React',
    '18.2.0',
    'A JavaScript library for building user interfaces',
    'MIT',
    'https://github.com/facebook/react',
    NOW(),
    NOW()
),
(
    uuid_generate_v4(),
    'Express.js',
    '4.18.2',
    'Fast, unopinionated, minimalist web framework for Node.js',
    'MIT',
    'https://github.com/expressjs/express',
    NOW(),
    NOW()
),
(
    uuid_generate_v4(),
    'PostgreSQL',
    '15.3',
    'Advanced open source relational database',
    'PostgreSQL',
    'https://github.com/postgres/postgres',
    NOW(),
    NOW()
),
(
    uuid_generate_v4(),
    'Redis',
    '7.0.11',
    'In-memory data structure store',
    'BSD-3-Clause',
    'https://github.com/redis/redis',
    NOW(),
    NOW()
),
(
    uuid_generate_v4(),
    'Nginx',
    '1.24.0',
    'HTTP and reverse proxy server',
    'BSD-2-Clause',
    'https://github.com/nginx/nginx',
    NOW(),
    NOW()
) ON CONFLICT DO NOTHING;

-- Create functions for common operations
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic updated_at updates
DO $$ 
DECLARE
    t text;
BEGIN
    FOR t IN 
        SELECT table_name 
        FROM information_schema.columns 
        WHERE column_name = 'updated_at' 
        AND table_schema = 'public'
    LOOP
        EXECUTE format('DROP TRIGGER IF EXISTS update_%I_updated_at ON %I', t, t);
        EXECUTE format('CREATE TRIGGER update_%I_updated_at 
                       BEFORE UPDATE ON %I 
                       FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()', t, t);
    END LOOP;
END $$;

-- Create materialized view for CVE statistics
CREATE MATERIALIZED VIEW IF NOT EXISTS cve_statistics AS
SELECT 
    COUNT(*) as total_cves,
    COUNT(*) FILTER (WHERE severity = 'CRITICAL') as critical_count,
    COUNT(*) FILTER (WHERE severity = 'HIGH') as high_count,
    COUNT(*) FILTER (WHERE severity = 'MEDIUM') as medium_count,
    COUNT(*) FILTER (WHERE severity = 'LOW') as low_count,
    AVG(cvss_score) as avg_cvss_score,
    MAX(published_date) as latest_cve_date,
    COUNT(*) FILTER (WHERE published_date >= CURRENT_DATE - INTERVAL '7 days') as recent_cves
FROM cves;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_cve_statistics_refresh ON cve_statistics (total_cves);

-- Create function to refresh statistics
CREATE OR REPLACE FUNCTION refresh_cve_statistics()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY cve_statistics;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT USAGE ON SCHEMA public TO cve_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO cve_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO cve_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO cve_user;

-- Create notification function for real-time updates
CREATE OR REPLACE FUNCTION notify_cve_changes()
RETURNS trigger AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM pg_notify('cve_changes', json_build_object(
            'operation', 'INSERT',
            'cve_id', NEW.cve_id,
            'severity', NEW.severity
        )::text);
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        PERFORM pg_notify('cve_changes', json_build_object(
            'operation', 'UPDATE',
            'cve_id', NEW.cve_id,
            'severity', NEW.severity
        )::text);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM pg_notify('cve_changes', json_build_object(
            'operation', 'DELETE',
            'cve_id', OLD.cve_id
        )::text);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for CVE notifications
DROP TRIGGER IF EXISTS cve_changes_trigger ON cves;
CREATE TRIGGER cve_changes_trigger
    AFTER INSERT OR UPDATE OR DELETE ON cves
    FOR EACH ROW EXECUTE FUNCTION notify_cve_changes();

-- Log initialization completion
DO $$
BEGIN
    RAISE NOTICE 'CVE Feed Service database initialization completed successfully';
END $$;
