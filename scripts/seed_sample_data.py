#!/usr/bin/env python3
"""
Seed the database with sample CVE data for development and testing.
"""

import asyncio
import sys
from datetime import datetime, timezone
from pathlib import Path

# Add src to path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from cve_feed_service.db.database import get_engine
from cve_feed_service.models.cve import CVE
from cve_feed_service.models.application import Component, CPEMapping
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

# Sample CVE data based on common vulnerabilities
SAMPLE_CVES = [
    {
        "cve_id": "CVE-2024-0001",
        "description": "A critical remote code execution vulnerability in nginx versions prior to 1.22.0. An attacker can exploit this vulnerability by sending specially crafted HTTP requests to execute arbitrary code on the server.",
        "published_date": datetime(2024, 1, 15, 10, 0, 0, tzinfo=timezone.utc),
        "last_modified_date": datetime(2024, 1, 15, 10, 0, 0, tzinfo=timezone.utc),
        "cvss_v3_score": 9.8,
        "cvss_v3_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H",
        "cvss_v3_severity": "CRITICAL",
        "cvss_v2_score": 10.0,
        "cvss_v2_vector": "AV:N/AC:L/Au:N/C:C/I:C/A:C",
        "cwe_ids": ["CWE-78", "CWE-94"],
        "references": [
            {"url": "https://nginx.org/en/security_advisories.html", "source": "nginx"},
            {"url": "https://nvd.nist.gov/vuln/detail/CVE-2024-0001", "source": "NVD"}
        ],
        "vendor_advisories": [
            {"url": "https://nginx.org/security/CVE-2024-0001", "vendor": "nginx"}
        ],
        "source": "NVD"
    },
    {
        "cve_id": "CVE-2024-0002", 
        "description": "A high severity vulnerability in Node.js versions 18.x prior to 18.19.0. The vulnerability allows for prototype pollution attacks that can lead to remote code execution in certain configurations.",
        "published_date": datetime(2024, 2, 1, 14, 30, 0, tzinfo=timezone.utc),
        "last_modified_date": datetime(2024, 2, 1, 14, 30, 0, tzinfo=timezone.utc),
        "cvss_v3_score": 8.1,
        "cvss_v3_vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H",
        "cvss_v3_severity": "HIGH",
        "cvss_v2_score": 7.5,
        "cvss_v2_vector": "AV:N/AC:L/Au:N/C:P/I:P/A:P",
        "cwe_ids": ["CWE-1321", "CWE-94"],
        "references": [
            {"url": "https://nodejs.org/en/blog/vulnerability/february-2024-security-releases/", "source": "nodejs"},
            {"url": "https://nvd.nist.gov/vuln/detail/CVE-2024-0002", "source": "NVD"}
        ],
        "vendor_advisories": [
            {"url": "https://nodejs.org/security/CVE-2024-0002", "vendor": "nodejs"}
        ],
        "source": "NVD"
    },
    {
        "cve_id": "CVE-2024-0003",
        "description": "A medium severity cross-site scripting (XSS) vulnerability in Express.js versions prior to 4.19.0. Improper input validation in the template engine allows attackers to inject malicious scripts.",
        "published_date": datetime(2024, 3, 10, 9, 15, 0, tzinfo=timezone.utc),
        "last_modified_date": datetime(2024, 3, 10, 9, 15, 0, tzinfo=timezone.utc),
        "cvss_v3_score": 6.1,
        "cvss_v3_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N",
        "cvss_v3_severity": "MEDIUM",
        "cvss_v2_score": 4.3,
        "cvss_v2_vector": "AV:N/AC:M/Au:N/C:N/I:P/A:N",
        "cwe_ids": ["CWE-79"],
        "references": [
            {"url": "https://expressjs.com/en/advanced/security-updates.html", "source": "expressjs"},
            {"url": "https://nvd.nist.gov/vuln/detail/CVE-2024-0003", "source": "NVD"}
        ],
        "vendor_advisories": [
            {"url": "https://expressjs.com/security/CVE-2024-0003", "vendor": "expressjs"}
        ],
        "source": "NVD"
    },
    {
        "cve_id": "CVE-2023-9999",
        "description": "A low severity information disclosure vulnerability in nginx versions 1.20.x through 1.21.x. Server configuration details may be leaked through error messages under specific conditions.",
        "published_date": datetime(2023, 12, 5, 16, 45, 0, tzinfo=timezone.utc),
        "last_modified_date": datetime(2023, 12, 5, 16, 45, 0, tzinfo=timezone.utc),
        "cvss_v3_score": 3.7,
        "cvss_v3_vector": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:N/A:N",
        "cvss_v3_severity": "LOW",
        "cvss_v2_score": 2.6,
        "cvss_v2_vector": "AV:N/AC:H/Au:N/C:P/I:N/A:N",
        "cwe_ids": ["CWE-200"],
        "references": [
            {"url": "https://nginx.org/en/security_advisories.html", "source": "nginx"},
            {"url": "https://nvd.nist.gov/vuln/detail/CVE-2023-9999", "source": "NVD"}
        ],
        "vendor_advisories": [],
        "source": "NVD"
    }
]

# CPE mappings for our components
CPE_MAPPINGS = [
    {
        "component_name": "nginx",
        "component_version": "1.21.6",
        "cpe_string": "cpe:2.3:a:nginx:nginx:1.21.6:*:*:*:*:*:*:*",
        "confidence": 1.0,
        "mapping_source": "MANUAL"
    },
    {
        "component_name": "nodejs", 
        "component_version": "18.17.0",
        "cpe_string": "cpe:2.3:a:nodejs:node.js:18.17.0:*:*:*:*:*:*:*",
        "confidence": 1.0,
        "mapping_source": "MANUAL"
    },
    {
        "component_name": "express",
        "component_version": "4.18.2", 
        "cpe_string": "cpe:2.3:a:expressjs:express:4.18.2:*:*:*:*:node.js:*:*",
        "confidence": 1.0,
        "mapping_source": "MANUAL"
    }
]

async def seed_sample_data():
    """Seed the database with sample CVE data and CPE mappings."""
    print("🌱 Seeding database with sample data...")
    
    engine = get_engine()
    
    async with AsyncSession(engine) as session:
        try:
            # 1. Add sample CVEs
            print("\n📊 Adding sample CVE data...")
            cve_count = 0
            
            for cve_data in SAMPLE_CVES:
                # Check if CVE already exists
                result = await session.execute(
                    select(CVE).where(CVE.cve_id == cve_data["cve_id"])
                )
                existing_cve = result.scalar_one_or_none()
                
                if not existing_cve:
                    cve = CVE(**cve_data)
                    session.add(cve)
                    cve_count += 1
                    print(f"   ✅ Added {cve_data['cve_id']} ({cve_data['cvss_v3_severity']})")
                else:
                    print(f"   ⏭️  Skipped {cve_data['cve_id']} (already exists)")
            
            # 2. Add CPE mappings to components
            print(f"\n🔗 Adding CPE mappings to components...")
            cpe_count = 0
            
            for cpe_data in CPE_MAPPINGS:
                # Find the component
                result = await session.execute(
                    select(Component).where(
                        Component.name == cpe_data["component_name"],
                        Component.version == cpe_data["component_version"],
                        Component.deleted_at.is_(None)
                    )
                )
                component = result.scalar_one_or_none()
                
                if component:
                    # Check if CPE mapping already exists
                    result = await session.execute(
                        select(CPEMapping).where(
                            CPEMapping.component_id == component.id,
                            CPEMapping.cpe_string == cpe_data["cpe_string"]
                        )
                    )
                    existing_mapping = result.scalar_one_or_none()
                    
                    if not existing_mapping:
                        cpe_mapping = CPEMapping(
                            component_id=component.id,
                            cpe_string=cpe_data["cpe_string"],
                            confidence=cpe_data["confidence"],
                            mapping_source=cpe_data["mapping_source"]
                        )
                        session.add(cpe_mapping)
                        cpe_count += 1
                        print(f"   ✅ Added CPE mapping for {cpe_data['component_name']} v{cpe_data['component_version']}")
                    else:
                        print(f"   ⏭️  Skipped CPE mapping for {cpe_data['component_name']} (already exists)")
                else:
                    print(f"   ❌ Component not found: {cpe_data['component_name']} v{cpe_data['component_version']}")
            
            # Commit all changes
            await session.commit()
            
            print(f"\n🎉 Sample data seeding completed!")
            print(f"   📊 CVEs added: {cve_count}")
            print(f"   🔗 CPE mappings added: {cpe_count}")
            
        except Exception as e:
            await session.rollback()
            print(f"\n❌ Error seeding data: {e}")
            raise
        finally:
            await engine.dispose()

if __name__ == "__main__":
    asyncio.run(seed_sample_data())
