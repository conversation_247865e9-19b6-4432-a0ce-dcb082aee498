# CVE Feed Service Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=postgresql+asyncpg://cve_user:cve_password_secure_2024@postgres:5432/cve_feed_db
POSTGRES_DB=cve_feed_db
POSTGRES_USER=cve_user
POSTGRES_PASSWORD=cve_password_secure_2024

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://:redis_password_secure_2024@redis:6379/0
REDIS_PASSWORD=redis_password_secure_2024

# =============================================================================
# APPLICATION SECURITY
# =============================================================================
SECRET_KEY=your-super-secret-key-change-in-production-2024
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production-2024
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
ENVIRONMENT=production
LOG_LEVEL=INFO
DEBUG=false
CORS_ORIGINS=https://app.feedme.localhost,https://admin.feedme.localhost

# =============================================================================
# EXTERNAL API KEYS
# =============================================================================
# NVD API Key (get from https://nvd.nist.gov/developers/request-an-api-key)
NVD_API_KEY=your-nvd-api-key-here

# Sentry DSN for error tracking (optional)
SENTRY_DSN=https://your-sentry-dsn-here

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# =============================================================================
# WORKER CONFIGURATION
# =============================================================================
WORKER_CONCURRENCY=4
CVE_SYNC_INTERVAL=3600
METRICS_INTERVAL=300
CLEANUP_INTERVAL=86400

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
GRAFANA_ADMIN_PASSWORD=admin_password_secure_2024
PROMETHEUS_RETENTION_TIME=30d
PROMETHEUS_RETENTION_SIZE=10GB

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
NEXT_PUBLIC_API_URL=https://api.feedme.localhost
NEXT_PUBLIC_APP_ENV=production
NEXT_PUBLIC_SENTRY_DSN=${SENTRY_DSN}

# =============================================================================
# ADMIN DASHBOARD CONFIGURATION
# =============================================================================
REACT_APP_API_URL=https://api.feedme.localhost
REACT_APP_ENV=production

# =============================================================================
# DOMAIN CONFIGURATION
# =============================================================================
# These domains should be configured in your Traefik setup
API_DOMAIN=api.feedme.localhost
APP_DOMAIN=app.feedme.localhost
ADMIN_DOMAIN=admin.feedme.localhost
DOCS_DOMAIN=docs.feedme.localhost
METRICS_DOMAIN=metrics.feedme.localhost
DASHBOARD_DOMAIN=dashboard.feedme.localhost

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
# Set to true if using HTTPS (recommended for production)
USE_HTTPS=true
SSL_CERT_PATH=/etc/ssl/certs/feedme.localhost.crt
SSL_KEY_PATH=/etc/ssl/private/feedme.localhost.key

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=cve-feed-backups
BACKUP_S3_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# =============================================================================
# EMAIL CONFIGURATION (for notifications)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>

# =============================================================================
# NOTIFICATION SETTINGS
# =============================================================================
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SLACK_NOTIFICATIONS=false
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
MAX_CONNECTIONS=100
CONNECTION_TIMEOUT=30
QUERY_TIMEOUT=30
CACHE_TTL=300

# =============================================================================
# DEVELOPMENT SETTINGS (only for development environment)
# =============================================================================
# Uncomment these for development
# ENVIRONMENT=development
# DEBUG=true
# LOG_LEVEL=DEBUG
# CORS_ORIGINS=http://localhost:3000,http://localhost:3001
