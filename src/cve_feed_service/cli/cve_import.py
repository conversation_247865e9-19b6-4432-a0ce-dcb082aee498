"""CLI command for CVE data import."""

import asyncio
import sys
from typing import Optional

import structlog
import typer
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.config import get_settings
from ..db.database import AsyncSessionLocal
from ..services.cve_ingestion_service import CVEIngestionService

logger = structlog.get_logger(__name__)
settings = get_settings()

app = typer.Typer(help="CVE data import commands")


@app.command()
def bulk_import(
    years: int = typer.Option(2, help="Number of years to import"),
    force: bool = typer.Option(False, help="Force import even if data exists"),
) -> None:
    """Perform bulk import of CVE data from NVD."""
    asyncio.run(_bulk_import(years, force))


@app.command()
def incremental_update(
    hours: int = typer.Option(24, help="Number of hours to look back for updates"),
) -> None:
    """Perform incremental update of CVE data."""
    asyncio.run(_incremental_update(hours))


@app.command()
def import_single(
    cve_id: str = typer.Argument(..., help="CVE ID to import (e.g., CVE-2023-1234)"),
) -> None:
    """Import a single CVE by ID."""
    asyncio.run(_import_single(cve_id))


async def _bulk_import(years: int, force: bool) -> None:
    """Perform bulk import of CVE data."""
    logger.info("Starting bulk CVE import", years=years, force=force)
    
    async with AsyncSessionLocal() as db:
        try:
            # Check if we already have recent CVE data
            if not force:
                from ..models.cve import CVE
                from sqlalchemy import func, select
                
                result = await db.execute(select(func.count(CVE.id)))
                existing_count = result.scalar()
                
                if existing_count > 1000:  # Arbitrary threshold
                    logger.warning(
                        "CVE data already exists. Use --force to reimport",
                        existing_count=existing_count,
                    )
                    return
            
            service = CVEIngestionService(db)
            processed_count = await service.perform_bulk_import(years)
            
            logger.info("Bulk import completed", processed_count=processed_count)
            typer.echo(f"Successfully imported {processed_count} CVEs")
            
        except Exception as e:
            logger.error("Bulk import failed", error=str(e))
            typer.echo(f"Import failed: {e}", err=True)
            sys.exit(1)


async def _incremental_update(hours: int) -> None:
    """Perform incremental update of CVE data."""
    logger.info("Starting incremental CVE update", hours=hours)
    
    async with AsyncSessionLocal() as db:
        try:
            service = CVEIngestionService(db)
            processed_count = await service.perform_incremental_update(hours)
            
            logger.info("Incremental update completed", processed_count=processed_count)
            typer.echo(f"Successfully updated {processed_count} CVEs")
            
        except Exception as e:
            logger.error("Incremental update failed", error=str(e))
            typer.echo(f"Update failed: {e}", err=True)
            sys.exit(1)


async def _import_single(cve_id: str) -> None:
    """Import a single CVE by ID."""
    logger.info("Importing single CVE", cve_id=cve_id)
    
    async with AsyncSessionLocal() as db:
        try:
            from ..services.nvd_client import NVDAPIClient
            
            async with NVDAPIClient() as client:
                cve_data = await client.get_cve_by_id(cve_id)
                
                if not cve_data:
                    typer.echo(f"CVE {cve_id} not found", err=True)
                    sys.exit(1)
                
                service = CVEIngestionService(db)
                cve = await service.ingest_cve(cve_data["cve"])
                await db.commit()
                
                logger.info("Single CVE import completed", cve_id=cve.cve_id)
                typer.echo(f"Successfully imported {cve.cve_id}")
                
        except Exception as e:
            logger.error("Single CVE import failed", cve_id=cve_id, error=str(e))
            typer.echo(f"Import failed: {e}", err=True)
            sys.exit(1)


if __name__ == "__main__":
    app()
