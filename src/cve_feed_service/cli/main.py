"""Main CLI entry point for CVE Feed Service."""

import typer

from . import cve_import

app = typer.Typer(help="CVE Feed Service CLI")

# Add subcommands
app.add_typer(cve_import.app, name="cve", help="CVE data management commands")


@app.command()
def version() -> None:
    """Show version information."""
    from .. import __version__
    typer.echo(f"CVE Feed Service v{__version__}")


if __name__ == "__main__":
    app()
