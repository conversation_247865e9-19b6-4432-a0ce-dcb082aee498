"""Application and Component models."""

from typing import List, Optional
from uuid import UUID

from sqlalchemy import ForeignKey, Index, String, Text, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..db.base import BaseModel


class Application(BaseModel):
    """Application model for inventory management."""

    __tablename__ = "applications"

    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    version: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    owner: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    environment: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    criticality: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)

    # Relationships
    components: Mapped[List["Component"]] = relationship(
        "Component",
        back_populates="application",
        cascade="all, delete-orphan",
        lazy="selectin",
    )

    __table_args__ = (
        # Partial unique constraint for active records only
        UniqueConstraint(
            "name",
            "environment",
            name="uq_application_name_env_active",
        ),
        Index(
            "ix_application_name_active",
            "name",
        ),
        Index(
            "ix_application_criticality_active",
            "criticality",
        ),
    )

    def __repr__(self) -> str:
        """String representation of the application."""
        return f"<Application(id={self.id}, name='{self.name}', env='{self.environment}')>"


class Component(BaseModel):
    """Component model for application dependencies."""

    __tablename__ = "components"

    application_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("applications.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    version: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    vendor: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    component_type: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Relationships
    application: Mapped["Application"] = relationship(
        "Application",
        back_populates="components",
        lazy="selectin",
    )
    cpe_mappings: Mapped[List["CPEMapping"]] = relationship(
        "CPEMapping",
        back_populates="component",
        cascade="all, delete-orphan",
        lazy="selectin",
    )

    __table_args__ = (
        # Partial unique constraint for active records only
        UniqueConstraint(
            "application_id",
            "name",
            "version",
            name="uq_component_app_name_version_active",
        ),
        Index(
            "ix_component_name_active",
            "name",
        ),
        Index(
            "ix_component_type_active",
            "component_type",
        ),
    )

    def __repr__(self) -> str:
        """String representation of the component."""
        return f"<Component(id={self.id}, name='{self.name}', version='{self.version}')>"


class CPEMapping(BaseModel):
    """CPE mapping model for component vulnerability matching."""

    __tablename__ = "cpe_mappings"

    component_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("components.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    cpe_string: Mapped[str] = mapped_column(String(500), nullable=False, index=True)
    confidence: Mapped[Optional[float]] = mapped_column(nullable=True)
    mapping_source: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)

    # Relationships
    component: Mapped["Component"] = relationship(
        "Component",
        back_populates="cpe_mappings",
        lazy="selectin",
    )

    __table_args__ = (
        # Partial unique constraint for active records only
        UniqueConstraint(
            "component_id",
            "cpe_string",
            name="uq_cpe_mapping_component_cpe_active",
        ),
        Index(
            "ix_cpe_string_active",
            "cpe_string",
        ),
    )

    def __repr__(self) -> str:
        """String representation of the CPE mapping."""
        return f"<CPEMapping(id={self.id}, cpe='{self.cpe_string}')>"
