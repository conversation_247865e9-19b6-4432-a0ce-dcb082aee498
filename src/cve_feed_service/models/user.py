"""User and authentication models."""

import enum
from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>um, ForeignKey, Index, String, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..db.base import BaseModel


class UserRole(str, enum.Enum):
    """User roles for RBAC."""

    IT_ADMIN = "it_admin"
    SECURITY_ANALYST = "security_analyst"


class User(BaseModel):
    """User model for authentication."""

    __tablename__ = "users"

    username: Mapped[str] = mapped_column(String(100), nullable=False, unique=True, index=True)
    email: Mapped[str] = mapped_column(String(255), nullable=False, unique=True, index=True)
    full_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    hashed_password: Mapped[str] = mapped_column(String(255), nullable=False)
    is_active: Mapped[bool] = mapped_column(<PERSON>olean, nullable=False, default=True, index=True)
    role: Mapped[UserRole] = mapped_column(Enum(UserRole), nullable=False, index=True)

    # Relationships
    api_keys: Mapped[List["APIKey"]] = relationship(
        "APIKey",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy="selectin",
    )

    __table_args__ = (
        # Partial unique constraints for active records only
        UniqueConstraint(
            "username",
            name="uq_user_username_active",
        ),
        UniqueConstraint(
            "email", 
            name="uq_user_email_active",
        ),
        Index(
            "ix_user_role_active",
            "role",
        ),
    )

    def __repr__(self) -> str:
        """String representation of the user."""
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"

    @property
    def is_admin(self) -> bool:
        """Check if user has admin role."""
        return self.role == UserRole.IT_ADMIN

    @property
    def is_analyst(self) -> bool:
        """Check if user has security analyst role."""
        return self.role == UserRole.SECURITY_ANALYST


class APIKey(BaseModel):
    """API key model for programmatic access."""

    __tablename__ = "api_keys"

    user_id: Mapped[str] = mapped_column(
        String(36),  # UUID as string
        ForeignKey("users.id"),
        nullable=False,
        index=True,
    )
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    key_hash: Mapped[str] = mapped_column(String(255), nullable=False, unique=True, index=True)
    is_active: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True, index=True)
    last_used_at: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # ISO datetime string

    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="api_keys",
        lazy="selectin",
    )

    __table_args__ = (
        UniqueConstraint(
            "key_hash",
            name="uq_api_key_hash_active",
        ),
        Index(
            "ix_api_key_user_active",
            "user_id",
        ),
    )

    def __repr__(self) -> str:
        """String representation of the API key."""
        return f"<APIKey(id={self.id}, name='{self.name}', user_id='{self.user_id}')>"
