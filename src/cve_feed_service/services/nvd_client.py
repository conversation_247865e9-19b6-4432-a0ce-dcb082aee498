"""NVD API client for CVE data ingestion."""

import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List, Optional

import httpx
import structlog
from tenacity import retry, stop_after_attempt, wait_exponential

from ..core.config import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()


class NVDAPIClient:
    """Client for interacting with the NVD CVE API."""

    def __init__(self) -> None:
        """Initialize the NVD API client."""
        self.base_url = settings.nvd_api_base_url
        self.api_key = settings.nvd_api_key
        self.rate_limit = settings.nvd_rate_limit_per_minute
        self.timeout = settings.nvd_request_timeout
        
        # Rate limiting
        self._last_request_time = 0.0
        self._request_interval = 60.0 / self.rate_limit  # seconds between requests
        
        # HTTP client
        headers = {"User-Agent": "CVE-Feed-Service/0.1.0"}
        if self.api_key:
            headers["apiKey"] = self.api_key
        
        self.client = httpx.AsyncClient(
            headers=headers,
            timeout=self.timeout,
        )

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.client.aclose()

    async def _rate_limit(self) -> None:
        """Enforce rate limiting."""
        current_time = asyncio.get_event_loop().time()
        time_since_last = current_time - self._last_request_time
        
        if time_since_last < self._request_interval:
            sleep_time = self._request_interval - time_since_last
            logger.debug("Rate limiting", sleep_time=sleep_time)
            await asyncio.sleep(sleep_time)
        
        self._last_request_time = asyncio.get_event_loop().time()

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
    )
    async def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Make a rate-limited request to the NVD API."""
        await self._rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        logger.debug("Making NVD API request", url=url, params=params)
        
        try:
            response = await self.client.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            logger.debug("NVD API response", status_code=response.status_code, total_results=data.get("totalResults", 0))
            
            return data
        except httpx.HTTPStatusError as e:
            logger.error("NVD API HTTP error", status_code=e.response.status_code, response=e.response.text)
            raise
        except Exception as e:
            logger.error("NVD API request failed", error=str(e))
            raise

    async def get_cves(
        self,
        start_index: int = 0,
        results_per_page: int = 100,
        pub_start_date: Optional[datetime] = None,
        pub_end_date: Optional[datetime] = None,
        last_mod_start_date: Optional[datetime] = None,
        last_mod_end_date: Optional[datetime] = None,
        cve_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Get CVEs from the NVD API.
        
        Args:
            start_index: Starting index for pagination
            results_per_page: Number of results per page (max 2000)
            pub_start_date: Filter by publication start date
            pub_end_date: Filter by publication end date
            last_mod_start_date: Filter by last modified start date
            last_mod_end_date: Filter by last modified end date
            cve_id: Specific CVE ID to retrieve
            
        Returns:
            NVD API response data
        """
        params = {
            "startIndex": start_index,
            "resultsPerPage": min(results_per_page, 2000),  # NVD API limit
        }
        
        if pub_start_date:
            params["pubStartDate"] = pub_start_date.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
        if pub_end_date:
            params["pubEndDate"] = pub_end_date.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
        if last_mod_start_date:
            params["lastModStartDate"] = last_mod_start_date.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
        if last_mod_end_date:
            params["lastModEndDate"] = last_mod_end_date.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
        if cve_id:
            params["cveId"] = cve_id
        
        return await self._make_request("cves/2.0", params)

    async def get_cve_by_id(self, cve_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific CVE by ID.
        
        Args:
            cve_id: CVE identifier (e.g., "CVE-2023-1234")
            
        Returns:
            CVE data or None if not found
        """
        try:
            response = await self.get_cves(cve_id=cve_id)
            vulnerabilities = response.get("vulnerabilities", [])
            
            if vulnerabilities:
                return vulnerabilities[0]
            
            return None
        except Exception as e:
            logger.error("Failed to get CVE by ID", cve_id=cve_id, error=str(e))
            return None

    async def get_recent_cves(self, days: int = 7) -> List[Dict[str, Any]]:
        """Get CVEs published in the last N days.
        
        Args:
            days: Number of days to look back
            
        Returns:
            List of CVE data
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        all_cves = []
        start_index = 0
        results_per_page = 2000
        
        while True:
            response = await self.get_cves(
                start_index=start_index,
                results_per_page=results_per_page,
                pub_start_date=start_date,
                pub_end_date=end_date,
            )
            
            vulnerabilities = response.get("vulnerabilities", [])
            all_cves.extend(vulnerabilities)
            
            total_results = response.get("totalResults", 0)
            if start_index + len(vulnerabilities) >= total_results:
                break
            
            start_index += results_per_page
        
        logger.info("Retrieved recent CVEs", count=len(all_cves), days=days)
        return all_cves

    async def get_cves_by_date_range(
        self,
        start_date: datetime,
        end_date: datetime,
        use_last_modified: bool = False,
    ) -> List[Dict[str, Any]]:
        """Get CVEs within a date range.
        
        Args:
            start_date: Start date for filtering
            end_date: End date for filtering
            use_last_modified: Use last modified date instead of publication date
            
        Returns:
            List of CVE data
        """
        all_cves = []
        start_index = 0
        results_per_page = 2000
        
        while True:
            if use_last_modified:
                response = await self.get_cves(
                    start_index=start_index,
                    results_per_page=results_per_page,
                    last_mod_start_date=start_date,
                    last_mod_end_date=end_date,
                )
            else:
                response = await self.get_cves(
                    start_index=start_index,
                    results_per_page=results_per_page,
                    pub_start_date=start_date,
                    pub_end_date=end_date,
                )
            
            vulnerabilities = response.get("vulnerabilities", [])
            all_cves.extend(vulnerabilities)
            
            total_results = response.get("totalResults", 0)
            if start_index + len(vulnerabilities) >= total_results:
                break
            
            start_index += results_per_page
        
        logger.info(
            "Retrieved CVEs by date range",
            count=len(all_cves),
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            use_last_modified=use_last_modified,
        )
        return all_cves
