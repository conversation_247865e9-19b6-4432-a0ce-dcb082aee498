"""CVE ingestion service for processing NVD data."""

from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

import structlog
from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from ..core.config import get_settings
from ..models.cve import CVE, CVECPEApplicability
from .nvd_client import NVDAPIClient

logger = structlog.get_logger(__name__)
settings = get_settings()


class CVEIngestionService:
    """Service for ingesting CVE data from NVD."""

    def __init__(self, db: AsyncSession) -> None:
        """Initialize the service."""
        self.db = db

    def _parse_cvss_data(self, cve_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse CVSS data from NVD CVE data."""
        metrics = cve_data.get("metrics", {})
        
        # CVSS v3.x data
        cvss_v3_data = {}
        cvss_v31 = metrics.get("cvssMetricV31", [])
        cvss_v30 = metrics.get("cvssMetricV30", [])
        
        if cvss_v31:
            cvss_data = cvss_v31[0].get("cvssData", {})
            cvss_v3_data = {
                "cvss_v3_score": cvss_data.get("baseScore"),
                "cvss_v3_vector": cvss_data.get("vectorString"),
                "cvss_v3_severity": cvss_data.get("baseSeverity"),
            }
        elif cvss_v30:
            cvss_data = cvss_v30[0].get("cvssData", {})
            cvss_v3_data = {
                "cvss_v3_score": cvss_data.get("baseScore"),
                "cvss_v3_vector": cvss_data.get("vectorString"),
                "cvss_v3_severity": cvss_data.get("baseSeverity"),
            }
        
        # CVSS v2 data
        cvss_v2_data = {}
        cvss_v2 = metrics.get("cvssMetricV2", [])
        if cvss_v2:
            cvss_data = cvss_v2[0].get("cvssData", {})
            cvss_v2_data = {
                "cvss_v2_score": cvss_data.get("baseScore"),
                "cvss_v2_vector": cvss_data.get("vectorString"),
            }
        
        return {**cvss_v3_data, **cvss_v2_data}

    def _parse_cwe_data(self, cve_data: Dict[str, Any]) -> List[str]:
        """Parse CWE IDs from NVD CVE data."""
        weaknesses = cve_data.get("weaknesses", [])
        cwe_ids = []
        
        for weakness in weaknesses:
            descriptions = weakness.get("description", [])
            for desc in descriptions:
                if desc.get("lang") == "en":
                    cwe_id = desc.get("value", "")
                    if cwe_id.startswith("CWE-"):
                        cwe_ids.append(cwe_id)
        
        return cwe_ids

    def _parse_references(self, cve_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Parse references from NVD CVE data."""
        references = cve_data.get("references", [])
        parsed_refs = []
        
        for ref in references:
            parsed_refs.append({
                "url": ref.get("url"),
                "source": ref.get("source"),
                "tags": ref.get("tags", []),
            })
        
        return parsed_refs

    def _parse_description(self, cve_data: Dict[str, Any]) -> Optional[str]:
        """Parse description from NVD CVE data."""
        descriptions = cve_data.get("descriptions", [])
        
        for desc in descriptions:
            if desc.get("lang") == "en":
                return desc.get("value")
        
        return None

    def _parse_dates(self, cve_data: Dict[str, Any]) -> Tuple[Optional[datetime], Optional[datetime]]:
        """Parse publication and last modified dates."""
        published_str = cve_data.get("published")
        last_modified_str = cve_data.get("lastModified")
        
        published_date = None
        last_modified_date = None
        
        if published_str:
            try:
                published_date = datetime.fromisoformat(published_str.replace('Z', '+00:00'))
            except ValueError:
                logger.warning("Failed to parse published date", date=published_str)
        
        if last_modified_str:
            try:
                last_modified_date = datetime.fromisoformat(last_modified_str.replace('Z', '+00:00'))
            except ValueError:
                logger.warning("Failed to parse last modified date", date=last_modified_str)
        
        return published_date, last_modified_date

    async def _create_or_update_cve(self, cve_data: Dict[str, Any]) -> CVE:
        """Create or update a CVE record."""
        cve_id = cve_data.get("id")
        if not cve_id:
            raise ValueError("CVE data missing ID")
        
        # Check if CVE already exists
        result = await self.db.execute(
            select(CVE).where(
                and_(
                    CVE.cve_id == cve_id,
                    CVE.deleted_at.is_(None),
                )
            )
        )
        cve = result.scalar_one_or_none()
        
        # Parse CVE data
        cvss_data = self._parse_cvss_data(cve_data)
        cwe_ids = self._parse_cwe_data(cve_data)
        references = self._parse_references(cve_data)
        description = self._parse_description(cve_data)
        published_date, last_modified_date = self._parse_dates(cve_data)
        
        if cve:
            # Update existing CVE
            cve.description = description
            cve.published_date = published_date
            cve.last_modified_date = last_modified_date
            cve.cvss_v3_score = cvss_data.get("cvss_v3_score")
            cve.cvss_v3_vector = cvss_data.get("cvss_v3_vector")
            cve.cvss_v3_severity = cvss_data.get("cvss_v3_severity")
            cve.cvss_v2_score = cvss_data.get("cvss_v2_score")
            cve.cvss_v2_vector = cvss_data.get("cvss_v2_vector")
            cve.cwe_ids = cwe_ids
            cve.references = references
            cve.nvd_last_modified = datetime.utcnow()
            cve.raw_data = cve_data
        else:
            # Create new CVE
            cve = CVE(
                cve_id=cve_id,
                description=description,
                published_date=published_date,
                last_modified_date=last_modified_date,
                cvss_v3_score=cvss_data.get("cvss_v3_score"),
                cvss_v3_vector=cvss_data.get("cvss_v3_vector"),
                cvss_v3_severity=cvss_data.get("cvss_v3_severity"),
                cvss_v2_score=cvss_data.get("cvss_v2_score"),
                cvss_v2_vector=cvss_data.get("cvss_v2_vector"),
                cwe_ids=cwe_ids,
                references=references,
                nvd_last_modified=datetime.utcnow(),
                raw_data=cve_data,
            )
            self.db.add(cve)
        
        return cve

    async def _process_cpe_applicability(self, cve: CVE, cve_data: Dict[str, Any]) -> None:
        """Process CPE applicability data for a CVE."""
        configurations = cve_data.get("configurations", [])
        
        # Delete existing applicability records
        existing_result = await self.db.execute(
            select(CVECPEApplicability).where(
                and_(
                    CVECPEApplicability.cve_id == cve.cve_id,
                    CVECPEApplicability.deleted_at.is_(None),
                )
            )
        )
        existing_records = existing_result.scalars().all()
        for record in existing_records:
            record.soft_delete()
        
        # Process new applicability data
        for config in configurations:
            nodes = config.get("nodes", [])
            for node in nodes:
                cpe_matches = node.get("cpeMatch", [])
                for match in cpe_matches:
                    cpe_applicability = CVECPEApplicability(
                        cve_id=cve.cve_id,
                        cpe_string=match.get("criteria", ""),
                        version_start_including=match.get("versionStartIncluding"),
                        version_start_excluding=match.get("versionStartExcluding"),
                        version_end_including=match.get("versionEndIncluding"),
                        version_end_excluding=match.get("versionEndExcluding"),
                        vulnerable=match.get("vulnerable", True),
                        configuration_id=config.get("id"),
                    )
                    self.db.add(cpe_applicability)

    async def ingest_cve(self, cve_data: Dict[str, Any]) -> CVE:
        """Ingest a single CVE from NVD data."""
        cve = await self._create_or_update_cve(cve_data)
        await self._process_cpe_applicability(cve, cve_data)
        
        return cve

    async def bulk_ingest_cves(self, cve_list: List[Dict[str, Any]]) -> int:
        """Bulk ingest multiple CVEs."""
        processed_count = 0
        
        for cve_data in cve_list:
            try:
                await self.ingest_cve(cve_data)
                processed_count += 1
                
                # Commit in batches
                if processed_count % settings.cve_batch_size == 0:
                    await self.db.commit()
                    logger.info("Processed CVE batch", count=processed_count)
            
            except Exception as e:
                logger.error("Failed to ingest CVE", cve_id=cve_data.get("id"), error=str(e))
                await self.db.rollback()
        
        # Final commit
        await self.db.commit()
        logger.info("Bulk CVE ingestion completed", total_processed=processed_count)
        
        return processed_count

    async def perform_bulk_import(self, years: int = 2) -> int:
        """Perform initial bulk import of CVEs."""
        logger.info("Starting bulk CVE import", years=years)
        
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=years * 365)
        
        async with NVDAPIClient() as client:
            cve_list = await client.get_cves_by_date_range(start_date, end_date)
            
            # Extract vulnerability data
            vulnerabilities = []
            for item in cve_list:
                if "cve" in item:
                    vulnerabilities.append(item["cve"])
            
            return await self.bulk_ingest_cves(vulnerabilities)

    async def perform_incremental_update(self, hours: int = 24) -> int:
        """Perform incremental update of recently modified CVEs."""
        logger.info("Starting incremental CVE update", hours=hours)
        
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(hours=hours)
        
        async with NVDAPIClient() as client:
            cve_list = await client.get_cves_by_date_range(
                start_date, end_date, use_last_modified=True
            )
            
            # Extract vulnerability data
            vulnerabilities = []
            for item in cve_list:
                if "cve" in item:
                    vulnerabilities.append(item["cve"])
            
            return await self.bulk_ingest_cves(vulnerabilities)
