"""Component service for business logic."""

from typing import List, Optional
from uuid import UUID

import structlog
from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from ..models.application import Application, Component, CPEMapping
from ..schemas.application import ComponentCreate, ComponentUpdate, CPEMappingCreate, CPEMappingUpdate
from ..utils.cpe_utils import validate_cpe_string

logger = structlog.get_logger(__name__)


class ComponentService:
    """Service for managing components and CPE mappings."""

    def __init__(self, db: AsyncSession) -> None:
        """Initialize the service."""
        self.db = db

    def _validate_cpe_string(self, cpe_string: str) -> bool:
        """Validate CPE 2.3 string format."""
        return validate_cpe_string(cpe_string)

    async def create_component(
        self, 
        application_id: UUID, 
        component_data: ComponentCreate
    ) -> Component:
        """Create a new component."""
        logger.info("Creating component", application_id=application_id, name=component_data.name)
        
        # Verify application exists
        app_result = await self.db.execute(
            select(Application).where(
                and_(
                    Application.id == application_id,
                    Application.deleted_at.is_(None),
                )
            )
        )
        if not app_result.scalar_one_or_none():
            raise ValueError(f"Application with ID {application_id} not found")
        
        # Check for existing component with same name and version
        existing = await self.db.execute(
            select(Component).where(
                and_(
                    Component.application_id == application_id,
                    Component.name == component_data.name,
                    Component.version == component_data.version,
                    Component.deleted_at.is_(None),
                )
            )
        )
        if existing.scalar_one_or_none():
            raise ValueError(
                f"Component '{component_data.name}' version '{component_data.version}' already exists"
            )
        
        # Create new component
        component = Component(
            application_id=application_id,
            **component_data.model_dump()
        )
        self.db.add(component)
        await self.db.commit()
        await self.db.refresh(component)
        
        return component

    async def get_component(self, component_id: UUID) -> Optional[Component]:
        """Get component by ID."""
        query = select(Component).where(
            and_(
                Component.id == component_id,
                Component.deleted_at.is_(None),
            )
        ).options(selectinload(Component.cpe_mappings))
        
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    async def list_components(
        self,
        application_id: Optional[UUID] = None,
        skip: int = 0,
        limit: int = 100,
        component_type: Optional[str] = None,
    ) -> List[Component]:
        """List components with optional filtering."""
        query = select(Component).where(Component.deleted_at.is_(None))
        
        if application_id:
            query = query.where(Component.application_id == application_id)
        if component_type:
            query = query.where(Component.component_type == component_type)
        
        query = (
            query.options(selectinload(Component.cpe_mappings))
            .offset(skip)
            .limit(limit)
            .order_by(Component.created_at.desc())
        )
        
        result = await self.db.execute(query)
        return list(result.scalars().all())

    async def update_component(
        self, 
        component_id: UUID, 
        component_data: ComponentUpdate
    ) -> Optional[Component]:
        """Update a component."""
        component = await self.get_component(component_id)
        if not component:
            return None
        
        # Check for name/version conflicts if updating those fields
        update_data = component_data.model_dump(exclude_unset=True)
        if "name" in update_data or "version" in update_data:
            new_name = update_data.get("name", component.name)
            new_version = update_data.get("version", component.version)
            
            existing = await self.db.execute(
                select(Component).where(
                    and_(
                        Component.application_id == component.application_id,
                        Component.name == new_name,
                        Component.version == new_version,
                        Component.id != component_id,
                        Component.deleted_at.is_(None),
                    )
                )
            )
            if existing.scalar_one_or_none():
                raise ValueError(
                    f"Component '{new_name}' version '{new_version}' already exists"
                )
        
        # Update component
        for field, value in update_data.items():
            setattr(component, field, value)
        
        await self.db.commit()
        await self.db.refresh(component)
        
        return component

    async def delete_component(self, component_id: UUID) -> bool:
        """Soft delete a component."""
        component = await self.get_component(component_id)
        if not component:
            return False
        
        # Soft delete the component and all its CPE mappings
        component.soft_delete()
        
        for cpe_mapping in component.cpe_mappings:
            if not cpe_mapping.is_deleted:
                cpe_mapping.soft_delete()
        
        await self.db.commit()
        logger.info("Component soft deleted", component_id=component_id)
        
        return True

    # CPE Mapping methods
    async def create_cpe_mapping(
        self, 
        component_id: UUID, 
        cpe_data: CPEMappingCreate
    ) -> CPEMapping:
        """Create a new CPE mapping."""
        logger.info("Creating CPE mapping", component_id=component_id, cpe_string=cpe_data.cpe_string)
        
        # Validate CPE string format
        if not self._validate_cpe_string(cpe_data.cpe_string):
            raise ValueError(f"Invalid CPE 2.3 format: {cpe_data.cpe_string}")
        
        # Verify component exists
        component = await self.get_component(component_id)
        if not component:
            raise ValueError(f"Component with ID {component_id} not found")
        
        # Check for existing CPE mapping
        existing = await self.db.execute(
            select(CPEMapping).where(
                and_(
                    CPEMapping.component_id == component_id,
                    CPEMapping.cpe_string == cpe_data.cpe_string,
                    CPEMapping.deleted_at.is_(None),
                )
            )
        )
        if existing.scalar_one_or_none():
            raise ValueError(f"CPE mapping '{cpe_data.cpe_string}' already exists for this component")
        
        # Create new CPE mapping
        cpe_mapping = CPEMapping(
            component_id=component_id,
            **cpe_data.model_dump()
        )
        self.db.add(cpe_mapping)
        await self.db.commit()
        await self.db.refresh(cpe_mapping)
        
        return cpe_mapping

    async def update_cpe_mapping(
        self, 
        cpe_mapping_id: UUID, 
        cpe_data: CPEMappingUpdate
    ) -> Optional[CPEMapping]:
        """Update a CPE mapping."""
        query = select(CPEMapping).where(
            and_(
                CPEMapping.id == cpe_mapping_id,
                CPEMapping.deleted_at.is_(None),
            )
        )
        result = await self.db.execute(query)
        cpe_mapping = result.scalar_one_or_none()
        
        if not cpe_mapping:
            return None
        
        # Validate CPE string if being updated
        update_data = cpe_data.model_dump(exclude_unset=True)
        if "cpe_string" in update_data:
            if not self._validate_cpe_string(update_data["cpe_string"]):
                raise ValueError(f"Invalid CPE 2.3 format: {update_data['cpe_string']}")
            
            # Check for conflicts
            existing = await self.db.execute(
                select(CPEMapping).where(
                    and_(
                        CPEMapping.component_id == cpe_mapping.component_id,
                        CPEMapping.cpe_string == update_data["cpe_string"],
                        CPEMapping.id != cpe_mapping_id,
                        CPEMapping.deleted_at.is_(None),
                    )
                )
            )
            if existing.scalar_one_or_none():
                raise ValueError(f"CPE mapping '{update_data['cpe_string']}' already exists for this component")
        
        # Update CPE mapping
        for field, value in update_data.items():
            setattr(cpe_mapping, field, value)
        
        await self.db.commit()
        await self.db.refresh(cpe_mapping)
        
        return cpe_mapping

    async def delete_cpe_mapping(self, cpe_mapping_id: UUID) -> bool:
        """Soft delete a CPE mapping."""
        query = select(CPEMapping).where(
            and_(
                CPEMapping.id == cpe_mapping_id,
                CPEMapping.deleted_at.is_(None),
            )
        )
        result = await self.db.execute(query)
        cpe_mapping = result.scalar_one_or_none()
        
        if not cpe_mapping:
            return False
        
        cpe_mapping.soft_delete()
        await self.db.commit()
        logger.info("CPE mapping soft deleted", cpe_mapping_id=cpe_mapping_id)
        
        return True
