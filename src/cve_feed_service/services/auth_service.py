"""Authentication service for user and API key management."""

from typing import List, Optional, Tuple
from uuid import UUID

import structlog
from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from ..core.auth import generate_api_key, get_password_hash, hash_api_key, verify_password
from ..models.user import API<PERSON>ey, User
from ..schemas.auth import APIKeyCreate, UserCreate, UserUpdate

logger = structlog.get_logger(__name__)


class AuthService:
    """Service for authentication and user management."""

    def __init__(self, db: AsyncSession) -> None:
        """Initialize the service."""
        self.db = db

    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate a user with username and password."""
        result = await self.db.execute(
            select(User).where(
                and_(
                    User.username == username,
                    User.is_active == True,
                    User.deleted_at.is_(None),
                )
            )
        )
        
        user = result.scalar_one_or_none()
        if not user or not verify_password(password, user.hashed_password):
            return None
        
        return user

    async def create_user(self, user_data: UserCreate) -> User:
        """Create a new user."""
        # Check for existing username
        existing = await self.db.execute(
            select(User).where(
                and_(
                    User.username == user_data.username,
                    User.deleted_at.is_(None),
                )
            )
        )
        if existing.scalar_one_or_none():
            raise ValueError(f"Username '{user_data.username}' already exists")
        
        # Check for existing email
        existing = await self.db.execute(
            select(User).where(
                and_(
                    User.email == user_data.email,
                    User.deleted_at.is_(None),
                )
            )
        )
        if existing.scalar_one_or_none():
            raise ValueError(f"Email '{user_data.email}' already exists")
        
        # Create user
        user = User(
            username=user_data.username,
            email=user_data.email,
            full_name=user_data.full_name,
            hashed_password=get_password_hash(user_data.password),
            role=user_data.role,
        )
        
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        
        return user

    async def get_user_by_id(self, user_id: UUID) -> Optional[User]:
        """Get user by ID."""
        result = await self.db.execute(
            select(User).where(
                and_(
                    User.id == user_id,
                    User.deleted_at.is_(None),
                )
            )
        )
        
        return result.scalar_one_or_none()

    async def get_user_with_api_keys(self, user_id: UUID) -> Optional[User]:
        """Get user with API keys."""
        result = await self.db.execute(
            select(User)
            .where(
                and_(
                    User.id == user_id,
                    User.deleted_at.is_(None),
                )
            )
            .options(selectinload(User.api_keys))
        )
        
        return result.scalar_one_or_none()

    async def list_users(self) -> List[User]:
        """List all active users."""
        result = await self.db.execute(
            select(User)
            .where(User.deleted_at.is_(None))
            .order_by(User.created_at.desc())
        )
        
        return list(result.scalars().all())

    async def update_user(self, user_id: UUID, user_data: UserUpdate) -> Optional[User]:
        """Update a user."""
        user = await self.get_user_by_id(user_id)
        if not user:
            return None
        
        update_data = user_data.model_dump(exclude_unset=True)
        
        # Check for email conflicts if updating email
        if "email" in update_data and update_data["email"] != user.email:
            existing = await self.db.execute(
                select(User).where(
                    and_(
                        User.email == update_data["email"],
                        User.id != user_id,
                        User.deleted_at.is_(None),
                    )
                )
            )
            if existing.scalar_one_or_none():
                raise ValueError(f"Email '{update_data['email']}' already exists")
        
        # Update user
        for field, value in update_data.items():
            setattr(user, field, value)
        
        await self.db.commit()
        await self.db.refresh(user)
        
        return user

    async def change_password(self, user_id: UUID, new_password: str) -> bool:
        """Change user password."""
        user = await self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.hashed_password = get_password_hash(new_password)
        await self.db.commit()
        
        return True

    # API Key management
    async def create_api_key(self, user_id: UUID, name: str) -> Tuple[APIKey, str]:
        """Create a new API key for a user."""
        # Check if user exists
        user = await self.get_user_by_id(user_id)
        if not user:
            raise ValueError(f"User with ID {user_id} not found")
        
        # Check for existing API key with same name
        existing = await self.db.execute(
            select(APIKey).where(
                and_(
                    APIKey.user_id == str(user_id),
                    APIKey.name == name,
                    APIKey.deleted_at.is_(None),
                )
            )
        )
        if existing.scalar_one_or_none():
            raise ValueError(f"API key with name '{name}' already exists")
        
        # Generate API key
        api_key_value = generate_api_key()
        key_hash = hash_api_key(api_key_value)
        
        # Create API key record
        api_key = APIKey(
            user_id=str(user_id),
            name=name,
            key_hash=key_hash,
        )
        
        self.db.add(api_key)
        await self.db.commit()
        await self.db.refresh(api_key)
        
        return api_key, api_key_value

    async def list_user_api_keys(self, user_id: UUID) -> List[APIKey]:
        """List API keys for a user."""
        result = await self.db.execute(
            select(APIKey).where(
                and_(
                    APIKey.user_id == str(user_id),
                    APIKey.deleted_at.is_(None),
                )
            ).order_by(APIKey.created_at.desc())
        )
        
        return list(result.scalars().all())

    async def delete_api_key(self, api_key_id: UUID, user_id: UUID) -> bool:
        """Delete an API key."""
        result = await self.db.execute(
            select(APIKey).where(
                and_(
                    APIKey.id == api_key_id,
                    APIKey.user_id == str(user_id),
                    APIKey.deleted_at.is_(None),
                )
            )
        )
        
        api_key = result.scalar_one_or_none()
        if not api_key:
            return False
        
        api_key.soft_delete()
        await self.db.commit()
        
        return True

    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        result = await self.db.execute(
            select(User).where(
                and_(
                    User.username == username,
                    User.deleted_at.is_(None),
                )
            )
        )
        return result.scalar_one_or_none()
