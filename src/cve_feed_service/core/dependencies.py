"""FastAPI dependencies for authentication and authorization."""

from datetime import datetime
from typing import Optional

import structlog
from fastapi import Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON>orization<PERSON>red<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APIKeyHeader
from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from ..db.database import get_db
from ..models.user import User, APIKey, UserRole
from .auth import verify_token, verify_api_key

logger = structlog.get_logger(__name__)

# Security schemes
bearer_scheme = HTTPBearer(auto_error=False)
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)


async def get_current_user_from_token(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(bearer_scheme),
    db: AsyncSession = Depends(get_db),
) -> Optional[User]:
    """Get current user from JWT token."""
    if not credentials:
        return None
    
    payload = verify_token(credentials.credentials)
    if not payload:
        return None
    
    username = payload.get("sub")
    if not username:
        return None
    
    # Get user from database
    result = await db.execute(
        select(User).where(
            and_(
                User.username == username,
                User.is_active == True,
                User.deleted_at.is_(None),
            )
        )
    )
    
    return result.scalar_one_or_none()


async def get_current_user_from_api_key(
    api_key: Optional[str] = Depends(api_key_header),
    db: AsyncSession = Depends(get_db),
) -> Optional[User]:
    """Get current user from API key."""
    if not api_key:
        return None
    
    # Find API key in database
    result = await db.execute(
        select(APIKey).where(
            and_(
                APIKey.is_active == True,
                APIKey.deleted_at.is_(None),
            )
        )
    )
    
    api_keys = result.scalars().all()
    
    # Check each API key hash
    for key_record in api_keys:
        if verify_api_key(api_key, key_record.key_hash):
            # Update last used timestamp
            key_record.last_used_at = datetime.utcnow().isoformat()
            await db.commit()
            
            # Get associated user
            user_result = await db.execute(
                select(User).where(
                    and_(
                        User.id == key_record.user_id,
                        User.is_active == True,
                        User.deleted_at.is_(None),
                    )
                )
            )
            
            return user_result.scalar_one_or_none()
    
    return None


async def get_current_user(
    token_user: Optional[User] = Depends(get_current_user_from_token),
    api_key_user: Optional[User] = Depends(get_current_user_from_api_key),
) -> Optional[User]:
    """Get current user from either JWT token or API key."""
    return token_user or api_key_user


async def require_authentication(
    current_user: Optional[User] = Depends(get_current_user),
) -> User:
    """Require user authentication."""
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return current_user


async def require_admin(
    current_user: User = Depends(require_authentication),
) -> User:
    """Require admin role."""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required",
        )
    
    return current_user


async def require_analyst_or_admin(
    current_user: User = Depends(require_authentication),
) -> User:
    """Require security analyst or admin role."""
    if not (current_user.is_analyst or current_user.is_admin):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Security analyst or admin access required",
        )
    
    return current_user


# Role-based access control helpers
def check_permission(user: User, required_role: UserRole) -> bool:
    """Check if user has required role or higher."""
    role_hierarchy = {
        UserRole.SECURITY_ANALYST: 1,
        UserRole.IT_ADMIN: 2,
    }
    
    user_level = role_hierarchy.get(user.role, 0)
    required_level = role_hierarchy.get(required_role, 0)
    
    return user_level >= required_level
