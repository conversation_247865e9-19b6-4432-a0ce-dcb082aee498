"""Application configuration settings."""

from functools import lru_cache
from typing import Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings."""

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    # Application settings
    app_name: str = "CVE Feed Service"
    app_version: str = "0.1.0"
    debug: bool = False
    environment: str = Field(default="development", description="Environment: development, staging, production, test")

    # API settings
    api_v1_prefix: str = "/api/v1"
    host: str = "0.0.0.0"
    port: int = 8000
    reload: bool = False

    # Database settings
    database_url: str = Field(
        default="sqlite+aiosqlite:///./dev_database.db",
        description="Database URL",
    )
    database_echo: bool = False

    # Security settings
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        description="Secret key for JWT tokens",
    )
    access_token_expire_minutes: int = 30
    algorithm: str = "HS256"

    # NVD API settings
    nvd_api_base_url: str = "https://services.nvd.nist.gov/rest/json"
    nvd_api_key: Optional[str] = Field(default=None, description="NVD API key for higher rate limits")
    nvd_rate_limit_per_minute: int = Field(default=10, description="NVD API rate limit per minute")
    nvd_request_timeout: int = 30

    # CVE ingestion settings
    cve_bulk_import_years: int = Field(default=2, description="Number of years to import during bulk import")
    cve_update_interval_minutes: int = Field(default=60, description="Interval for CVE updates in minutes")
    cve_batch_size: int = Field(default=100, description="Batch size for CVE processing")

    # Logging settings
    log_level: str = "INFO"
    log_format: str = "json"

    @field_validator("environment")
    @classmethod
    def validate_environment(cls, v: str) -> str:
        """Validate environment setting."""
        allowed = {"development", "staging", "production", "test"}
        if v not in allowed:
            raise ValueError(f"Environment must be one of: {allowed}")
        return v

    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """Validate log level."""
        allowed = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
        if v.upper() not in allowed:
            raise ValueError(f"Log level must be one of: {allowed}")
        return v.upper()

    @property
    def is_development(self) -> bool:
        """Check if running in development mode."""
        return self.environment == "development"

    @property
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return self.environment == "production"

    @property
    def is_testing(self) -> bool:
        """Check if running in test mode."""
        return self.environment == "test"


def get_settings() -> Settings:
    """Get settings instance."""
    return Settings()
