"""Component management endpoints."""

from typing import List
from uuid import UUID

import structlog
from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from ....db.database import get_db
from ....schemas.application import (
    ComponentCreate,
    ComponentResponse,
    ComponentUpdate,
    ComponentWithCPEResponse,
    CPEMappingCreate,
    CPEMappingResponse,
    CPEMappingUpdate,
)
from ....services.component_service import ComponentService

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.post(
    "/{application_id}/components",
    response_model=ComponentResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new component",
    description="Create a new component for an application",
)
async def create_component(
    application_id: UUID = Path(..., description="Application ID"),
    component_data: ComponentCreate = ...,
    db: AsyncSession = Depends(get_db),
) -> ComponentResponse:
    """Create a new component for an application."""
    logger.info("Creating component", application_id=application_id, name=component_data.name)
    
    service = ComponentService(db)
    try:
        component = await service.create_component(application_id, component_data)
        logger.info("Component created", component_id=component.id, name=component.name)
        return ComponentResponse.model_validate(component)
    except ValueError as e:
        logger.error("Failed to create component", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.get(
    "/{application_id}/components",
    response_model=List[ComponentWithCPEResponse],
    summary="List application components",
    description="Retrieve components for a specific application",
)
async def list_application_components(
    application_id: UUID = Path(..., description="Application ID"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    component_type: str = Query(None, description="Filter by component type"),
    db: AsyncSession = Depends(get_db),
) -> List[ComponentWithCPEResponse]:
    """List components for an application."""
    logger.info("Listing components", application_id=application_id, skip=skip, limit=limit)
    
    service = ComponentService(db)
    components = await service.list_components(
        application_id=application_id,
        skip=skip,
        limit=limit,
        component_type=component_type,
    )
    
    return [ComponentWithCPEResponse.model_validate(comp) for comp in components]


@router.get(
    "/components/{component_id}",
    response_model=ComponentWithCPEResponse,
    summary="Get component by ID",
    description="Retrieve a specific component with its CPE mappings",
)
async def get_component(
    component_id: UUID = Path(..., description="Component ID"),
    db: AsyncSession = Depends(get_db),
) -> ComponentWithCPEResponse:
    """Get component by ID."""
    logger.info("Getting component", component_id=component_id)
    
    service = ComponentService(db)
    component = await service.get_component(component_id)
    
    if not component:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Component with ID {component_id} not found",
        )
    
    return ComponentWithCPEResponse.model_validate(component)


@router.patch(
    "/components/{component_id}",
    response_model=ComponentResponse,
    summary="Update component",
    description="Update an existing component",
)
async def update_component(
    component_id: UUID = Path(..., description="Component ID"),
    component_data: ComponentUpdate = ...,
    db: AsyncSession = Depends(get_db),
) -> ComponentResponse:
    """Update a component."""
    logger.info("Updating component", component_id=component_id)
    
    service = ComponentService(db)
    try:
        component = await service.update_component(component_id, component_data)
        if not component:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Component with ID {component_id} not found",
            )
        
        logger.info("Component updated", component_id=component.id)
        return ComponentResponse.model_validate(component)
    except ValueError as e:
        logger.error("Failed to update component", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.delete(
    "/components/{component_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete component",
    description="Soft delete a component and all its CPE mappings",
)
async def delete_component(
    component_id: UUID = Path(..., description="Component ID"),
    db: AsyncSession = Depends(get_db),
) -> None:
    """Soft delete a component."""
    logger.info("Deleting component", component_id=component_id)
    
    service = ComponentService(db)
    success = await service.delete_component(component_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Component with ID {component_id} not found",
        )
    
    logger.info("Component deleted", component_id=component_id)


# CPE Mapping endpoints
@router.post(
    "/components/{component_id}/cpe-mappings",
    response_model=CPEMappingResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create CPE mapping",
    description="Create a new CPE mapping for a component",
)
async def create_cpe_mapping(
    component_id: UUID = Path(..., description="Component ID"),
    cpe_data: CPEMappingCreate = ...,
    db: AsyncSession = Depends(get_db),
) -> CPEMappingResponse:
    """Create a CPE mapping for a component."""
    logger.info("Creating CPE mapping", component_id=component_id, cpe_string=cpe_data.cpe_string)
    
    service = ComponentService(db)
    try:
        cpe_mapping = await service.create_cpe_mapping(component_id, cpe_data)
        logger.info("CPE mapping created", cpe_mapping_id=cpe_mapping.id)
        return CPEMappingResponse.model_validate(cpe_mapping)
    except ValueError as e:
        logger.error("Failed to create CPE mapping", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.patch(
    "/cpe-mappings/{cpe_mapping_id}",
    response_model=CPEMappingResponse,
    summary="Update CPE mapping",
    description="Update an existing CPE mapping",
)
async def update_cpe_mapping(
    cpe_mapping_id: UUID = Path(..., description="CPE mapping ID"),
    cpe_data: CPEMappingUpdate = ...,
    db: AsyncSession = Depends(get_db),
) -> CPEMappingResponse:
    """Update a CPE mapping."""
    logger.info("Updating CPE mapping", cpe_mapping_id=cpe_mapping_id)
    
    service = ComponentService(db)
    try:
        cpe_mapping = await service.update_cpe_mapping(cpe_mapping_id, cpe_data)
        if not cpe_mapping:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"CPE mapping with ID {cpe_mapping_id} not found",
            )
        
        logger.info("CPE mapping updated", cpe_mapping_id=cpe_mapping.id)
        return CPEMappingResponse.model_validate(cpe_mapping)
    except ValueError as e:
        logger.error("Failed to update CPE mapping", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.delete(
    "/cpe-mappings/{cpe_mapping_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete CPE mapping",
    description="Soft delete a CPE mapping",
)
async def delete_cpe_mapping(
    cpe_mapping_id: UUID = Path(..., description="CPE mapping ID"),
    db: AsyncSession = Depends(get_db),
) -> None:
    """Soft delete a CPE mapping."""
    logger.info("Deleting CPE mapping", cpe_mapping_id=cpe_mapping_id)
    
    service = ComponentService(db)
    success = await service.delete_cpe_mapping(cpe_mapping_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"CPE mapping with ID {cpe_mapping_id} not found",
        )
    
    logger.info("CPE mapping deleted", cpe_mapping_id=cpe_mapping_id)
