"""Authentication endpoints."""

from datetime import <PERSON><PERSON><PERSON>
from typing import List

import structlog
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from ....core.auth import create_access_token, verify_password
from ....core.config import get_settings
from ....core.dependencies import require_admin, require_authentication
from ....db.database import get_db
from ....models.user import User
from ....schemas.auth import (
    APIKeyCreate,
    APIKeyCreateResponse,
    APIKeyResponse,
    LoginRequest,
    PasswordChangeRequest,
    TokenResponse,
    UserCreate,
    UserResponse,
    UserUpdate,
    UserWithAPIKeysResponse,
)
from ....services.auth_service import AuthService

logger = structlog.get_logger(__name__)
router = APIRouter()
settings = get_settings()


@router.post(
    "/login",
    response_model=TokenResponse,
    summary="User login",
    description="Authenticate user and return JWT token",
)
async def login(
    login_data: LoginRequest,
    db: AsyncSession = Depends(get_db),
) -> TokenResponse:
    """Authenticate user and return JWT token."""
    logger.info("User login attempt", username=login_data.username)
    
    service = AuthService(db)
    user = await service.authenticate_user(login_data.username, login_data.password)
    
    if not user:
        logger.warning("Login failed", username=login_data.username)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create access token
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.username, "role": user.role.value},
        expires_delta=access_token_expires,
    )
    
    logger.info("User logged in successfully", user_id=user.id, username=user.username)
    
    return TokenResponse(
        access_token=access_token,
        token_type="bearer",
        expires_in=settings.access_token_expire_minutes * 60,
    )


@router.get(
    "/me",
    response_model=UserWithAPIKeysResponse,
    summary="Get current user",
    description="Get current user information",
)
async def get_current_user_info(
    current_user: User = Depends(require_authentication),
    db: AsyncSession = Depends(get_db),
) -> UserWithAPIKeysResponse:
    """Get current user information."""
    service = AuthService(db)
    user_with_keys = await service.get_user_with_api_keys(current_user.id)
    
    return UserWithAPIKeysResponse.model_validate(user_with_keys)


@router.post(
    "/change-password",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Change password",
    description="Change current user's password",
)
async def change_password(
    password_data: PasswordChangeRequest,
    current_user: User = Depends(require_authentication),
    db: AsyncSession = Depends(get_db),
) -> None:
    """Change current user's password."""
    logger.info("Password change request", user_id=current_user.id)
    
    # Verify current password
    if not verify_password(password_data.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect",
        )
    
    service = AuthService(db)
    await service.change_password(current_user.id, password_data.new_password)
    
    logger.info("Password changed successfully", user_id=current_user.id)


# Admin-only endpoints
@router.post(
    "/users",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create user",
    description="Create a new user (admin only)",
)
async def create_user(
    user_data: UserCreate,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db),
) -> UserResponse:
    """Create a new user."""
    logger.info("Creating user", username=user_data.username, created_by=current_user.id)
    
    service = AuthService(db)
    try:
        user = await service.create_user(user_data)
        logger.info("User created", user_id=user.id, username=user.username)
        return UserResponse.model_validate(user)
    except ValueError as e:
        logger.error("Failed to create user", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.get(
    "/users",
    response_model=List[UserResponse],
    summary="List users",
    description="List all users (admin only)",
)
async def list_users(
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db),
) -> List[UserResponse]:
    """List all users."""
    service = AuthService(db)
    users = await service.list_users()
    
    return [UserResponse.model_validate(user) for user in users]


@router.patch(
    "/users/{user_id}",
    response_model=UserResponse,
    summary="Update user",
    description="Update a user (admin only)",
)
async def update_user(
    user_id: str,
    user_data: UserUpdate,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db),
) -> UserResponse:
    """Update a user."""
    logger.info("Updating user", user_id=user_id, updated_by=current_user.id)
    
    service = AuthService(db)
    try:
        user = await service.update_user(user_id, user_data)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"User with ID {user_id} not found",
            )
        
        logger.info("User updated", user_id=user.id)
        return UserResponse.model_validate(user)
    except ValueError as e:
        logger.error("Failed to update user", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


# API Key management
@router.post(
    "/api-keys",
    response_model=APIKeyCreateResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create API key",
    description="Create a new API key for the current user",
)
async def create_api_key(
    api_key_data: APIKeyCreate,
    current_user: User = Depends(require_authentication),
    db: AsyncSession = Depends(get_db),
) -> APIKeyCreateResponse:
    """Create a new API key."""
    logger.info("Creating API key", user_id=current_user.id, name=api_key_data.name)
    
    service = AuthService(db)
    try:
        api_key, key_value = await service.create_api_key(current_user.id, api_key_data.name)
        logger.info("API key created", api_key_id=api_key.id, user_id=current_user.id)
        
        response = APIKeyCreateResponse.model_validate(api_key)
        response.api_key = key_value
        return response
    except ValueError as e:
        logger.error("Failed to create API key", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.get(
    "/api-keys",
    response_model=List[APIKeyResponse],
    summary="List API keys",
    description="List current user's API keys",
)
async def list_api_keys(
    current_user: User = Depends(require_authentication),
    db: AsyncSession = Depends(get_db),
) -> List[APIKeyResponse]:
    """List current user's API keys."""
    service = AuthService(db)
    api_keys = await service.list_user_api_keys(current_user.id)
    
    return [APIKeyResponse.model_validate(key) for key in api_keys]


@router.delete(
    "/api-keys/{api_key_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete API key",
    description="Delete an API key",
)
async def delete_api_key(
    api_key_id: str,
    current_user: User = Depends(require_authentication),
    db: AsyncSession = Depends(get_db),
) -> None:
    """Delete an API key."""
    logger.info("Deleting API key", api_key_id=api_key_id, user_id=current_user.id)
    
    service = AuthService(db)
    success = await service.delete_api_key(api_key_id, current_user.id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"API key with ID {api_key_id} not found",
        )
    
    logger.info("API key deleted", api_key_id=api_key_id)
