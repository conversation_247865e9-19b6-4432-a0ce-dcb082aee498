"""CVE and vulnerability feed endpoints."""

from typing import Optional
from uuid import UUID

import structlog
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from ....db.database import get_db
from ....schemas.cve import CVEFeedResponse, CVEResponse, CVEWithApplicabilityResponse
from ....services.cve_service import CVEService

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.get(
    "/feed",
    response_model=CVEFeedResponse,
    summary="Get tailored CVE feed",
    description="Retrieve a tailored CVE feed based on application components and filters",
)
async def get_cve_feed(
    application_id: Optional[UUID] = Query(None, description="Filter by application ID"),
    severity: Optional[str] = Query(None, description="Filter by CVSS severity (LOW, MEDIUM, HIGH, CRITICAL)"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Pagination offset"),
    db: AsyncSession = Depends(get_db),
) -> CVEFeedResponse:
    """Get tailored CVE feed."""
    logger.info(
        "Getting CVE feed",
        application_id=application_id,
        severity=severity,
        limit=limit,
        offset=offset,
    )
    
    service = CVEService(db)
    
    # Validate severity parameter
    if severity and severity.upper() not in ["LOW", "MEDIUM", "HIGH", "CRITICAL"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Severity must be one of: LOW, MEDIUM, HIGH, CRITICAL",
        )
    
    try:
        cves, total = await service.get_tailored_feed(
            application_id=application_id,
            severity=severity.upper() if severity else None,
            limit=limit,
            offset=offset,
        )
        
        return CVEFeedResponse(
            cves=[CVEResponse.model_validate(cve) for cve in cves],
            total=total,
            limit=limit,
            offset=offset,
            has_more=offset + len(cves) < total,
        )
    except ValueError as e:
        logger.error("Failed to get CVE feed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.get(
    "/{cve_id}",
    response_model=CVEWithApplicabilityResponse,
    summary="Get CVE by ID",
    description="Retrieve a specific CVE with its CPE applicability information",
)
async def get_cve(
    cve_id: str,
    db: AsyncSession = Depends(get_db),
) -> CVEWithApplicabilityResponse:
    """Get CVE by ID."""
    logger.info("Getting CVE", cve_id=cve_id)
    
    service = CVEService(db)
    cve = await service.get_cve_by_id(cve_id)
    
    if not cve:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"CVE {cve_id} not found",
        )
    
    return CVEWithApplicabilityResponse.model_validate(cve)


@router.get(
    "/",
    response_model=CVEFeedResponse,
    summary="List CVEs",
    description="List all CVEs with optional filtering",
)
async def list_cves(
    severity: Optional[str] = Query(None, description="Filter by CVSS severity"),
    published_after: Optional[str] = Query(None, description="Filter CVEs published after date (ISO format)"),
    published_before: Optional[str] = Query(None, description="Filter CVEs published before date (ISO format)"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Pagination offset"),
    db: AsyncSession = Depends(get_db),
) -> CVEFeedResponse:
    """List CVEs with filtering."""
    logger.info(
        "Listing CVEs",
        severity=severity,
        published_after=published_after,
        published_before=published_before,
        limit=limit,
        offset=offset,
    )
    
    service = CVEService(db)
    
    # Validate severity parameter
    if severity and severity.upper() not in ["LOW", "MEDIUM", "HIGH", "CRITICAL"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Severity must be one of: LOW, MEDIUM, HIGH, CRITICAL",
        )
    
    try:
        cves, total = await service.list_cves(
            severity=severity.upper() if severity else None,
            published_after=published_after,
            published_before=published_before,
            limit=limit,
            offset=offset,
        )
        
        return CVEFeedResponse(
            cves=[CVEResponse.model_validate(cve) for cve in cves],
            total=total,
            limit=limit,
            offset=offset,
            has_more=offset + len(cves) < total,
        )
    except ValueError as e:
        logger.error("Failed to list CVEs", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
