"""Application management endpoints."""

from typing import List
from uuid import UUID

import structlog
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from ....db.database import get_db
from ....schemas.application import (
    ApplicationCreate,
    ApplicationResponse,
    ApplicationUpdate,
    ApplicationWithComponentsResponse,
)
from ....services.application_service import ApplicationService

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.post(
    "/",
    response_model=ApplicationResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new application",
    description="Create a new application in the inventory",
)
async def create_application(
    application_data: ApplicationCreate,
    db: AsyncSession = Depends(get_db),
) -> ApplicationResponse:
    """Create a new application."""
    logger.info("Creating application", name=application_data.name)
    
    service = ApplicationService(db)
    try:
        application = await service.create_application(application_data)
        logger.info("Application created", application_id=application.id, name=application.name)
        return ApplicationResponse.model_validate(application)
    except ValueError as e:
        logger.error("Failed to create application", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.get(
    "/",
    response_model=List[ApplicationResponse],
    summary="List applications",
    description="Retrieve a list of applications with optional filtering",
)
async def list_applications(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    environment: str = Query(None, description="Filter by environment"),
    criticality: str = Query(None, description="Filter by criticality"),
    db: AsyncSession = Depends(get_db),
) -> List[ApplicationResponse]:
    """List applications with optional filtering."""
    logger.info("Listing applications", skip=skip, limit=limit, environment=environment, criticality=criticality)
    
    service = ApplicationService(db)
    applications = await service.list_applications(
        skip=skip,
        limit=limit,
        environment=environment,
        criticality=criticality,
    )
    
    return [ApplicationResponse.model_validate(app) for app in applications]


@router.get(
    "/{application_id}",
    response_model=ApplicationWithComponentsResponse,
    summary="Get application by ID",
    description="Retrieve a specific application with its components",
)
async def get_application(
    application_id: UUID,
    include_components: bool = Query(True, description="Include application components"),
    db: AsyncSession = Depends(get_db),
) -> ApplicationWithComponentsResponse:
    """Get application by ID."""
    logger.info("Getting application", application_id=application_id)
    
    service = ApplicationService(db)
    application = await service.get_application(application_id, include_components=include_components)
    
    if not application:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Application with ID {application_id} not found",
        )
    
    return ApplicationWithComponentsResponse.model_validate(application)


@router.patch(
    "/{application_id}",
    response_model=ApplicationResponse,
    summary="Update application",
    description="Update an existing application",
)
async def update_application(
    application_id: UUID,
    application_data: ApplicationUpdate,
    db: AsyncSession = Depends(get_db),
) -> ApplicationResponse:
    """Update an application."""
    logger.info("Updating application", application_id=application_id)
    
    service = ApplicationService(db)
    try:
        application = await service.update_application(application_id, application_data)
        if not application:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Application with ID {application_id} not found",
            )
        
        logger.info("Application updated", application_id=application.id)
        return ApplicationResponse.model_validate(application)
    except ValueError as e:
        logger.error("Failed to update application", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.delete(
    "/{application_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete application",
    description="Soft delete an application and all its components",
)
async def delete_application(
    application_id: UUID,
    db: AsyncSession = Depends(get_db),
) -> None:
    """Soft delete an application."""
    logger.info("Deleting application", application_id=application_id)
    
    service = ApplicationService(db)
    success = await service.delete_application(application_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Application with ID {application_id} not found",
        )
    
    logger.info("Application deleted", application_id=application_id)
