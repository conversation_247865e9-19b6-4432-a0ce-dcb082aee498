"""CPE (Common Platform Enumeration) utilities."""

import re
from typing import List, Optional, Tuple


class CPEValidator:
    """Validator for CPE 2.3 strings."""
    
    # CPE 2.3 format: cpe:2.3:part:vendor:product:version:update:edition:language:sw_edition:target_sw:target_hw:other
    CPE_23_PATTERN = re.compile(
        r"^cpe:2\.3:[aho\*]:[^:]*:[^:]*:[^:]*:[^:]*:[^:]*:[^:]*:[^:]*:[^:]*:[^:]*:[^:]*$"
    )
    
    @classmethod
    def validate_cpe_string(cls, cpe_string: str) -> bool:
        """Validate CPE 2.3 string format.
        
        Args:
            cpe_string: CPE string to validate
            
        Returns:
            True if valid CPE 2.3 format, False otherwise
        """
        if not cpe_string or not isinstance(cpe_string, str):
            return False
        
        return bool(cls.CPE_23_PATTERN.match(cpe_string))
    
    @classmethod
    def parse_cpe_string(cls, cpe_string: str) -> Optional[dict]:
        """Parse CPE 2.3 string into components.
        
        Args:
            cpe_string: CPE string to parse
            
        Returns:
            Dictionary with CPE components or None if invalid
        """
        if not cls.validate_cpe_string(cpe_string):
            return None
        
        parts = cpe_string.split(":")
        if len(parts) != 13:
            return None
        
        return {
            "cpe_version": parts[1],  # Should be "2.3"
            "part": parts[2],         # a=application, o=operating system, h=hardware
            "vendor": parts[3],
            "product": parts[4],
            "version": parts[5],
            "update": parts[6],
            "edition": parts[7],
            "language": parts[8],
            "sw_edition": parts[9],
            "target_sw": parts[10],
            "target_hw": parts[11],
            "other": parts[12],
        }
    
    @classmethod
    def normalize_cpe_component(cls, component: str) -> str:
        """Normalize a CPE component by handling special characters.
        
        Args:
            component: CPE component to normalize
            
        Returns:
            Normalized component
        """
        if not component or component == "*":
            return "*"
        
        # Handle URL encoding and special characters
        # This is a simplified normalization - full CPE spec has more rules
        return component.lower().replace("_", "").replace("-", "")


class CPEMatcher:
    """Matcher for CPE strings against CVE applicability."""
    
    @classmethod
    def cpe_matches(cls, component_cpe: str, cve_cpe: str) -> bool:
        """Check if a component CPE matches a CVE CPE pattern.
        
        This implements basic CPE matching logic. In a full implementation,
        this would need to handle version ranges, wildcards, and other
        CPE matching rules according to the CPE specification.
        
        Args:
            component_cpe: CPE string from component
            cve_cpe: CPE string from CVE applicability
            
        Returns:
            True if CPEs match, False otherwise
        """
        if not CPEValidator.validate_cpe_string(component_cpe) or \
           not CPEValidator.validate_cpe_string(cve_cpe):
            return False
        
        comp_parts = component_cpe.split(":")
        cve_parts = cve_cpe.split(":")
        
        if len(comp_parts) != 13 or len(cve_parts) != 13:
            return False
        
        # Compare each component (simplified matching)
        for i in range(2, 13):  # Skip cpe:2.3 prefix
            if not cls._component_matches(comp_parts[i], cve_parts[i]):
                return False
        
        return True
    
    @classmethod
    def _component_matches(cls, component: str, pattern: str) -> bool:
        """Check if a CPE component matches a pattern.
        
        Args:
            component: Component value
            pattern: Pattern to match against (may contain wildcards)
            
        Returns:
            True if component matches pattern
        """
        # Handle wildcards
        if pattern == "*" or component == "*":
            return True
        
        # Handle empty/unspecified values
        if pattern == "-" or component == "-":
            return pattern == component
        
        # Exact match (case-insensitive)
        return component.lower() == pattern.lower()
    
    @classmethod
    def extract_cpe_base(cls, cpe_string: str) -> Optional[str]:
        """Extract the base CPE (vendor:product) for matching.
        
        Args:
            cpe_string: Full CPE string
            
        Returns:
            Base CPE string (vendor:product) or None if invalid
        """
        parsed = CPEValidator.parse_cpe_string(cpe_string)
        if not parsed:
            return None
        
        return f"{parsed['vendor']}:{parsed['product']}"
    
    @classmethod
    def generate_cpe_patterns(cls, cpe_string: str) -> List[str]:
        """Generate CPE patterns for database matching.
        
        This creates patterns that can be used in SQL LIKE queries
        to find matching CVEs.
        
        Args:
            cpe_string: Base CPE string
            
        Returns:
            List of patterns for database matching
        """
        parsed = CPEValidator.parse_cpe_string(cpe_string)
        if not parsed:
            return []
        
        patterns = []
        
        # Exact match pattern
        patterns.append(cpe_string)
        
        # Base pattern (vendor:product with wildcards for version)
        base_pattern = f"cpe:2.3:{parsed['part']}:{parsed['vendor']}:{parsed['product']}:*"
        patterns.append(base_pattern)
        
        # Version-specific patterns if version is specified
        if parsed['version'] and parsed['version'] != "*":
            version_pattern = f"cpe:2.3:{parsed['part']}:{parsed['vendor']}:{parsed['product']}:{parsed['version']}:*"
            patterns.append(version_pattern)
        
        return patterns


def validate_cpe_string(cpe_string: str) -> bool:
    """Convenience function to validate CPE string.
    
    Args:
        cpe_string: CPE string to validate
        
    Returns:
        True if valid CPE 2.3 format
    """
    return CPEValidator.validate_cpe_string(cpe_string)


def parse_cpe_string(cpe_string: str) -> Optional[dict]:
    """Convenience function to parse CPE string.
    
    Args:
        cpe_string: CPE string to parse
        
    Returns:
        Dictionary with CPE components or None if invalid
    """
    return CPEValidator.parse_cpe_string(cpe_string)
