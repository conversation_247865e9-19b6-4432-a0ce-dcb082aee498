#!/usr/bin/env python3
"""Initialize database with test data."""

import asyncio
import os
import uuid
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

# Set environment variables
os.environ["DATABASE_URL"] = "sqlite+aiosqlite:///./dev_database.db"
os.environ["ENVIRONMENT"] = "development"

from src.cve_feed_service.db.base import Base
from src.cve_feed_service.models.user import User, UserRole
from src.cve_feed_service.core.auth import get_password_hash


async def init_database():
    """Initialize database with tables and test user."""
    # Create engine
    engine = create_async_engine("sqlite+aiosqlite:///./dev_database.db", echo=True)
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
        await conn.run_sync(Base.metadata.create_all)
    
    # Create session
    async_session = async_sessionmaker(engine, class_=AsyncSession)
    
    # Create test user
    async with async_session() as session:
        # Create a test user with proper UUID
        test_user_id = uuid.uuid4()
        test_user = User(
            id=test_user_id,
            username="<EMAIL>",
            email="<EMAIL>",
            full_name="Security Analyst",
            hashed_password=get_password_hash("password123"),
            role=UserRole.SECURITY_ANALYST,
            is_active=True,
        )
        session.add(test_user)
        await session.commit()
        print(f"Created test user: {test_user.username} with ID: {test_user_id}")
    
    await engine.dispose()
    print("Database initialized successfully!")


if __name__ == "__main__":
    asyncio.run(init_database())
