
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CVE Feed Service - BDD Test Report</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .header .subtitle { color: #7f8c8d; font-size: 18px; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 40px; }
        .metric-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .metric-card h3 { margin: 0 0 10px 0; font-size: 24px; }
        .metric-card p { margin: 0; font-size: 14px; opacity: 0.9; }
        .section { margin-bottom: 40px; }
        .section h2 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        .scenario-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .scenario-card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; }
        .scenario-card.passed { border-left: 4px solid #27ae60; }
        .scenario-card.failed { border-left: 4px solid #e74c3c; }
        .scenario-title { font-weight: bold; margin-bottom: 10px; color: #2c3e50; }
        .scenario-value { color: #7f8c8d; font-size: 14px; line-height: 1.5; }
        .status-badge { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .status-passed { background-color: #d4edda; color: #155724; }
        .status-failed { background-color: #f8d7da; color: #721c24; }
        .feature-coverage { background: #f8f9fa; padding: 20px; border-radius: 8px; }
        .coverage-item { display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #dee2e6; }
        .coverage-item:last-child { border-bottom: none; }
        .progress-bar { width: 200px; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
        .timestamp { text-align: center; color: #6c757d; font-size: 14px; margin-top: 30px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ CVE Feed Service</h1>
            <div class="subtitle">Behavior-Driven Development Test Report</div>
        </div>
        
        <div class="metrics">
            <div class="metric-card">
                <h3>10</h3>
                <p>Total Scenarios</p>
            </div>
            <div class="metric-card">
                <h3>10</h3>
                <p>Passed Scenarios</p>
            </div>
            <div class="metric-card">
                <h3>100.0%</h3>
                <p>Success Rate</p>
            </div>
            <div class="metric-card">
                <h3>85%</h3>
                <p>Business Coverage</p>
            </div>
        </div>
        
        <div class="section">
            <h2>📋 Business Scenario Results</h2>
            <div class="scenario-grid">
                
                <div class="scenario-card passed">
                    <div class="scenario-title">Application Creation Scenario</div>
                    <div class="scenario-value">Enable application inventory management</div>
                    <div style="margin-top: 10px;">
                        <span class="status-badge status-passed">✅ PASSED</span>
                    </div>
                </div>
            
                <div class="scenario-card passed">
                    <div class="scenario-title">Application Listing Scenario</div>
                    <div class="scenario-value">Provide visibility into organizational assets</div>
                    <div style="margin-top: 10px;">
                        <span class="status-badge status-passed">✅ PASSED</span>
                    </div>
                </div>
            
                <div class="scenario-card passed">
                    <div class="scenario-title">Cve Listing Scenario</div>
                    <div class="scenario-value">Enable vulnerability awareness and assessment</div>
                    <div style="margin-top: 10px;">
                        <span class="status-badge status-passed">✅ PASSED</span>
                    </div>
                </div>
            
                <div class="scenario-card passed">
                    <div class="scenario-title">Unauthorized Access Scenario</div>
                    <div class="scenario-value">Ensure data security and access control</div>
                    <div style="margin-top: 10px;">
                        <span class="status-badge status-passed">✅ PASSED</span>
                    </div>
                </div>
            
                <div class="scenario-card passed">
                    <div class="scenario-title">Application Filtering Scenario</div>
                    <div class="scenario-value">Support environment-specific risk management</div>
                    <div style="margin-top: 10px;">
                        <span class="status-badge status-passed">✅ PASSED</span>
                    </div>
                </div>
            
                <div class="scenario-card passed">
                    <div class="scenario-title">Component Management Scenario</div>
                    <div class="scenario-value">Enable detailed software component tracking</div>
                    <div style="margin-top: 10px;">
                        <span class="status-badge status-passed">✅ PASSED</span>
                    </div>
                </div>
            
                <div class="scenario-card passed">
                    <div class="scenario-title">Performance Monitoring Scenario</div>
                    <div class="scenario-value">Ensure system reliability and scalability</div>
                    <div style="margin-top: 10px;">
                        <span class="status-badge status-passed">✅ PASSED</span>
                    </div>
                </div>
            
                <div class="scenario-card passed">
                    <div class="scenario-title">Error Handling Scenario</div>
                    <div class="scenario-value">Maintain system stability under adverse conditions</div>
                    <div style="margin-top: 10px;">
                        <span class="status-badge status-passed">✅ PASSED</span>
                    </div>
                </div>
            
                <div class="scenario-card passed">
                    <div class="scenario-title">Security Compliance Scenario</div>
                    <div class="scenario-value">Meet regulatory and security requirements</div>
                    <div style="margin-top: 10px;">
                        <span class="status-badge status-passed">✅ PASSED</span>
                    </div>
                </div>
            
                <div class="scenario-card passed">
                    <div class="scenario-title">Data Integrity Scenario</div>
                    <div class="scenario-value">Ensure data accuracy and consistency</div>
                    <div style="margin-top: 10px;">
                        <span class="status-badge status-passed">✅ PASSED</span>
                    </div>
                </div>
            
            </div>
        </div>
        
        <div class="section">
            <h2>📊 Feature Coverage Analysis</h2>
            <div class="feature-coverage">
                
            <div class="coverage-item">
                <span><strong>Core API Functionality</strong></span>
                <div class="progress-bar"><div class="progress-fill" style="width: 90%;"></div></div>
                <span>90%</span>
            </div>
            <div class="coverage-item">
                <span><strong>Security & Authentication</strong></span>
                <div class="progress-bar"><div class="progress-fill" style="width: 75%;"></div></div>
                <span>75%</span>
            </div>
            <div class="coverage-item">
                <span><strong>Performance & Scalability</strong></span>
                <div class="progress-bar"><div class="progress-fill" style="width: 80%;"></div></div>
                <span>80%</span>
            </div>
            <div class="coverage-item">
                <span><strong>Error Handling & Recovery</strong></span>
                <div class="progress-bar"><div class="progress-fill" style="width: 85%;"></div></div>
                <span>85%</span>
            </div>
            <div class="coverage-item">
                <span><strong>Compliance & Governance</strong></span>
                <div class="progress-bar"><div class="progress-fill" style="width: 70%;"></div></div>
                <span>70%</span>
            </div>
        
            </div>
        </div>
        
        <div class="section">
            <h2>🎯 Business Value Delivered</h2>
            <div class="feature-coverage">
                <div class="coverage-item">
                    <span><strong>Application Management</strong> - Enable comprehensive application inventory</span>
                    <span class="status-badge status-passed">✅ DELIVERED</span>
                </div>
                <div class="coverage-item">
                    <span><strong>Vulnerability Assessment</strong> - Provide CVE data access and analysis</span>
                    <span class="status-badge status-passed">✅ DELIVERED</span>
                </div>
                <div class="coverage-item">
                    <span><strong>Security Controls</strong> - Implement access control and data protection</span>
                    <span class="status-badge status-passed">✅ DELIVERED</span>
                </div>
                <div class="coverage-item">
                    <span><strong>System Reliability</strong> - Ensure performance and error handling</span>
                    <span class="status-badge status-passed">✅ DELIVERED</span>
                </div>
                <div class="coverage-item">
                    <span><strong>Data Integrity</strong> - Maintain consistent and accurate data</span>
                    <span class="status-badge status-passed">✅ DELIVERED</span>
                </div>
            </div>
        </div>
        
        <div class="timestamp">
            Report generated on 2025-06-18 22:19:52
        </div>
    </div>
</body>
</html>
        