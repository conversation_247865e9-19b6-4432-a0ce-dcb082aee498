[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "cve-feed-service"
version = "0.1.0"
description = "CVE Feed Service MVP - Tailored vulnerability feeds for applications"
authors = [
    {name = "CVE Feed Team", email = "<EMAIL>"},
]
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "asyncpg>=0.29.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    "httpx>=0.25.0",
    "tenacity>=8.2.0",
    "structlog>=23.2.0",
    "rich>=13.7.0",
    "typer>=0.9.0",
]

[project.optional-dependencies]
docs = [
    "sphinx>=7.1.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
    "sphinx-autobuild>=2021.3.14",
]
dev = [
    "ruff>=0.1.6",
    "mypy>=1.7.0",
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pre-commit>=3.5.0",
    "types-passlib>=1.7.7",
    "types-python-jose>=3.3.4",
    "aiosqlite>=0.19.0",
    "httpx>=0.25.0",
    "behave>=1.2.6",
]

[project.scripts]
cve-feed = "cve_feed_service.cli.main:app"

[project.urls]
Homepage = "https://github.com/forkrul/day3-cve-feed"
Repository = "https://github.com/forkrul/day3-cve-feed.git"
Issues = "https://github.com/forkrul/day3-cve-feed/issues"

[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
    "ARG001", # unused-function-args
    "SIM", # flake8-simplify
    "TCH", # flake8-type-checking
    "TID", # flake8-tidy-imports
    "Q", # flake8-quotes
    "PL", # pylint
    "PT", # flake8-pytest-style
    "RUF", # ruff-specific rules
]
ignore = [
    "E501",  # line too long, handled by black
    "PLR0913", # too many arguments
    "PLR0915", # too many statements
    "PLR2004", # magic value used in comparison
]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true
extra_checks = true

[[tool.mypy.overrides]]
module = [
    "alembic.*",
    "sqlalchemy.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
filterwarnings = [
    "ignore:'crypt' is deprecated and slated for removal in Python 3.13:DeprecationWarning:passlib.*",
]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
