# 🐳 CVE Feed Service Docker Setup

Complete Docker infrastructure for the CVE Feed Service with Traefik integration and *.feedme.localhost domain management.

## 🏗️ **Architecture Overview**

```mermaid
graph TB
    subgraph "Traefik (Host)"
        T[Traefik Router]
    end
    
    subgraph "CVE Feed Services"
        API[API Service<br/>api.feedme.localhost]
        FE[Frontend<br/>app.feedme.localhost]
        AD[Admin<br/>admin.feedme.localhost]
        DOC[Docs<br/>docs.feedme.localhost]
        
        subgraph "Data Layer"
            PG[(PostgreSQL)]
            RD[(Redis)]
        end
        
        subgraph "Background"
            WK[Worker Service]
        end
        
        subgraph "Monitoring"
            PR[Prometheus<br/>metrics.feedme.localhost]
            GR[Grafana<br/>dashboard.feedme.localhost]
        end
    end
    
    T --> API
    T --> FE
    T --> AD
    T --> DOC
    T --> PR
    T --> GR
    
    API --> PG
    API --> RD
    WK --> PG
    WK --> RD
    PR --> API
    GR --> PR
```

## 🚀 **Quick Start**

### **Prerequisites**
- Docker and Docker Compose installed
- Traefik running on the host system
- Domain `*.feedme.localhost` configured to point to your Docker host

### **1. Setup Environment**
```bash
# Clone the repository
git clone https://github.com/forkrul/day3-cve-feed.git
cd day3-cve-feed

# Setup development environment
make setup-dev

# Edit environment variables
nano .env
```

### **2. Start Services**
```bash
# Build and start all services
make up-build

# Or start individual services
make api          # API only
make frontend     # Frontend only
make monitoring   # Monitoring only
```

### **3. Access Services**
- **API**: https://api.feedme.localhost
- **Frontend**: https://app.feedme.localhost
- **Admin**: https://admin.feedme.localhost
- **Documentation**: https://docs.feedme.localhost
- **Metrics**: https://metrics.feedme.localhost
- **Dashboard**: https://dashboard.feedme.localhost

**Development Tools** (development mode only):
- **PgAdmin**: https://pgadmin.feedme.localhost
- **Redis Commander**: https://redis.feedme.localhost

## 📋 **Services Overview**

### **Core Services**

| Service | Domain | Port | Description |
|---------|--------|------|-------------|
| **API** | api.feedme.localhost | 8000 | FastAPI backend service |
| **Frontend** | app.feedme.localhost | 3000 | Next.js React application |
| **Admin** | admin.feedme.localhost | 3000 | React admin dashboard |
| **Docs** | docs.feedme.localhost | 80 | Sphinx documentation |
| **Worker** | - | - | Background task processor |

### **Data Services**

| Service | Internal Port | Description |
|---------|---------------|-------------|
| **PostgreSQL** | 5432 | Primary database |
| **Redis** | 6379 | Cache and session store |

### **Monitoring Services**

| Service | Domain | Port | Description |
|---------|--------|------|-------------|
| **Prometheus** | metrics.feedme.localhost | 9090 | Metrics collection |
| **Grafana** | dashboard.feedme.localhost | 3000 | Monitoring dashboard |

## 🔧 **Configuration**

### **Environment Variables**

Key environment variables in `.env`:

```bash
# Database
DATABASE_URL=postgresql+asyncpg://cve_user:cve_password_secure_2024@postgres:5432/cve_feed_db
REDIS_URL=redis://:redis_password_secure_2024@redis:6379/0

# Security
SECRET_KEY=your-super-secret-key-change-in-production-2024
NVD_API_KEY=your-nvd-api-key-here

# Domains
API_DOMAIN=api.feedme.localhost
APP_DOMAIN=app.feedme.localhost
ADMIN_DOMAIN=admin.feedme.localhost
```

### **Traefik Labels**

Each service includes Traefik labels for automatic routing:

```yaml
labels:
  - "traefik.enable=true"
  - "traefik.http.routers.cve-api.rule=Host(`api.feedme.localhost`)"
  - "traefik.http.routers.cve-api.entrypoints=web"
  - "traefik.http.services.cve-api.loadbalancer.server.port=8000"
```

## 🛠️ **Development Workflow**

### **Available Make Commands**

```bash
# Environment Setup
make setup-dev          # Setup development environment
make setup-prod         # Setup production environment

# Service Management
make up                 # Start all services
make down               # Stop all services
make restart            # Restart all services
make build              # Build all images
make build-no-cache     # Build without cache

# Individual Services
make api                # Start API service only
make frontend           # Start frontend only
make monitoring         # Start monitoring only

# Logs and Debugging
make logs               # Show all logs
make logs-api           # Show API logs only
make status             # Show service status
make health             # Check service health

# Database Operations
make db-shell           # Connect to database
make db-migrate         # Run migrations
make db-reset           # Reset database (WARNING!)

# Testing
make test               # Run all tests
make test-api           # Run API tests only
make test-coverage      # Run with coverage

# Maintenance
make backup             # Backup database
make clean              # Clean Docker resources
make update             # Update images
```

### **Development Mode Features**

When running in development mode (`docker-compose.override.yml`):

- **Hot Reload**: Code changes automatically reload services
- **Debug Mode**: Enhanced logging and debugging
- **Development Tools**: PgAdmin and Redis Commander included
- **Exposed Ports**: Database and Redis accessible from host
- **Volume Mounts**: Source code mounted for live editing

## 🔒 **Security Features**

### **Container Security**
- Non-root users in all containers
- Minimal base images (Alpine Linux)
- Security headers in Nginx configurations
- Health checks for all services

### **Network Security**
- Internal Docker network isolation
- Traefik handles SSL termination
- CORS configuration for API
- Basic auth for admin interfaces

### **Data Security**
- Encrypted database connections
- Redis password protection
- Secret management via environment variables
- Regular security updates

## 📊 **Monitoring & Observability**

### **Metrics Collection**
- **Prometheus**: Collects metrics from all services
- **Custom Metrics**: CVE statistics, application performance
- **System Metrics**: Container resource usage
- **Business Metrics**: User activity, security events

### **Dashboards**
- **Grafana**: Pre-configured dashboards
- **Real-time Monitoring**: Live service status
- **Alerting**: Configurable alerts for critical events
- **Performance Tracking**: Response times, error rates

### **Health Checks**
All services include comprehensive health checks:
- Database connectivity
- Redis availability
- API endpoint responsiveness
- Frontend application status

## 🚀 **Production Deployment**

### **Production Setup**
```bash
# Setup production environment
make setup-prod

# Edit production configuration
nano .env.prod

# Start production services
make prod-up
```

### **Production Considerations**
- Change all default passwords
- Configure proper SSL certificates
- Set up external monitoring
- Configure backup strategies
- Implement log aggregation
- Set resource limits

### **Scaling**
Services can be scaled using Docker Compose:
```bash
docker-compose up -d --scale api=3 --scale worker=2
```

## 🔄 **Backup & Recovery**

### **Database Backup**
```bash
# Create backup
make backup

# Restore from backup
make restore BACKUP_FILE=backups/backup_20240120_143000.sql
```

### **Volume Management**
```bash
# List volumes
docker volume ls

# Backup volumes
docker run --rm -v cve-feed-service_postgres_data:/data -v $(pwd)/backups:/backup alpine tar czf /backup/postgres_data.tar.gz -C /data .
```

## 🐛 **Troubleshooting**

### **Common Issues**

**Services not accessible:**
```bash
# Check Traefik configuration
docker logs traefik

# Verify service status
make status

# Check service logs
make logs-api
```

**Database connection issues:**
```bash
# Check database status
make logs-db

# Connect to database
make db-shell

# Reset database
make db-reset
```

**Performance issues:**
```bash
# Check resource usage
docker stats

# View detailed logs
make logs

# Check health status
make health
```

### **Debug Mode**
Enable debug mode by setting in `.env`:
```bash
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG
```

## 📚 **Additional Resources**

- [API Documentation](https://docs.feedme.localhost/api/)
- [Frontend Documentation](https://docs.feedme.localhost/frontend/)
- [Deployment Guide](https://docs.feedme.localhost/deployment/)
- [Monitoring Guide](https://docs.feedme.localhost/monitoring/)

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Make changes and test locally
4. Submit a pull request

## 📄 **License**

This project is licensed under the MIT License - see the LICENSE file for details.
