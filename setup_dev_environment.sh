#!/bin/bash

# CVE Feed Service - Development Environment Setup Script
# This script sets up the complete development environment

set -e  # Exit on any error

echo "🚀 Setting up CVE Feed Service development environment..."

# Step 1: Check prerequisites
echo "📋 Checking prerequisites..."

if ! command -v nix-shell &> /dev/null; then
    echo "❌ Nix is not installed. Please install Nix first:"
    echo "   curl -L https://nixos.org/nix/install | sh"
    exit 1
fi

if ! command -v psql &> /dev/null; then
    echo "⚠️  PostgreSQL client not found. Will use SQLite for development."
    USE_SQLITE=true
else
    USE_SQLITE=false
fi

# Step 2: Enter Nix environment and install additional dependencies
echo "📦 Setting up Python environment..."
nix-shell --run "
    echo '🐍 Installing additional Python packages...'
    pip install aiosqlite httpx pytest-asyncio
    echo '✅ Python packages installed'
"

# Step 3: Set up environment variables
echo "⚙️  Setting up environment variables..."
cat > .env << EOF
# Development Environment Configuration
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG
SECRET_KEY=dev-secret-key-change-in-production

# Database Configuration
DATABASE_URL=sqlite+aiosqlite:///./dev_database.db
TEST_DATABASE_URL=sqlite+aiosqlite:///:memory:

# Server Configuration
HOST=0.0.0.0
PORT=8000
RELOAD=true

# External APIs (optional for development)
NVD_API_KEY=your-nvd-api-key-here
NVD_API_BASE_URL=https://services.nvd.nist.gov/rest/json

# Security
ACCESS_TOKEN_EXPIRE_MINUTES=60
ALGORITHM=HS256

# Features (disabled for development)
ENABLE_NVD_SYNC=false
ENABLE_BACKGROUND_TASKS=false
EOF

echo "✅ Environment variables configured in .env"

# Step 4: Set up database
echo "🗄️  Setting up database..."
if [ "$USE_SQLITE" = true ]; then
    echo "Using SQLite for development database"
    nix-shell --run "
        echo '📊 Running database migrations...'
        alembic upgrade head
        echo '✅ Database migrations completed'
    "
else
    echo "Setting up PostgreSQL database..."
    # Create development database if it doesn't exist
    createdb cve_feed_dev 2>/dev/null || echo "Database cve_feed_dev already exists"
    createdb cve_feed_test 2>/dev/null || echo "Database cve_feed_test already exists"
    
    # Update .env for PostgreSQL
    cat > .env << EOF
# Development Environment Configuration
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG
SECRET_KEY=dev-secret-key-change-in-production

# Database Configuration (PostgreSQL)
DATABASE_URL=postgresql+asyncpg://\$USER@localhost/cve_feed_dev
TEST_DATABASE_URL=postgresql+asyncpg://\$USER@localhost/cve_feed_test

# Server Configuration
HOST=0.0.0.0
PORT=8000
RELOAD=true

# External APIs (optional for development)
NVD_API_KEY=your-nvd-api-key-here
NVD_API_BASE_URL=https://services.nvd.nist.gov/rest/json

# Security
ACCESS_TOKEN_EXPIRE_MINUTES=60
ALGORITHM=HS256

# Features (disabled for development)
ENABLE_NVD_SYNC=false
ENABLE_BACKGROUND_TASKS=false
EOF
    
    nix-shell --run "
        echo '📊 Running database migrations...'
        alembic upgrade head
        echo '✅ Database migrations completed'
    "
fi

# Step 5: Run tests to verify setup
echo "🧪 Running tests to verify setup..."
nix-shell --run "
    echo '🔍 Running basic functionality tests...'
    python -m pytest tests/unit/test_basic_functionality.py -v
    echo '✅ Basic tests completed'
"

# Step 6: Start development server (optional)
echo "🌐 Development environment setup complete!"
echo ""
echo "📋 Next steps:"
echo "   1. Enter the development environment: nix-shell"
echo "   2. Start the development server: uvicorn src.cve_feed_service.main:app --reload"
echo "   3. Open API docs: http://localhost:8000/docs"
echo "   4. View documentation: open docs/_build/html/index.html"
echo ""
echo "🧪 Run tests with: python -m pytest"
echo "📚 Build docs with: cd docs && sphinx-build -b html . _build/html"
echo ""

# Offer to start the server
read -p "🚀 Would you like to start the development server now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🌐 Starting development server..."
    nix-shell --run "uvicorn src.cve_feed_service.main:app --reload --host 0.0.0.0 --port 8000"
fi
